<?php

'yii2-only`;

	namespace app\controllers;
	use Yang;

`/yii2-only';


#yii2: done

class InstallerController extends Controller
{
	public $layout = '//layouts/main';
	
	public function rmdir_recursive($dir)
	{
		foreach (scandir($dir) as $file)
		{
			if ('.' === $file || '..' === $file)
			{
				continue;
			}
			
			/*if (is_link("$dir/$file"))
			{
				rmdir("$dir/$file");
			}
			else */if (is_dir("$dir/$file"))
			{
				$this->rmdir_recursive("$dir/$file");
			}
			else
			{
				unlink("$dir/$file");
			}
		}
		rmdir($dir);
	}
	
	public function copy_recursive($src,$dst) { 
		$dir = opendir($src); 
		@mkdir($dst); 
		while(false !== ( $file = readdir($dir)) ) { 
			if (( $file != '.' ) && ( $file != '..' )) { 
				if ( is_dir($src . '/' . $file) ) { 
					$this->copy_recursive($src . '/' . $file,$dst . '/' . $file); 
				} 
				else { 
					copy($src . '/' . $file,$dst . '/' . $file); 
				} 
			} 
		} 
		closedir($dir); 
	}
	
	public function actionUpgr()
	{
		$this->upgrade("/var/www/modules/ttwa-wwm","/var/www/modules/ttwa-build/controllers/staging/tmp/ttwa-wwm.zip",false);
	}
	
	private function upgrade($install_dir = "/var/www/wwm.stage-ttwa.login.hu",$source = '/var/www/modules/ttwa-build/controllers/staging/tmp/ttwa-base.zip',$backupRestore = true)
	{
		$row_id = isset($_POST["row_id"])?$_POST["row_id"]:null;

//		$dir = dirname(__FILE__);
//		$tmpdir = $dir."/tmp";
//		
//		$zip = new ZipArchive();
//		$filename = $tmpdir."/test112.zip";
//		
//		if ($zip->open($filename, ZipArchive::CREATE)!==TRUE) {
//			exit("cannot open <$filename>\n");
//		}
//
//		$zip->addFromString("testfilephp.txt" . time(), "#1 This is a test string added as testfilephp.txt.\n");
//		$zip->addFromString("testfilephp2.txt" . time(), "#2 This is a test string added as testfilephp2.txt.\n");
//		echo "numfiles: " . $zip->numFiles . "\n";
//		echo "status:" . $zip->status . "\n";
//		$zip->close();
		
		//(!is_writable('../runtime'))
		
//		if (isset($row_id) && !empty($row_id))
		{
//			$build = Build::model()->findByPk($row_id);
//			$install_dir = $build->install_dir;
			
			////////////////////////////////////////////////////////////////////
			// BACKUP -->
			if ($backupRestore)
			{
				if (file_exists("$install_dir/inst") && is_dir("$install_dir/inst"))
				{
					$this->rmdir_recursive("$install_dir/inst");
				}
				mkdir("$install_dir/inst");

				$install_backup_list_file = "$install_dir/install.backup";

				$i = 0;
				$lines = array();

				if (file_exists($install_backup_list_file))
				{
					$backup_handle = fopen($install_backup_list_file, "r");

					while ($line = fgets($backup_handle))
					{
						$to_backup = str_replace(array("\r\n","\n"),"",$line);
						$backup_dir = "$install_dir/inst/$to_backup";
						mkdir($backup_dir,0777,true);

						if (file_exists("$install_dir/$to_backup") && is_dir("$install_dir/$to_backup"))
						{
							$this->copy_recursive("$install_dir/$to_backup","$install_dir/inst/$to_backup");
							$lines[$i] = $to_backup;
							$i++;
						}
					}

					fclose($backup_handle);
				}
			}
			// BACKUP <--
			////////////////////////////////////////////////////////////////////
			
			////////////////////////////////////////////////////////////////////
			// DELETE -->
			$list_handle = opendir("$install_dir");
			
			shell_exec("rm -rf $install_dir/webroot/ext");
			
			while ($entry = readdir($list_handle))
			{
				if ($entry !== "ttwa-base.zip" && $entry !== "." && $entry !== ".." && $entry !== "inst")
				{
					if (is_dir("$install_dir/$entry"))
					{
						$this->rmdir_recursive("$install_dir/$entry");
					}
					else
					{
						unlink("$install_dir/$entry");
					}
				}
			}
			// DELETE <--
			////////////////////////////////////////////////////////////////////
			
			////////////////////////////////////////////////////////////////////
			// UNZIP -->
			$zip = new ZipArchive();
			$zip_file = $source;
			
			$res = $zip->open($zip_file, ZIPARCHIVE::CHECKCONS);
			if (file_exists($zip_file) && $res === TRUE)
			{
				$extraction = $zip->extractTo($install_dir);
				if (!$extraction)
				{
					echo "extraction failed!";
				}
				else
				{
					echo "extraction success!";
				}
				$zip->close();
			}
			// UNZIP <--
			////////////////////////////////////////////////////////////////////
			
			////////////////////////////////////////////////////////////////////
			// RESTORE -->
			if ($backupRestore && file_exists("$install_dir/inst") && is_dir("$install_dir/inst"))
			{
				$this->copy_recursive("$install_dir/inst","$install_dir");
			}
			// RESTORE <--
			////////////////////////////////////////////////////////////////////
			
//			$build->build_date = date("Y-m-d H:i:s");
//			$build->save();
		}
	}
}

?>