<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\MyActiveForm;
	use app\models\Registration;
	use Yang;

`/yii2-only';


#yii2: done

class ReaderController extends Controller
{
	public function actionSaveRegistration() {
		$ok = true;
		
		$cardr = false;
		$hkr = false;
		$terminal_id = null;
		$datetime = null;
		$card = null;
		
		if (!empty(requestParam("cardr"))) {
			$cardr = (int)requestParam("cardr");
		}
		
		if (!empty(requestParam("hkr"))) {
			$hkr = (int)requestParam("hkr");
		}
		
		if (!$cardr && !$hkr) {
			$ok = false;
		}

		if (!empty(requestParam("terminal_id"))) {
			$terminal_id = requestParam("terminal_id");
		} else {
			$ok = false;
		}

		if (!empty(requestParam("datetime"))) {
			$datetime = requestParam("datetime");
		} else {
			$ok = false;
		}

		if ($cardr) {
			if (!empty(requestParam("card"))) {
				$card = requestParam("card");
			} else {
				$ok = false;
			}
		}
		
		if (!$ok) {
			return false;
		}

		$dtArr = explode("_", $datetime);
		
		if (count($dtArr) !== 2) {
			return false;
		}
		
		$datetime = $dtArr[0]." ".str_replace("-", ":", $dtArr[1]);
		
		$results = [
			'saved' => 0,
		];
		
		if ($cardr) {
			$r = new Registration;
			$r->terminal_id = $terminal_id;
			$r->card = $card;
			$r->time = $datetime;
			$r->event_type_id = "NMB";
			$r->status = 2;
			$r->created_by = "ReaderController";
			$r->create_on = date("Y-m-d H:i:s");
			
			$valid = $r->validate();

			if ($valid) {
				$r->save();

				$results = [
					'saved' => 1,
				];
			} else {
				$error = MyActiveForm::_validate($r);

				$results = [
					'saved' => 0,
				];
			}
		}
		
		echo json_encode($results);
	}
}