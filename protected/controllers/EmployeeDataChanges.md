# Dolgozói adat változtatások megjelenítésére szolgáló kimutatás


### Működés
A dolgozó kezelésben történt változásokat a Grid2 bementi a main_log táblába JSON-l.
Ezeket az adatokat adja vissza, illetve amennyiben új dolgozó jött létre megjeleníti azt is (legkisebb valid_to és created_on az adott intervallumba esik).
Az adatok megjelenítésénél nagyon sok mindent figyelembe vesz az inkozisztencia miatt.
Oda figyel az app_lookup alapú értékekre és azt jeleníti meg.
Oda figyel, hogy a csoport 1-2-3 táblanevek id 1-2-3 nélk<PERSON>l vannak.
Amennyiben id alapú a változás, akkor ellenőrzi, hogy tartozik-e hozzá külön tábla a megnevezéssel és azt adja vissza.
Oda figy<PERSON>, hogy szabadságok fülön a megnevezések azok külön dictionaryből jönnek.
Amennyiben nincs külön megnevezése az értéknek, úgy szimplán az értéket jeleníti meg.
Osztályváltozóba felvannak véve dictionary felülírások szintén az inkozisztencia miatt (várt dict_idből cserél dict_id-re, ha szerepel a tömbben).
Szintén oda figyel a csoportosítás fül eltérő működésére, ahol a változott érték ugye szintén egy oszlop változó: group_id és group_value párost menti a main_logba, ezek segítségével olvassa ki.
Tekintve a Grid2-be az actionSave kétszer kerül meghívásra, így sajnos duplázódnak kis idő eltéréssel a változtatások sorai, így egy függvény megadható másodperc intervallummal ezt a group_byozáson felül szűri ki.
A dinamikus tabok adatok nem mentődnek main_logba, így nem is kerül megjelenítésre, de nem akasztja el a kimutatást.
Szűrők: Alap dolgozó és időintervallum tól-ig szűrés, jogosultság pedig az összesítőlapos.


### Funkciók


#### `__construct()`
Paraméter | Type | Leírás
---: | --- | ---
*@return* |  | 

Grid2 szólőosztály konstruktor. LA Grid és LA Grid Light engedélyezve.

-----

#### `G2BInit()`
Paraméter | Type | Leírás
---: | --- | ---
*@return* | | 

G2B inicializálás. Exel export, keresés engedélyezve.

-----

#### `columns()`
Paraméter | Type | Leírás
---: | --- | ---
*@return* | array | oszlopok tömbje

Inicializálja a Grid oszlopait.

-----

#### `attributeLabels()`
Paraméter | Type | Leírás
---: | --- | ---
*@return* | array | oszlop feliratok tömbje

Inicializálja a Grid2 oszlopok elnevezését (dictionary táblából).

------

#### `search()`
Paraméter | Type | Leírás
---: | --- | ---
*@return* | array | szűrök tömbje

Inicializálja a Grid2 szűrőjét. Esetünkben az összesítőlapos szűrőket használja, tól-ig és dolgozói alapszűrő.

-----

#### `getNewEmployees($gargSQL, $filter)`
Paraméter | Type | Leírás
---: | --- | ---
*$gargSQL* | string | get approver related group SQL-je
*$filter* | array | szűrőből kapott értékek
*@return* | array | SQL eredménye

Végig megy az employee táblán, és ahol az intervallumba esik a created_on mező, és a történetiség a dolgozónak a legkorábbi visszaadja az adatokat a kimutatáshoz új dolgozóként, figyelve a jogosultságokra és a szűrőre.

-----

#### `getAppLookUpValue($param, $lang, $changedData)`
Paraméter | Type | Leírás
---: | --- | ---
*$param* | string | old vagy new, attól függően az új adatról vagy a módosítottról van szó
*$lang* | string | a felhasználó beállított nyelvének kódja (pl.: hu, en)
*$changedData* | array | az adatmódosítás tömbje
*@return* | string | maga a változott adat

A változott adathoz amennyiben tartozik app_lookup elnevezés visszakeresi a dictionary táblából és azt az értéket adja vissza, amennyiben nem létezik az eredetit, illetve dokumentum feltöltés esetén üres stringet.

-----

#### `reformatSQL($changedData)`
Paraméter | Type | Leírás
---: | --- | ---
*$changedData* | array | adat módosítások tömbje
*@return* | array | SQL helyes select, join és where utasításainak stringjéből keletkezett tömb

A változott adatnál megnézi, hogy _id végződése-e van-e amennyiben nem akkor a getAppLookUpValue függvényt meghívja, adja vissza a korábbi és a változott értékeket.
Amennyiben létezik külön tábla a megnevezéshez, úgy megvizsgálja, hogy szerepel-e benne valid_from és valid_to, és ehhez igazítja a JOIN feltételét.
Amennyiben Csoport 1-2-3 úgy id mellett az 1-2-3-t is levágja a tábla névből. Amennyiben szabadság típus, akkor pedig dictionary-ből szedi a megnevezést.
Egyéb normál esetben a tábla _name értéke, illetve még a Csoportosítás fül esetén van megoldva hogy a group_id alapján JOINolja be a táblát.
Végezetül visszaadja az SQL-hez a select, a join és a where string ehhez kapcsolódó részét.

-----

#### `getMainLogData($filter)`
Paraméter | Type | Leírás
---: | --- | ---
*$filter* | array | szűrő mező értékei
*@return* | array | SQL által generált eredmény halmaz

Lekérdezi a main_log táblából a grid2 adatváltoztatásokat, ahol a log_message GRID_DATA_CHANGE és json_decolva visszaadja asszociatív tömbként a paramétereket.

-----

#### `getEmployeeChanges($gargSQL, $filter)`
Paraméter | Type | Leírás
---: | --- | ---
*$gargSQL* | string | jóváhagyási SQL
*$filter* | array | szűrő mező értékei
*@return* | array | SQL által generált eredmény halmaz

A main_logból visszaadott adatokat rakja össze a kimutatáshoz kívánt formátumba. Amennyiben valamilyen employeeTab-n történt változtatás, úgy összerakja a dolgozóhoz az SQL-t, és helyes formátumba nyeri ki a változtatott oszlopot, korábbi adatot ás új adatot.
Figyel a szűrőben beállított értékekre és a jogosultságokra.

-----

#### `dataArray($gridID, $filter, $isExport)`
Paraméter | Type | Leírás
---: | --- | ---
*$gridID* | string | Grid2 azonosító (általános)
*$filter* | array | szűrő mező értékei
*$isExport* | boolean | exportnál használja a függvényt az adatgeneráláshoz vagy normál betöltésnél
*@return* | array | A Grid adathalmaza

Az új dolgozó felvétek és dolgozó adat módosítások adatait állítja elő és tölti be a Grid2-be.

-----

#### `dataDiffers($maxDifference, $actualArray, $compareArray)`
Paraméter | Type | Leírás
---: | --- | ---
*$maxDifference* | int | maximum eltérés secundumban
*$actualArray* | array | az eredmény jelenlegi adathalmaza
*$compareArray* | array | az új tömb amit az aktuális eredményhez viszonyítunk
*@return* | boolean | az új tömböt belerakjuk-e az eredményhalmazba vagy sem

A Grid2 hibája miatt, hogy kétszer fut le az actionSave, szükség van erre a függvényre, hogy kiszűrje a duplikátumokat. Az egyszerű SQL GROUP BY esetén amennyiben a created_on mező változott, hozni fogja másodperc miatt a duplikációt.
A függvénynek megadható mi legyen az a maximum másodperc különbség ami alapján már új módosításnak tekintjük. Amennyiben a dolgozó azonosító és változott oszlop megegyezik akkor vizsgálja a két változtatás közötti idő eltérést.

-----

#### `filters()`
Paraméter | Type | Leírás
---: | --- | ---
*@return* | array | 

Biztonsági függvény #1

-----

#### `accessRules()`
Paraméter | Type | Leírás
---: | --- | ---
*@return* | array | 

Biztonsági függvény #2