<?php

'yii2-only`;

	namespace app\controllers;
	use app\models\SqlVersion;
	use Yang;

`/yii2-only';


#yii2: done

class TestingController extends Controller
{
	private function isTestEnv() {
		return (strpos($_SERVER["SERVER_NAME"], '.hu.login') !== false || strpos($_SERVER["SERVER_NAME"], '.dev') !== false || strpos($_SERVER["SERVER_NAME"], '.test') !== false);
	}

	public function actionInstallFixture() {
		$this->layout = "ajax";

		$fixtureFileLoc = requestParam('fixtureFile');

		$srv = $_SERVER["SERVER_NAME"];

		$resp = [
			'status' => 'skip',
		];

		if ($this->isTestEnv() && file_exists($fixtureFileLoc) && is_readable($fixtureFileLoc)) {
			$conn = Yii::app()->db;
			$conn->setAttribute(PDO::ATTR_AUTOCOMMIT, 0);

			$failed = false;
			$transaction = $conn->beginTransaction();

			$fixtureFile = fopen($fixtureFileLoc, "r");

			$sql = '';

			while ($line = fgets($fixtureFile)) {
				if (!empty($line) && substr($line, 0, 2) !== '--') {
					$sql .= $line;
				}
			}

			try {
				$exec = $conn->createCommand($sql)->execute();
			} catch (Exception $ex) {
				$failed = true;
			}

			if (!$failed) {
				$transaction->commit();

				$resp = [
					'status' => 'success',
				];
			} else {
				$transaction->rollback();

				$resp = [
					'status' => 'failed',
				];
			}

			fclose($fixtureFile);
		}

		echo json_encode($resp);
	}

	public function actionClearAppSettingsSession() {
		if ($this->isTestEnv() && isset($_SESSION["tiptime"]["settings"])) {
			unset($_SESSION["tiptime"]["settings"]);
		}
	}

	public function actionInstallTestDB() {
		if (!$this->isTestEnv()) {
			return false;
		}

		set_time_limit(60*60);

		$modulesToInstall = requestParam('modulesToInstall');

		if (!empty($modulesToInstall)) {
			$modulesToInstall = "base-$modulesToInstall";
		} else {
			$modulesToInstall = "base";
		}

		$dbName = "test_$modulesToInstall";

		$getDbName = requestParam('dbName');

		if (!empty($getDbName)) {
			$dbName = $getDbName;
		}

		if (!empty($modulesToInstall)) {
			$modulesToInstall = explode("-", $modulesToInstall); // arr
		} else {
			$modulesToInstall = [];
		}

		$conn = new mysqli(Yang::getParam('dbHost'), Yang::getParam('dbUser'), Yang::getParam('dbPass'));

		$dbCheck = $conn->query("SHOW DATABASES LIKE '$dbName'"); // multi_query

		while ($row = $dbCheck->fetch_row()) {
			return false;
		}

		$basePath = Yang::getBasePath();

		$SQL = "";

		$SQL .= "
			SET NAMES utf8;

			DROP DATABASE IF EXISTS `$dbName`;
			CREATE DATABASE IF NOT EXISTS `$dbName` DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci;
			USE `$dbName`;
		";

		$file_STRUCTURE_init = "$basePath/data/db/STRUCTURE_init.sql";
		if (!file_exists($file_STRUCTURE_init) || !is_readable($file_STRUCTURE_init)) {
			return false;
		}
		$fp_STRUCTURE_init = fopen($file_STRUCTURE_init, 'r');
		$fr_STRUCTURE_init = fread($fp_STRUCTURE_init, filesize($file_STRUCTURE_init));
		$SQL .= $fr_STRUCTURE_init;

		foreach ($modulesToInstall as $module) {
			if ($module === "base") {
				$moduleDir = "$basePath/data/db";
			} else {
				$moduleDir = "$basePath/data/db/Modules/$module";
			}

			if (!file_exists($moduleDir) || !is_readable($moduleDir)) {
				return false;
			}

			$moduleFilesOrder = [];

			foreach(scandir($moduleDir) as $file) {
				if ($file !== "." && $file !== ".." && strpos($file, 'DB_ch') !== false && strpos($file, '~') === false) {
					$version = str_replace(array("DB_ch_",".sql"),array("",""),$file);

					$versionExploded = explode(".", $version);

					if (isset($versionExploded[1]) && $versionExploded[1] < 10) {
						$version = $versionExploded[0]. "." . "0" . $versionExploded[1];
					}

					$moduleFilesOrder[] = ["file" => $file, "order" => $version,];
				}
			}

			$sorted = usort($moduleFilesOrder, function($a, $b) {
				return $a["order"] > $b["order"];
			});

			foreach ($moduleFilesOrder as $file) {
				$file = $file["file"];

//				if ($file !== "." && $file !== ".." && strpos($file, 'DB_ch') !== false && strpos($file, '~') === false) {
					echo "$module: $moduleDir/$file<br/>";

					$file_DB_ch = "$moduleDir/$file";
					if (!file_exists($file_DB_ch) || !is_readable($file_DB_ch)) {
						return false;
					}
					$fp_DB_ch = fopen($file_DB_ch, 'r');
					$fr_DB_ch = fread($fp_DB_ch, filesize($file_DB_ch));
					$SQL .= $fr_DB_ch;
//				}
			}
		}

		//$SQLlines = explode(PHP_EOL, $SQL);
		$SQLlines = preg_split('/\r\n|\r|\n/', $SQL);

		$delim = ";";

		$SQLExec = "";
		$hasError = false;
		foreach ($SQLlines as $SQLline) {
			if (!empty($SQLline) && substr($SQLline, 0, 2) !== '--') {
				if (strpos($SQLline, "DELIMITER") !== false) {
					$delim = str_replace(array("DELIMITER"," ","\r\n","\n"), "", $SQLline);

					$SQLExec = "";
				} else {
					$commentPos = strrpos($SQLline, "--");

					if ($commentPos !== false && strpos($SQLline, "'", $commentPos) === false && strpos($SQLline, "\"", $commentPos) === false && strpos($SQLline, "`", $commentPos) === false) {
						$SQLline = substr($SQLline, 0, $commentPos);
					}

					$SQLExec .= $SQLline." ";
				}

				$delimPos = strrpos($SQLline, $delim);

				if ($delimPos !== false && strpos($SQLline, "'", $delimPos) === false && strpos($SQLline, "\"", $delimPos) === false && strpos($SQLline, "`", $delimPos) === false && strpos($SQLline, "DELIMITER") === false) {
					if ($delim !== ";") {
						$SQLExec = str_replace($delim, "", $SQLExec);
					}

					if (!$conn->query($SQLExec)) {
						$hasError = true;
						echo "<br/>";
						echo "<br/>";
						echo "<span style=\"color:red;\">$SQLExec</span><br/>";
						echo $conn->error."<br/>";
					} else if ($hasError) {
						echo "<br/>";
						echo "<br/>";
						echo "$SQLExec<br/>";
					}

					$SQLExec = "";
				}
			}
		}
	}

	public function actionCheckModuleExists() {
		$module = requestParam('module');

		$v = new SqlVersion;
		$c = new CDbCriteria;
		$c->condition = "`module` LIKE '$module'";
		$r = $v->find($c);

		if ($r) {
			$resp = [
				"status" => "found",
			];
		} else {
			$resp = [
				"status" => "notfound",
			];
		}

		echo json_encode($resp);
	}
}