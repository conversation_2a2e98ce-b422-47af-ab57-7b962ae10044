<?php

'yii2-only`;

	namespace app\controllers;
	use Yang;

`/yii2-only';


#yii2: done

ini_set("display_errors", 1);

class LdapTestController extends Controller
{
	public function actionIndex() {
		$this->layout = 'empty';
		
		if (!function_exists("ldap_connect")) {
			return false;
		}
		
		$ldapConnection = ldap_connect("ldap://10.85.101.102", 389) or die('Could not connect to LDAP server.');
		ldap_set_option($ldapConnection, LDAP_OPT_PROTOCOL_VERSION, 3);
		ldap_set_option($ldapConnection, LDAP_OPT_REFERRALS, 0);
		
//		--------------------------------------------
		
		$dnString = '';
		echo "dnString:: ".$dnString;
		
		$search = ldap_read($ldapConnection, $dnString, "objectclass=*", array('*', 'subschemasubentry'));
		$entries = ldap_get_entries($ldapConnection, $search);
		$schemadn = $entries[0]["subschemasubentry"][0];

		print "Searching ". $schemadn . "<br/>";
		
		$entry = ldap_first_entry($ldapConnection, $search);

		$attrs = ldap_get_attributes($ldapConnection, $entry);
		
//		var_dump($attrs);
		
//		--------------------------------------------
		
		$user = 'obohu\teszt';
		$pass = 'Km3ZZ1iM';
		
		if ($bind = @ldap_bind($ldapConnection, $user, $pass)) {
			echo "AUTH OK!";
			
			$dnString = "OU=obo,DC=obohu,DC=local";
			
			$filter = 'sAMAccountName=macsek';

			$result = ldap_search($ldapConnection, $dnString, $filter);
			
			$data = ldap_get_entries($ldapConnection, $result);
			
			echo '<h1>Show me the users</h1>'/* . $data["count"]*/;
			for ($i = 0; $i < $data["count"]; $i++) {
				/*echo "dn is: ". $data[$i]["dn"] ."<br />";*/
				echo "User: ". $data[$i]["samaccountname"][0] ."<br />";
				echo "User: ". $data[$i]["cn"][0] ."<br />";
				if(isset($data[$i]["mail"][0])) {
					echo "Email: ". $data[$i]["mail"][0] ."<br /><br />";
				} else {
					echo "Email: None<br /><br />";
				}

				//print_r($data[$i]);
			}
			
//			echo $info[0]["mail"][0];
			
//			$filter = "(sAMAccountName=".$user.")";
//			$result = ldap_search($ldapConnection, $schemadn, $filter) or exit("Unable to search LDAP server");
//			$info = ldap_get_entries($ldapConnection, $result);
//			
//			for ($i = 0; $i < $info["count"]; $i++) {
//				if ($info['count'] > 1)
//					break;
//				echo "<p>You are accessing <strong> ". $info[$i]["sn"][0] .", " . $info[$i]["givenname"][0] ."</strong><br /> (" . $info[$i]["samaccountname"][0] .")</p>\n";
//				
//				echo '<pre>';
//				var_dump($info);
//				echo '</pre>';
//				
//				$userDn = $info[$i]["distinguishedname"][0]; 
//			}

			// check groups
//			foreach($entries[0]['memberof'] as $grps) {
//				// is manager, break loop
//				if(strpos($grps, $ldap_manager_group)) { $access = 2; break; }
//
//				// is user
//				if(strpos($grps, $ldap_user_group)) $access = 1;
//			}
//
//			if($access != 0) {
//				echo "AUTH OK!";
//				// establish session variables
////				$_SESSION['user'] = $user;
////				$_SESSION['access'] = $access;
////				return true;
//			} else {
//				echo "AUTH Failed!";
//				// user has no rights
////				return false;
//			}
		} else {
			echo "AUTH Failed!";
		}
		
//		$dnString = '';
//		echo "dnString:: ".$dnString;
//		
//		$search = ldap_read($ldapConnection, $dnString, "objectclass=*", array('*', 'subschemasubentry'));
//		$entries = ldap_get_entries($ldapConnection, $search);
//
//		print "Searching ". $schemadn . "<br/>";
//		
//		$count = $entries["count"];
////
//		print "<br/><br/><br/>Printing all attribute types<br/>";
//		for ($i = 0; $i < $count; $i++)
//		   print $entries[$i]["cn"] . "<br/>";
		
//		--------------------------------------------
		
//		$schsearch = ldap_read($ldapConnection, $schemadn, "objectClass=subSchema", array('objectclasses', 'attributetypes'));
//		$schentries = ldap_get_entries($ldapConnection, $schsearch);
//
//		$count = $schentries[0]["attributetypes"]["count"];
//
//		print "Printing all attribute types <br/>";
//		for ($i = 0; $i < $count; $i++)
//		   print $schentries[0]["attributetypes"][$i] . "<br/>";


//		$count = $schentries[0]["objectclasses"]["count"];
//
//		print "Printing all objectclasses <br/>";
//		for ($i = 0; $i < $count; $i++)
//			print $schentries[0]["objectclasses"][$i] . "<br/>";
		
//		$user = "root";
//		$attributes = array('dn');
//		$result = ldap_search($ldapConnection, $dnString,
//        "(samaccountname={$user})", $attributes);
//		if ($result === FALSE) { return ''; }
//		$entries = ldap_get_entries($ldapConnection, $result);
//		if ($entries['count']>0) { return $entries[0]['dn']; }
//		else { return ''; };
		
//		$userdn = getDN($ldapConnection, $user, $dnString);
//		echo $userdn."<br/>";
	}
}