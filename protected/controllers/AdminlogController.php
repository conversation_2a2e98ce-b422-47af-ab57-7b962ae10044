<?php
class AdminLogController extends GridController
{
	public function __construct()
	{
		parent::__construct("adminlog");
		$this->setModelName("AdminLog");
		parent::setTitle(Dict::getValue("page_title_admin_log"));
		parent::exportSettings(Dict::getValue("export_file_admin_log"));
		parent::selectConditions("(`status` = ".Status::PUBLISHED.")");
	}

	public function columns()
	{
		return array(
			'action'				=> array('export'=> true , 'col_type'=>'ed', 'width'=>'*'),
			'description'			=> array('export'=> true , 'col_type'=>'ed', 'width'=>'*'),
			'created_by'			=> array('export'=> true , 'col_type'=>'ro', 'width'=>'*'),
			'created_on'			=> array('export'=> true , 'col_type'=>'ro', 'width'=>'*'),
		);
	}
}
?>
