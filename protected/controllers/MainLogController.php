<?php #yii2: done

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use app\models\Status;
	use Yang;

`/yii2-only';


#yii2: done

class MainLogController extends Grid2Controller
{
	public function __construct() {
		parent::__construct("mainLog");
		parent::enableLAGrid();
		parent::enableLAGridLight();
	}

	protected function G2BInit() {
		parent::setControllerPageTitleId("page_title_main_log");
		
		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);
		$this->LAGridDB->enableArrMode();
		$this->LAGridDB->setPrimaryKey("primary_key");
		parent::setExportFileName(Dict::getValue("page_title_main_log"));
		parent::setGridProperty("splitColumnEnabled", true,	 "dhtmlxGrid");
		parent::setGridProperty("splitColumn", 1,	 "dhtmlxGrid");
		parent::G2BInit();
	}
	
	public function attributeLabels() {
		return array(
			'ipv4_address'			=> 'IP cím',
			'event_time'			=> Dict::getValue('time'),
			'username'				=> Dict::getValue('username'),
			'page_title'			=> Dict::getValue('page_title'),
			'controller_id'			=> Dict::getValue('controller'),
			'action_id'				=> Dict::getValue('operation'),
			'log_message'			=> Dict::getValue('message'),
			'params'				=> 'Paraméterek',
			'_'						=> '',
		);
	}

	public function columns() {
	    $retArr = [];

	    $retArr["ipv4_address"] = ['grid' => true, 'export'=> true, 'col_type'=>'ro', 'width'=>'150',];
	    $retArr["event_time"] = ['grid' => true, 'export'=> true, 'col_type'=>'ro', 'width'=>'150',];
	    $retArr["username"] = ['grid' => true, 'export'=> true, 'col_type'=>'ro', 'width'=>'150',];
	    $retArr["page_title"] = ['grid' => true, 'export'=> true, 'col_type'=>'ro', 'width'=>'150',];
	    $retArr["controller_id"] = ['grid' => true, 'export'=> true, 'col_type'=>'ro', 'width'=>'150',];
	    $retArr["action_id"] = ['grid' => true, 'export'=> true, 'col_type'=>'ro', 'width'=>'150',];
	    $retArr["log_message"] = ['grid' => true, 'export'=> true, 'col_type'=>'ro', 'width'=>'150',];
	    $retArr["_"] = ['grid' => true, 'export'=> false, 'col_type'=>'ro', 'width'=>'*',];
	    return $retArr;
	}

	public function search() {
		return array(
			'date_from'			=> ['col_type' => 'ed', 'dPicker' => true, 'width' => '*', 'label_text' => Dict::getValue("date_from"), 'default_value' => date('Y-m-d'),],
			'date_to'			=> ['col_type' => 'ed', 'dPicker' => true, 'width' => '*', 'label_text' => Dict::getValue("date_to"), 'default_value' => date('Y-m-d'),],
			'submit'		=> ['col_type' => 'searchBarReloadGrid', 'width' => '*', 'label_text' => '',],
		);
	}

	protected function dataArray($gridID, $filter, $isExport = false) {
        $regenerate = false;
		foreach ($this->fetchParams as $var => $val) {
			${$var} = $val;
		}

		$results = [];

		$SQL_base = "
			SELECT
				main_log.`row_id` AS `primary_key`,

				main_log.`ipv4_address`,
				main_log.`event_time`,
				user.`username`,
				main_log.`page_title`,
				main_log.`controller_id`,
				main_log.`action_id`,

				main_log.`log_message`,
				main_log.`log_params`,
				main_log.`params`
			FROM
				`main_log`
			LEFT JOIN
				`user` ON
					user.`user_id` = main_log.`user_id`
					AND user.`status` = ".Status::PUBLISHED."
					AND main_log.`event_time` BETWEEN user.`valid_from` AND IFNULL(user.`valid_to`, '".App::getSetting("defaultEnd")."')
			WHERE
				main_log.`event_time` BETWEEN '".$filter['date_from']."' AND '".$filter['date_to']."'
		";

		if (!App::hasRight(null, "su")) {
			$SQL_base .= "
				AND user.`username` NOT LIKE 'root'
			";
		}

		$SQL_base .= "
			ORDER BY
				`event_time` DESC
			/*LIMIT
				100*/
		";

		if ($regenerate) {
			$results = $this->LAGridDB->getConnection()->createCommand($SQL_base)->queryAll();

			for ($i = 0; $i < count($results); $i++) {
				$results[$i]['page_title'] = Dict::getValue($results[$i]['page_title']);

				$log_params = [];
				$log_params = CJSON::decode($results[$i]['log_params']);

				$params = [];
				$params = CJSON::decode($results[$i]['params']);

				$params = Yang::arrayMerge( $log_params , $params );
				$results[$i]['log_message'] = Dict::getValue($results[$i]['log_message'], $params);
			}
		}
		return $results;
	}
}