<?php

class ApproveController extends Grid2ApproveController
{
	protected string $approveDefaultValidFromMinusMonth;
	protected string $approveDefaultValidToPlusMonth;
    protected string $defaultValidFrom;
	protected string $defaultValidTo;

	protected $userIdToSwitch;

	public function __construct() {
		parent::__construct("approve");
		$this->maxDays 							 = App::getSetting("approve_max_days");
		$this->approveDefaultValidFromMinusMonth = App::getSetting("approveDefaultValidFromMinusMonth");
		$this->approveDefaultValidToPlusMonth 	 = App::getSetting("approveDefaultValidToPlusMonth");
		$this->defaultValidFrom					 = App::getSetting("approve_default_valid_from");
		$this->defaultValidTo					 = App::getSetting("approve_default_valid_to");
		$this->userIdToSwitch					 = isset($_SESSION["tiptime"]["userIdToSwitch"]) ? $_SESSION["tiptime"]["userIdToSwitch"] : userID();
	}
}