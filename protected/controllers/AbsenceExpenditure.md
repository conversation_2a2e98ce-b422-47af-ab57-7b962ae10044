# Szabadság kiad<PERSON> k<PERSON>, adott napra mindenkinek akinek adott napon beosztása van.

## Működés
A kereső részen 3 adatot kell megadni. Minden adat kötelező, a dátum validálása is megtörténik.
A program a beérkező adatok ellenőrzése után lekérdezi a dolgozókat, majd ezeket egyesével feldolgozza. Egyszerre csak 1 dolgozóval foglalkozunk, és az ereményt egy tömbbe írjuk, amit később a GRID-be kirakunk.

Lekérdezzük hogy a dolgozónak van-e beosztás szerinti munkanapja az adott napon. Ez két táblából történik.
Ha nincs munkanapja, akkor ezt a elmentjük az eredmény tömbbe a dolgozó sorához, és lépünk a következő dolgozóra.
<PERSON> van munkanapja, akkor a következő ellenőrzés az elérhető szabadságok lekérdezése. (jelenleg csak az alapszabadságot figyeljük)
Ha nincs elég szabadsága, akkor ezt a elmentjük az eredmény tömbbe a dolgozó sorához, és lépünk a következő dolgozóra.
Ha van elég szabadsága, akkor ellenőrizzük, hogy az adott napra van-e már igényelt, vagy jóváhagyott szabadsága.
Ha van, akkor akkor ezt a elmentjük az eredmény tömbbe a dolgozó sorához, és lépünk a következő dolgozóra.
Ha nincs akkor kiadunk neki egy nap szabadságot. Beállítástól függően ezt el is fogadjuk, vagy kiküldjük a jóváhagyó emailt, vagy jóváhagyás nélkül emailt sem küldünk.
Az eredményt elmentjük az eredmény tömbbe a dolgozó sorához, és lépünk a következő dolgozóra.


### Paraméterek
1. Dátum (amelyik napra ki akarjuk adni a szabadságot)

2. Szabadság elfogadása
	* Igen (szabadságok egyből elfogadásra is kerülnek
	* Nem (jóváhagyó email nélkül) (a szabadságok nem lesznek jóváhagyva, és email sem kerül kiküldésre)
	* Nem (jóváhagyó email küldése) (a szabadságok nem lesznek jóváhagyva, de jóváhagyó email kerül kiküldésre)

3. Szimuláció futtatása
	* Igen (szimulált futás, minden lekérdezés valós, de a szabadság kiadás (insert), és jóváhagyó email küldése (beállítás alapján) nem történik meg
	* Nem (nem szimulált feldolgozás, az adatok beírásra kerülnek az adatbázisba, és a beállítás függvényében a jóváhagyó emailek is kiküldésre kerülnek)


### Osztályváltozók

#### `$date`  // keresőből jövő dátum

#### `$acceptance` // szabadság elfogadás kell-e

#### `$simulation` // szimulációban legyen-e futtatva

#### `$error_log` // hibák

#### `$table_row` // táblázat sora

#### `$result_array` // eredmény tömb amit a grid-nek adunk át

#### `$abs_status` // szabadság státusza (1 default, 2 ha jóvá is kell hagyni)

#### `$employee_data` // dolgozó adatait ebbe a tömbbe töltjük fel, hogy az osztályon belül bárhol elérhető legyen



### Funkciók

#### `G2BInit()`
Paraméter | Type | Leírás
:---: | --- | ---
*$gridID* |  | grid azonosító
*$filter* | array | szűrőből post-on érkező adatok
*@return* |  | 

Grid

-----

#### `setSQL()`
Paraméter | Type | Leírás
:---: | --- | ---
*$filter* | array | szűrőből post-on érkező adatok
*$gridID* | | grid azonosító
*@return* |  | 

Ebben futtatjuk a folyamatot, majd visszaadja a grid-nek az eredményt (jelenleg egy temp táblát)

-----

#### `columns()`
Paraméter | Type | Leírás
:---: | --- | ---
*@return* |  | 

A GRID oszlopainak beállítása

-----

#### `attributeLabels()`
Paraméter | Type | Leírás
:---: | --- | ---
*@return* |  | 

A GRID oszlop fejléceknek szövege

-----

#### `search()`
Paraméter | Type | Leírás
:---: | --- | ---
*@return* |  | 

A kereső form beállítása

-----

#### `validationPost()`
Paraméter | Type | Leírás
:---: | --- | ---
*@return* | string | 

A beérkező adatok ellenőrzése.

-----

#### `processEmployee()`
Paraméter | Type | Leírás
:---: | --- | ---
*$employees* |  | Lekérdezett dolgozók
*$filter* |  | Keresőből érkező adatok
*@return* |  | 

A Lekért dolgozók feldolgozása egyesével egy ciklusban. A cikluson belül, hívjuk meg a további feldolozó, és ellenöző függvényeket.

-----

#### `sendApprovalEmail()`
Paraméter | Type | Leírás
:---: | --- | ---
*$employee_absence_id* |  | Dolgozó szabadságának egyedi azonosítója
*@return* |  | 

A jóváhagyó email kiküldése.

-----

#### `getEmployeeAbsenceRowId()`
Paraméter | Type | Leírás
:---: | --- | ---
*$employee_absence_id* |  | Dolgozó szabadságának egyedi azonosítója
*@return* | mixed | 

Szabadság adatok lekérdezése $employee_absence_id alapján

-----

#### `getEmployees()`
Paraméter | Type | Leírás
:---: | --- | ---
*@return* | mixed | 

Minden olyan dolgozó lekérdezése akinek ki kell adni szabadnapot

-----

#### `checkWorkday()`
Paraméter | Type | Leírás
:---: | --- | ---
*@return* | bool | 

Munkanap ellenőrzése.

-----

#### `getWorkDayByWSBE()`
Paraméter | Type | Leírás
:---: | --- | ---
*@return* | mixed | 

Munkanap lekérdezése a work_schedule_by_employee táblából

-----

#### `getWorkDayByWSBU()`
Paraméter | Type | Leírás
:---: | --- | ---
*@return* | mixed | 

Munkanap lekérdezése a work_schedule_by_unit táblából

-----

#### `getWorkDayByWSU()`
Paraméter | Type | Leírás
:---: | --- | ---
*@return* | mixed | 

Munkanap lekérdezése a work_schedule_used táblából

-----

#### `checkFreeAbsence()`
Paraméter | Type | Leírás
:---: | --- | ---
*@return* | mixed | 

Dolgozó elérhető alap szabadságai lekérdezése

-----

#### `logEmployeeAbsStatus()`
Paraméter | Type | Leírás
:---: | --- | ---
*$abs_check_status* |  | Adott napi szabadság státusza
*@return* | mixed | 

Adott napon van-e már a dolgozónak igényelt, vagy jóváhagyott szabadsága.

-----

#### `insertAbsence()`
Paraméter | Type | Leírás
:---: | --- | ---
*@return* | mixed | 

Szabadság beírása.

-----

#### `checkABS()`
Paraméter | Type | Leírás
:---: | --- | ---
*@return* | mixed | 

Adott napra van-e már szabadsága a dolgozónak. Ha van azt loggoljuk is.

-----

#### `getEmployeeAbsByDate()`
Paraméter | Type | Leírás
:---: | --- | ---
*@return* | array | 

Adott napra van-e már a dolgozónak kiadott szabadsága

-----

#### `writeResultArray()`
Paraméter | Type | Leírás
:---: | --- | ---
*$msg* | string | Szöveg
*@return* | array | 

Eredmény tömb írása

-----

#### `multiInsert()`
Paraméter | Type | Leírás
:---: | --- | ---
*$data* |  | adat tömb amit insertálni akarunk
*@return* |  | 

A tömbben lévő adatokat beírja egy temp táblába

-----

#### `getSqlResult()`
Paraméter | Type | Leírás
:---: | --- | ---
*$sql* | string | Adott napi szabadság státusza
*$one_row* | bool | Adott napi szabadság státusza
*@return* | mixed | 

Adatbázis lekérséek futtatása, és eredmény visszaadása.

-----

#### `create_temp_table()`
Paraméter | Type | Leírás
:---: | --- | ---
*@return* |  | 

temp tábla létrehozása (mivel ebből olvassuk ki a grid adatokat)

-----