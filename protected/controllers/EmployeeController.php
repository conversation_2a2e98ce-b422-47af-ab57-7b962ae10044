<?php

/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> munkakör váltás folyamata:
 *		<PERSON> kell válaszatani egy dolgo<PERSON>, majd a "Munkakör váltás" gombra kattintva ki kell választani az új munkakört.
 *		Innen átvisz a "Munkakörhöz hiányzó kompetenciák" oldalra, közben session-be elmenti a szükséges adatokat (do<PERSON><PERSON><PERSON><PERSON> azonosító, tel<PERSON><PERSON> név, munkakör azonosító).
 *		Az orvosi kompetenci<PERSON>l (competency táblában medical nem NULL) a plusz jelre kattintva átvisz az "Orvosi vizsgálatok beosztása" felületre,
 *		ahol dupla kattintással lehet időpontot foglalni orvosi vizsgálatra.
 *		Megj.:
 *			1) Az "Orvosi vizsgálatok beosztása" felület még nincs összekötve a kompetenciákkal
 *			2) "Munkakörhöz hiányzó kompetenciák" felületen van egy plusz oszlop kikommentezve, Követkző tanfolyam,
 *				ami az adott kompetenciából legközelebb megrendezendő tanfolyam időpontját és egy hozzáadás gombot tartalmaz, de a gomb működése nincs megírva.
 *			3) Utóbbi felületre kell például egy elfogadás gomb, amivel jóváhagyhatja az áthelyezést.
 * Új oszlopok:
 *		reassignment (Áthelyezés): A fenti folyamatban hol tart a a dolgozó, DEMO!!!!
 *		position_matching (Munkaköri megfelelés): A dolgozó meglévő kompetenciái milyen mértékben felelnek meg a munkaköréhez szükséges kompetenciáinak
 */
class EmployeeController extends Grid2HistoryWizardController
{
    use EmployeeControllerActions;
	use EmployeeControllerAttributeLabels;
	use EmployeeControllerColumns;
	use EmployeeControllerData;
	use EmployeeControllerFakeData;
	use EmployeeControllerStatusButtons;
	use EmployeeControllerWizards;

	use EmployeeControllerLoga;
	use EmployeeControllerBaber;
	use EmployeeControllerLaurel;

	private $employeeDemoMode = false;

	public function __construct()
	{
		parent::__construct("employee");
		parent::enableLAGrid();
		parent::enableLAGridLight();
		$this->employeeDemoMode = (int)App::getSetting("employeeDemoMode") ? true : false;
	}

	protected function G2BInit() {

		parent::enableMultiGridMode();

		parent::setControllerPageTitleId("page_title_employee_management");

		$this->LAGridRights->overrideInitRights("paging",				true,		"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload",				true,		"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search",				true,		"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search_header",		true,		"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("select",				true,		"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("multi_select",			false,		"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("column_move",			true,		"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("col_sorting",			true,		"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload_sortings",		true,		"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("details",				false,		"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_xls",			false,		"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_xlsx",			true,		"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_pdf_node",		false,		"dhtmlxGrid");

		{// cache: gargSQL

			$acKey = "controllers/EmployeeController:G2BInit,gargSQL";
			if(AnyCache::has($acKey)) {
				$dta = AnyCache::get($acKey); // így akkor is működni fog, ha cache=OFF
			}else{
				$art = new ApproverRelatedGroup;
				$dta = $art->getApproverReleatedGroupSQL("Employee",["employeeManagement"]);
				AnyCache::set($acKey,$dta); #completeThis // gondolom a garg felsorolást valahová ki kéne szervezni, mert sok helyen kell majd
			}
			$gargSQL = $dta;

		}

		{// cache: SQLfilter
			$filter = requestParam('searchInput');
			$acKey = AnyCache::key("controllers/EmployeeController:G2BInit,GetPreDefinedFilter",[
				$this->getControllerID(),
				["cal.`date`"],
				['company'=> "employee",'payroll'=> "employee"],
				false,
				$filter
			]);
			if(AnyCache::has($acKey)) {
				$dta = AnyCache::get($acKey);
			}else{
				$gpf = new GetPreDefinedFilter(
					$this->getControllerID(),
					["cal.`date`"],
					['company'=> "employee",'payroll'=> "employee"],
					false,
					$filter
				);
				$dta = $gpf->getFilter();
				AnyCache::set($acKey,$dta); #completeThis // nem derült ki, milyen tábláktól függ
			}
			$SQLfilter = $dta;

		}

		$exprFullName = AnyCache::get("G2BInit.exprFullName");
		if(!$exprFullName) {
			$exprFullName = Employee::getParam('fullname', 'employee');
			AnyCache::set("G2BInit.exprFullName",$exprFullName,"-");
		}

		$SQL = '
			SELECT
				CONCAT(IFNULL(employee.`employee_id`,""),/*"_",IFNULL(employee_contract.`employee_contract_id`,""),*/"_",cal.`date`) AS row_id,
				employee.`employee_id`,
				employee.`company_id`,
				employee.`company_org_group1_id`,
				employee.`company_org_group2_id`,
				employee.`company_org_group3_id`,
				employee.`payroll_id`,
				employee.`emp_id`,
				employee.`title`,
				employee.`first_name`,
				employee.`last_name`,
				'.$exprFullName.' AS fullname,
				employee.`unit_id`,
				employee.`row_id` AS e_row_id,
				employee.`valid_from` AS e_valid_from,
				employee.`valid_to` AS e_valid_to,
				employee.`status` AS e_status,
				employee.`tax_number`,
				employee_contract.`employee_contract_id`,
				employee_contract.`employee_contract_number`,
				employee_contract.`employee_contract_type`,
				employee_contract.`workgroup_id`,
				employee_card.`card`,
				company.`company_name`,
				payroll.`payroll_name`,
				workgroup.`workgroup_name`,
				unit.`unit_name`,
				employee_position.`employee_position_name`,
				cost.`cost_name`, ';

			if(EmployeeGroupConfig::isActiveGroup('cost_center_id'))
			{
				$SQL .= 'cost_center.`cost_center_name` as cost_center_name,';
			}

			$SQL .= '
				company_org_group1.`company_org_group_name` as company_org_group1_name,
				company_org_group2.`company_org_group_name` as company_org_group2_name,
				company_org_group3.`company_org_group_name` as company_org_group3_name,
				CASE SUBSTRING(employee.emp_id, -1)%5
					WHEN 0 THEN "<div class=\'reassignment\'><div class=\'reassignmentColor color0\'></div><div class=\'reassignmentContent\'>' . Dict::getValue('waiting_medical_post') . '</div></div>"
					WHEN 1 THEN "<div class=\'reassignment\'><div class=\'reassignmentColor color1\'></div><div class=\'reassignmentContent\'>' . Dict::getValue('waiting_medical_examination') . '</div></div>"
					WHEN 2 THEN "<div class=\'reassignment\'><div class=\'reassignmentColor color2\'></div><div class=\'reassignmentContent\'>' . Dict::getValue('not_match') . '</div></div>"
					WHEN 3 THEN "<div class=\'reassignment\'><div class=\'reassignmentColor color3\'></div><div class=\'reassignmentContent\'>' . Dict::getValue('not_appeared') . '</div></div>"
					WHEN 4 THEN "<div class=\'reassignment\'><div class=\'reassignmentColor color4\'></div><div class=\'reassignmentContent\'>' . Dict::getValue('reassigned') . '</div></div>"
				END AS reassignment';

		if(weHaveModule('ttwa-csm') && App::getSetting('employee_controller_competency_columns')){

			$SQL .= ',CONCAT(
						"<div class=\'position_matching\'><div class=\'position_matchingColor color", ROUND((COUNT(DISTINCT IF(ecomp.competency_id = competency_links.competency_id, 1, NULL)) / COUNT(DISTINCT competency_links.competency_id)) * 100, -1),"\'></div><div class=\'position_matchingContent\'>",
						ROUND((COUNT(DISTINCT IF(ecomp.competency_id = competency_links.competency_id, 1, NULL)) / COUNT(DISTINCT competency_links.competency_id)) * 100, 2),
						"%",
						"</div></div>") AS position_matching';
		}

			$SQL .= '/*
				NULL AS risk_of_term,
				NULL AS time_of_term,
				NULL AS cause_of_term,
				NULL AS ssc_index,
				NULL AS mood_index,
				NULL AS role_in_tem_comm,
				NULL AS absenteeism,
				NULL AS time_prop_abs,
				NULL AS delays,
				NULL AS overtime,
				NULL AS cond_eval,
				NULL AS subs_eval,
				NULL AS cowrs_eval*/
			FROM
				`calendar` cal
			LEFT JOIN
				`employee` ON
					(cal.`date` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`, "'.App::getSetting("defaultEnd").'"))
						AND employee.`status`='.Status::PUBLISHED.'
			LEFT JOIN
				`employee_contract` ON
					employee.`employee_id` = employee_contract.`employee_id`
						AND (cal.`date` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`, "'.App::getSetting("defaultEnd").'"))
							 AND employee_contract.`status`='.Status::PUBLISHED.'
			LEFT JOIN
				`employee_card` ON
					employee_contract.`employee_contract_id` = employee_card.`employee_contract_id`
						AND (cal.`date` BETWEEN employee_card.`valid_from` AND IFNULL(employee_card.`valid_to`, "'.App::getSetting("defaultEnd").'"))
							AND employee_card.`status`='.Status::PUBLISHED.'

			LEFT JOIN
				`employee_cost` ON
					employee_contract.`employee_contract_id` = employee_cost.`employee_contract_id`
						AND (cal.`date` BETWEEN employee_cost.`valid_from` AND IFNULL(employee_cost.`valid_to`, "'.App::getSetting("defaultEnd").'"))
							AND employee_cost.`status`='.Status::PUBLISHED.'

			LEFT JOIN
				`user` ON
					employee.`employee_id` = user.`employee_id`
						AND (cal.`date` BETWEEN user.`valid_from` AND IFNULL(user.`valid_to`, "'.App::getSetting("defaultEnd").'"))
							 AND user.`status`='.Status::PUBLISHED.'
			LEFT JOIN
				`company` ON
					employee.`company_id` = company.`company_id`
						AND (cal.`date` BETWEEN company.`valid_from` AND IFNULL(company.`valid_to`, "'.App::getSetting("defaultEnd").'"))
							AND company.`status`='.Status::PUBLISHED.'
			LEFT JOIN
				`payroll` ON
					employee.`payroll_id` = payroll.`payroll_id`
						AND (cal.`date` BETWEEN payroll.`valid_from` AND IFNULL(payroll.`valid_to`, "'.App::getSetting("defaultEnd").'"))
							AND payroll.`status`='.Status::PUBLISHED.'

			LEFT JOIN
				`cost` ON
					employee_cost.`cost_id` = cost.`cost_id`
						AND (cal.`date` BETWEEN cost.`valid_from` AND IFNULL(cost.`valid_to`, "'.App::getSetting("defaultEnd").'"))
							AND cost.`status`='.Status::PUBLISHED.' ';

		$SQL .= EmployeeGroup::getAllActiveLeftJoinSQL("employee_contract"); //---------------------------------------	slow

		if(EmployeeGroupConfig::isActiveGroup('cost_center_id'))
		{
			$SQL .= "LEFT JOIN `cost_center` ON
						cost_center.`cost_center_id`=".EmployeeGroup::getActiveGroupSQL("cost_center_id","ecost")."
					AND cost_center.`status` = ".Status::PUBLISHED."
					AND employee_contract.`valid_from` <= IFNULL(cost_center.`valid_to`,'".App::getSetting("defaultEnd")."') AND employee_contract.`valid_to` >= cost_center.`valid_from`
					AND employee_contract.`ec_valid_from` <= IFNULL(cost_center.`valid_to`,'".App::getSetting("defaultEnd")."') AND employee_contract.`ec_valid_to` >= cost_center.`valid_from`
				";
		}

		$SQL .= "
			LEFT JOIN company_org_group1 ON
					company_org_group1.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group1_id","employee")."
				AND company_org_group1.`status` = ".Status::PUBLISHED."
				AND cal.`date` BETWEEN company_org_group1.`valid_from` AND IFNULL(company_org_group1.`valid_to`,'".App::getSetting("defaultEnd")."')
			LEFT JOIN company_org_group2 ON
					company_org_group2.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group2_id","employee")."
				AND company_org_group2.`status` = ".Status::PUBLISHED."
				AND cal.`date` BETWEEN company_org_group2.`valid_from` AND IFNULL(company_org_group2.`valid_to`,'".App::getSetting("defaultEnd")."')
			LEFT JOIN company_org_group3 ON
					company_org_group3.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group3_id","employee")."
				AND company_org_group3.`status` = ".Status::PUBLISHED."
				AND cal.`date` BETWEEN company_org_group3.`valid_from` AND IFNULL(company_org_group3.`valid_to`,'".App::getSetting("defaultEnd")."')
			LEFT JOIN `workgroup` ON
					`workgroup`.`workgroup_id`= ".EmployeeGroup::getActiveGroupSQL("workgroup_id","employee_contract")."
				AND `workgroup`.`status`=".Status::PUBLISHED."
				AND cal.`date` BETWEEN `workgroup`.`valid_from` AND IFNULL(`workgroup`.`valid_to`,'".App::getSetting("defaultEnd")."')
			LEFT JOIN `unit` ON
					`unit`.`unit_id`= ".EmployeeGroup::getActiveGroupSQL("unit_id","employee")."
				AND `unit`.`status`=".Status::PUBLISHED."
				AND cal.`date` BETWEEN `unit`.`valid_from` AND IFNULL(`unit`.`valid_to`,'".App::getSetting("defaultEnd")."')
			LEFT JOIN `employee_position` ON
                    `employee_position`.`employee_position_id`= ".EmployeeGroup::getActiveGroupSQL("employee_position_id","employee_contract")."
                AND `employee_position`.`status`=".Status::PUBLISHED."
                AND cal.`date` BETWEEN `employee_position`.`valid_from` AND IFNULL(`employee_position`.`valid_to`,'".App::getSetting("defaultEnd")."')	
		";

		if (isset($gargSQL["join"])) {
			$SQL .= $gargSQL["join"];
		}

		if(App::getSetting('employee_controller_competency_columns')){
			$SQL .= '
					LEFT JOIN `competency_links` ON
							competency_links.`link_table_id` = employee_position.`employee_position_id`
						AND competency_links.link_type = "EP" AND competency_links.status = '.Status::PUBLISHED.'
					LEFT JOIN employee_competency ecomp ON employee_contract.employee_contract_id = ecomp.employee_contract_id AND ecomp.status = ' . Status::PUBLISHED;
		}
		$SQL .=	'
				WHERE
					'.$SQLfilter.'
					AND employee.`row_id` IS NOT NULL
					AND employee_contract.`row_id` IS NOT NULL
		';

		if (isset($gargSQL["where"])) {
			$SQL .= $gargSQL["where"];
		}

		$SQL .= '
			GROUP BY
				employee.`row_id`
			ORDER BY
				`fullname`
		';

		$this->LAGridDB->enableSQLMode("dhtmlxGrid");
		$this->LAGridDB->setSQLSelection($SQL, 'row_id',	"dhtmlxGrid");

		$this->LAGridDB->enableSplitRowID("_");
		$this->LAGridDB->setSplitRowIDStructure(["employee_id", /*"employee_contract_id",*/ "valid_date",]);

		parent::setExportFileName(Dict::getValue("export_file_employee"));
		parent::setGridProperty("splitColumnEnabled", true,	 "dhtmlxGrid");
		parent::setGridProperty("splitColumn", 2,	 "dhtmlxGrid");

		$this->LAGridRights->overrideInitRights("modify", false, "employeeTabHistory");
		$this->LAGridRights->overrideInitRights("modify", false, "employeeContractTabHistory");
		$this->LAGridRights->overrideInitRights("modify", false, "employeeSalaryTabHistory");
		$this->LAGridRights->overrideInitRights("modify", false, "employeeCafeteriaTabHistory");
		$this->LAGridRights->overrideInitRights("modify", false, "employeeAddressTabHistory");
		$this->LAGridRights->overrideInitRights("modify", false, "employeeExtTabHistory");
		$this->LAGridRights->overrideInitRights("modify", false, "employeeExt2TabHistory");
		$this->LAGridRights->overrideInitRights("modify", false, "employeeDocsTab");
		$this->LAGridRights->overrideInitRights("modify", false, "employeeLabourDocsTab");
		$this->LAGridRights->overrideInitRights("modify", false, "employeeGroupTabHistory");
		$this->LAGridRights->overrideInitRights("modify", false, "employeeCardTabHistory");
		$this->LAGridRights->overrideInitRights("modify", false, "employeeCostTabHistory");
		$this->LAGridRights->overrideInitRights("modify", false, "employeeBaseAbsenceTabHistory");
		$this->LAGridRights->overrideInitRights("modify", false, "employeePersonalCompetencyTabHistory");
		$this->LAGridRights->overrideInitRights("search_header", true, "employeePersonalCompetencyTabHistory");
		$this->LAGridRights->overrideInitRights("modify", true, "employeeTravelCostTabHistory");
		$this->LAGridRights->overrideInitRights("modify", false, "employeeDeliveredArticleTabHistory");
		$this->LAGridRights->overrideInitRights("search_header", true, "employeeDeliveredArticleTabHistory");

		$dinamicTabs=Tab::getTabs();
		foreach($dinamicTabs as $tab)
		{
			$this->LAGridRights->overrideInitRights("modify", false, "dinamicTab_".$tab["tab_id"]."History");
		}

		/**
		 * Engedélyezi az egyesített form üzemmódot
		 */
		$this->setBigFormMode("uploadDialog");

		parent::setDialogMaxWidth(6);

		parent::G2BInit();

	}
}
