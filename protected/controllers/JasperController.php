<?php

'yii2-only`;

	namespace app\controllers;
	use app\models\flow\Client;
	use Yang;

`/yii2-only';


#yii2: done

ini_set('display_errors', 1);

require_once Yang::getBasePath() . "/extensions/jasper/autoload.dist.php";

use Jaspersoft\Client\Client;

use Jaspersoft\Dto\Resource\Folder;
use Jaspersoft\Dto\Resource\ReportUnit;

class JasperController extends Controller
{
	public function actionIndex() {
		$this->layout = "//layouts/empty";

		$c = new Client(
						"http://localhost:8080/jasperserver",
						"jasperadmin",
						"jasperadmin"
					);
		
//		$info = $c->serverInfo();
 
//		print_r($info);
		
		/*$folder = new Folder;
 
		$folder->label = "ImageFolder";
		$folder->description = "A folder for storing images";

		$c->repositoryService()->createResource($folder, "/");
		
		$c->repositoryService()->deleteResources("/ImageFolder");*/
		
		$controls = array(
			'Country_multi_select' => array('USA', 'Mexico'),
			'Cascading_state_multi_select' => array('CA', 'OR'),
		);
		
		$report = $c->reportService()->runReport('/reports/samples/Cascading_multi_select_report', 'pdf', null, null, $controls);
		
//		$report_shop = new ReportUnit();
//		
//		$report_shop->uri = "/reports/allaccount_shop";
//		$report_shop->dataSource = "/datasources/JServerJdbcDS";
//
//		$report_shop->jrxml = "/cascade.jrxml";
//		
//		$report = $c->reportService()->runReport('/reports/allaccount_shop', 'pdf');
 
		header('Cache-Control: must-revalidate');
		header('Pragma: public');
		header('Content-Description: File Transfer');
		header('Content-Disposition: attachment; filename=report.pdf');
		header('Content-Transfer-Encoding: binary');
		header('Content-Length: ' . strlen($report));
		header('Content-Type: application/pdf');

		echo $report;
	}
}

?>