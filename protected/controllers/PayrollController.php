<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2History\Grid2HistoryController;
	use app\models\Payroll;
	use app\models\Status;
	use Yang;

`/yii2-only';


#yii2: done

class PayrollController extends Grid2HistoryController
{
	private $user;
	private $defaultEnd;
	private $statusPublished;
	private $useCompanyAndPayrollRights;
	private $payrollControllerShowId;

	public function __construct()
	{
		parent::__construct("payroll");
		$this->user             			= userID();
		$this->defaultEnd					= App::getSetting("defaultEnd");
		$this->statusPublished 				= Status::PUBLISHED;
		$this->useCompanyAndPayrollRights	= App::getSetting("useCompanyAndPayrollRights");
		$this->payrollControllerShowId 		= App::getSetting("payrollController_show_id");
	}

	protected function G2BInit() {
		$this->LAGridDB->setModelName("Payroll");

		parent::setControllerPageTitleId("page_title_payroll");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",		true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		true);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);

		$filters = Yang::session('payroll_filters',[]);
		if (isset($filters["date"]))
		{
			$companyFilter = "";
			if ($this->useCompanyAndPayrollRights) {
				$companyFilter = "AND (`company_id` = '{company}' OR '{company}' = 'ALL' OR '{company}' = '')";
			}
			$art = new ApproverRelatedGroup;
			$gargSQL = $art->getApproverReleatedGroupSQL("Payroll", "companyMainData", false, "'{date}'", "AND", $filters["date"]);
	
			$payroll = new Payroll;
			$payrollCriteria = new CDbCriteria();
			$payrollCriteria->alias = $payroll->tableName();
			$payrollCriteria->condition = "`status` = " . Status::PUBLISHED . "
						AND ('{date}' BETWEEN `valid_from` AND default_end(`valid_to`))
						$companyFilter
						{$gargSQL["where"]}";
			$payrollCriteria->order = "`payroll_name`";
	
			$this->LAGridDB->setModelSelection($payroll, $payrollCriteria);
		}

		parent::G2BInit();
	}

	/**
	 * Kereső értékek átadása sessionnek az oszlopok felépítéséhez
	 * @return void
	 */
	public function actionSetInitProperties()
	{
		Yang::setSessionValue('payroll_filters', requestParam('searchInput'));

		parent::actionSetInitProperties();
	}

	public function search()
	{
		$returnCompanyPayroll = [];
		$dateOnChange = [];

		if ($this->useCompanyAndPayrollRights) {

			$art = new ApproverRelatedGroup;
			$gargCompanySQL = $art->getApproverReleatedGroupSQL("Company", "companyMainData", false, "'{date}'", "AND", "allDate");

			$company = new Company();
			$companyCriteria = $company->getColumnGridCriteria($gargCompanySQL['where'], "{date}");

			$returnCompanyPayroll =
			[
				'company' =>
				[
					'label_text'=> Dict::getValue("company_id"),
					'col_type'	=> 'combo',
					'options'	=>
					[
						'mode'					=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
						'modelSelectionModel'	=> $company,
						'modelSelectionCriteria'=> $companyCriteria,
						'comboId'				=> 'company_id',
						'comboValue'			=> 'company_name',
						'array'					=> "",
					],
				]
			];

			$dateOnChange = ["company"];
		}

		return Yang::arrayMerge(
			[
				'date' => [ 'col_type' => 'ed', 'dPicker' => true, 'width' => '*',
					'label_text'    => Dict::getValue("date"),
					'default_value' => date('Y-m-d'),
					'onchange'      => $dateOnChange
				],
			],
			$returnCompanyPayroll,
			[
				'submit' =>
				[
					'col_type'=>'searchBarReinitGrid',
					'width'=>'*',
					'label_text'=>''
				],
			]
		);
	}

	/**
	 * @return array customized grid columns (name=>params)
	 */
	public function columns()
	{
		$filters = Yang::session('payroll_filters',[]);
		$id = ($this->payrollControllerShowId  || $this->user == '6acd9683761b153750db382c1c3694f6')?
				['payroll_id'		=> ['grid'=>true, 'width'=>260, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'edit'=>false],]:
				[];

		if (!isset($filters["date"])) { return []; }

		$column = [];
		if ($this->useCompanyAndPayrollRights)
		{
			$art = new ApproverRelatedGroup;
			$gargCompanySQL = $art->getApproverReleatedGroupSQL("Company", "companyMainData", false, "'" . $filters["date"] . "'", "AND", $filters["date"]);

			$company = new Company();
			$companyCriteriaGrid = $company->getColumnGridCriteria($gargCompanySQL['where'], $filters["date"]);
			$companyCriteriaDialog = $company->getColumnDialogCriteria($gargCompanySQL['where'], 'valid_from', 'valid_to');

			$column["company_id"] =
			[
				'export'    => true,
				'grid'      => true,
				'col_type'  => 'combo',
				'options'   =>
				[
					'mode'                              => Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'               => $company,
					'modelSelectionCriteriaGridMode'    => $companyCriteriaGrid,
					'modelSelectionCriteriaDialogMode'  => $companyCriteriaDialog,
					'comboId'                           => 'company_id',
					'comboValue'                        => 'company_name',
					'array' => "",
				],
				'width' => "*",
			];
		}

		return Yang::arrayMerge(
			$id,
			$column,
			[
				'payroll_name'	=> ['grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'200'],
				'note'			=> ['grid'=>true, 'window'=>true, 'export'=> false, 'col_type'=>'ed',
									'line_break'=>true],
				'valid_from'	=> ['grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed',
									'dPicker'=>true, 'onchange'=>['company_id']],
				'valid_to'		=> ['grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed',
									'dPicker'=>true, 'onchange'=>['company_id']],
			]
		);
	}
}
?>
