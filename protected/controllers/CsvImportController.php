<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\CsvImport;
	use app\components\Dict;
	use Yang;

`/yii2-only';


#yii2: done

class CsvImportController extends Controller
{
	public function actionIndex() {
		$importProcessId = requestParam('ipid');
		
		$csvImport = new CsvImport;
		if($csvImport->checkImportProcessId($importProcessId)) {
			$csvImport->importfile = dirname(__FILE__).'\database.csv';
			$csvImport->targetDbTable = 'approver';
			$csvImport->run($importProcessId);
		} else {
			header('Content-Type: text/html; charset=utf-8');
			die(Dict::getModuleValue("ttwa-base","missing_parameter"));
		}
	}
}