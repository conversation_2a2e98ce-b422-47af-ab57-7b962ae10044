<?php

/**
 * HomeShopController
 *
 */
class HomeShopController extends Controller
{
	private static $maxPinAttempts = 5;
	private $stPublished;
	private $defaultEnd;


    /**
     * Index action
     *
     */
	public function actionIndex()
    {
        if (array_key_exists("inetconntest", $_GET)) {

            $this->testNetworkConnectivity();
        }

        AnyCache::clear();

        $view            = "index";
        $cplatformClient = App::getSetting("cplatform_client");
        $this->layout    = "//layouts/pageEmpty";
        $card_reader     = (int)requestParam('card_reader');
        $card_num_length = (int)requestParam('card_num_length');

        if (!$card_reader && isset($_SESSION["demo"]["cardNumber"])) {

            unset($_SESSION["demo"]["cardNumber"]);
        }

        if (!isset($_SESSION['cplatform']['home'])) {

            $_SESSION['cplatform']['home'] = true;
        }

        if ($cplatformClient == "kuehnenagel") {

            $view = "application.views.customers.kuehnenagel.homeShop.index";
        } else if ($cplatformClient == "airbus") {

            $view = "application.views.customers.airbus.homeShop.index";
        } else if ($cplatformClient == "masterg") {

            $view = "application.views.customers.masterg.homeShop.index";
        } else if ($cplatformClient == "te") {

            $view = "application.views.customers.teesztergom.homeShop.index";
        } else if ($cplatformClient == "eisberg") {

            $view = "application.views.customers.eisberg.homeShop.index";
        } else {

		    $view = "index";
        }

		$this->render($view, [
			"card_reader"     => $card_reader,
			"card_num_length" => $card_num_length, 
			"dictionaryTexts" => $this->getDictionaryTexts(),
		]);
	}

    /**
     * Init session action
     *
     */
	public function actionInitSession()
	{
		$uri      = requestParam('uri');
		$user_id  = '';
		$fullname = '';

		if (userID() !== null) {
			$user_id  = userID();
			$fullname = $this->getEmployeeNameByUserId($user_id);
		}

		$_SESSION["tiptime"]["settings"]['demoModeBackButton']        = 1;
		$_SESSION["tiptime"]["settings"]['demoModeBackButtonUrl']     = $uri;
		$_SESSION["tiptime"]["settings"]['activitylogger_guest_mode'] = 1;
		$_SESSION["tiptime"]["settings"]["demoRosenbergerKiosk"]      = 1;

		echo $fullname;
	}

    /**
     * Save card action
     *
     */
	public function actionSaveCard()
	{
		$cardNumber  = requestParam('cardNumber');
		$useRegTable = requestParam('useRegTable');

		if (preg_match('/^a_/', $cardNumber)) {

			$cardNumberParts = explode('a_', $cardNumber);
			$cardNumber      = $cardNumberParts[1];
		}

		$_SESSION["demo"]["cardNumber"] = $cardNumber;
		
		if ($useRegTable === true) {

			$user = $this->checkExistingUserWithRegTable($cardNumber);
			$_SESSION['flex']['user_id'] = $user['user_id'];
		}

		$response = [ 'status' => 1 ];
		echo json_encode($response);
	}

    /**
     * Action check card number in DB
     *
     */
    public function actionCheckCardNumberInDb()
    {
        $lang             = userID() ? Dict::getLang() : requestParam('lang', Dict::getLang());
        $card_number      = requestParam('cardNumber');
        $hexFormat        = requestParam("hexFormat");
        $isDecimalFormat  = requestParam("isDecimalFormat");
        $real_card_number = '';
        $stPublished      = Status::PUBLISHED;
        $defaultEnd       = App::getSetting('defaultEnd');
        $response         = [];

        // Check request param 'hexFormat'
        if ($hexFormat == 'true') {
            $hexFormat = true;
        } else {
            $hexFormat = false;
        }

        // Check request param 'isDecimalFormat'
        if ($isDecimalFormat == 'false') {
            $isDecimalFormat = false;
        } else {
            $isDecimalFormat = true;
        }

        // Check card number format
        if (preg_match('/^a_/', $card_number)) {
            $cardNumberParts = explode('a_', $card_number);
            $real_card_number = $cardNumberParts[1];
        } else {
            if ($hexFormat === true && $isDecimalFormat === true) {

                $hex_card_number     = $this->getCardNumberDecimalFormatFromHex($card_number);
                $decimal_card_number = $this->getRealCardNumber($card_number);
                $real_card_number    = "'" . $hex_card_number . "','" . $decimal_card_number . "'";
            } else if ($hexFormat === true && $isDecimalFormat === false) {

                $real_card_number = "'" . $this->getCardNumberDecimalFormatFromHex($card_number) . "'";
            } else if ($hexFormat === false && $isDecimalFormat === true) {

                $real_card_number = "'" . $this->getRealCardNumber($card_number) . "'";
            } else {

                $real_card_number = "'" . $card_number . "'";
            }
        }

        $sql = "
			SELECT
				CONCAT(e.`last_name`,' ',e.`first_name`) as fullname,
				u.`user_id` AS user_id,
			    ecard.`card` AS card
			FROM
			    `employee_card` ecard
			LEFT JOIN
                `employee_contract` ec ON
                ecard.`employee_contract_id` = ec.`employee_contract_id`
				AND ec.`status` = $stPublished
				AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '$defaultEnd')
				AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '$defaultEnd')
			LEFT JOIN
                `employee` e ON
                ec.`employee_id` = e.`employee_id`
				AND e.`status` = $stPublished
				AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '$defaultEnd')
			LEFT JOIN
                `user` u ON
                u.`employee_id` = e.`employee_id`
				AND u.`status` = $stPublished
				AND CURDATE() BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '$defaultEnd')
			WHERE
                ecard.`card` IN ($real_card_number)
				AND ecard.`status` = $stPublished
				AND CURDATE() BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`, '$defaultEnd')
            LIMIT 1
        ";

        $result = dbFetchAll($sql);

        $_SESSION["bypassLDAP"] = false ;
        if (count($result) > 0)
        {
            if ($result[0]['user_id'] !== null) {
                $response['fullname']   = $result[0]['fullname'];
                $response['status']     = 1;
                $_SESSION["bypassLDAP"] = true ;
            } else {
                $response['status'] = 2;
                $response['error']  = Dict::getValueWithLang("missing_user_data", $lang);
            }
        } else {
            $response['status']     = 2;
            $response['error']      = Dict::getValueWithLang('invalid_card_number', $lang);
            $response['cardNumber'] = (int)$real_card_number;
        }

        echo json_encode($response);
    }

    /**
     * Get username and password
     *
     */
    public function actionGetUsernameAndPassword()
    {
        $lang             = userID() ? Dict::getLang() : requestParam('lang', Dict::getLang());
        $card_number      = requestParam('cardNumber');
        $hexFormat        = requestParam("hexFormat");
        $isDecimalFormat  = requestParam("isDecimalFormat");
        $real_card_number = '';
        $stPublished      = Status::PUBLISHED;
        $defaultEnd       = App::getSetting('defaultEnd');
        $response         = [];

        // Check request param 'hexFormat'
        if ($hexFormat == 'true') {
            $hexFormat = true;
        } else {
            $hexFormat = false;
        }

        // Check request param 'isDecimalFormat'
        if ($isDecimalFormat == 'false') {
            $isDecimalFormat = false;
        } else {
            $isDecimalFormat = true;
        }

        // Check card number format
        if (preg_match('/^a_/', $card_number)) {
            $cardNumberParts = explode('a_', $card_number);
            $real_card_number = $cardNumberParts[1];
        } else {
            if ($hexFormat === true && $isDecimalFormat === true) {

                $hex_card_number     = $this->getCardNumberDecimalFormatFromHex($card_number);
                $decimal_card_number = $this->getRealCardNumber($card_number);
                $real_card_number    = "'" . $hex_card_number . "','" . $decimal_card_number . "'";
            } else if ($hexFormat === true && $isDecimalFormat === false) {

                $real_card_number = "'" . $this->getCardNumberDecimalFormatFromHex($card_number) . "'";
            } else if ($hexFormat === false && $isDecimalFormat === true) {

                $real_card_number = "'" . $this->getRealCardNumber($card_number) . "'";
            } else {

                $real_card_number = "'" . $card_number . "'";
            }
        }

        $sql =	"
            SELECT
                u.`username`,
                u.`password`
            FROM
                `employee_card` ecard
            LEFT JOIN
                `employee_contract` ec ON
                ecard.`employee_contract_id` = ec.`employee_contract_id`
                AND ec.`status` = $stPublished
                AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '$defaultEnd')
                AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '$defaultEnd')
            LEFT JOIN
                `employee` e ON
                ec.`employee_id` = e.`employee_id`
                AND e.`status` = $stPublished
                AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '$defaultEnd')
            INNER JOIN
                `user` u ON
                e.`employee_id` = u.`employee_id`
                AND u.`status` = $stPublished
                AND CURDATE() BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '$defaultEnd')
            WHERE
                ecard.`card` IN ($real_card_number)
                AND ecard.`status` = $stPublished
                AND CURDATE() BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`, '$defaultEnd')
            LIMIT 1
        ";

        $result = dbFetchAll($sql);

        if (count($result) === 1)
        {
            $response['status'] = 1;
            $response['username'] = $result[0]['username'];
            $response['password'] = $result[0]['password'];
        } else if (count($result) < 1) {
            $response['status'] = 2;
            $response['error'] = Dict::getValueWithLang("missing_user_data", $lang);
        }  else {
            $response['status'] = 3;
            $response['error'] = Dict::getValueWithLang("duplicated_username_belong_to_same_card", $lang);
        }

        echo json_encode($response);
    }

    /**
     * Check username and password in db action
     *
     */
    public function actionCheckUsernameAndPasswordInDb()
    {
        $lang           = userID() ? Dict::getLang() : requestParam('lang', Dict::getLang());
        $card_number    = requestParam('cardNumber');
        $username       = requestParam('username');
        $password       = requestParam('password');
        $hashedPassword = hash('sha512',$password);

        $sql = "
            SELECT
                u.`password`,
                u.`username`
            FROM
                `employee_card` ecard
            LEFT JOIN
                `employee_contract` ec ON ecard.`employee_contract_id` = ec.`employee_contract_id`
                AND ec.`status` = ".Status::PUBLISHED."
                AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '".App::getSetting("defaultEnd")."')
                AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '".App::getSetting("defaultEnd")."')
            LEFT JOIN
                `employee` e ON ec.`employee_id` = e.`employee_id`
                AND e.`status` = ".Status::PUBLISHED."
                AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."')
            LEFT JOIN
                `user` u ON e.`employee_id` = u.`employee_id`
                AND u.`status` = ".Status::PUBLISHED."
                AND CURDATE() BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '".App::getSetting("defaultEnd")."')
            WHERE
                ecard.`card` = '".$card_number."'
                AND ecard.`status` = ".Status::PUBLISHED."
                AND CURDATE() BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`, '".App::getSetting("defaultEnd")."')
        ";

        $result   = dbFetchAll($sql);
        $response = [];

        if (count($result) > 0) {

            $username_to_card_number = $result[0]['username'];
            $password_to_card_number = $result[0]['password'];

            if ($username === $username_to_card_number && $hashedPassword === $password_to_card_number) {

                $response['match'] = true;
            } else {

                $response['match'] = false;
                $response['error'] = Dict::getValueWithLang("error_password_incorrect", $lang);
            }
        }

        echo json_encode($response);
    }

    /**
     * Get navigation url action
     *
     */
    public function actionGetNavigationUrl()
    {
        $lang      = userID() ? Dict::getLang() : requestParam('lang', Dict::getLang());
        $button_id = requestParam('button_id');
        $response  = [];

        $sql = "
            SELECT
                `navigation_url`
            FROM
                `cplatform_button_config`
            WHERE
                `button_id` = '$button_id'
        ";

        $result = $result = dbFetchAll($sql);

        if ($result > 0) {

            $response['status'] = 1;
            $response['url']    = $result[0]['navigation_url'];
        } else {

            $response['status'] = 2;
            $response['error']  = Dict::getValueWithLang("missing_navigation_url", $lang);
        }

        echo json_encode($response);
    }

    /**
     * Check discount condition action
     *
     * @return void
     */
    public function actionCheckCondition()
    {
        $userId             = userID();
        $lang               = $userId ? Dict::getLang() : requestParam('lang', Dict::getLang());
        $employeeContractId = $this->getEmployeeContractId($userId);
        $check              = $this->checkDiscount($employeeContractId);
		$balance			= $this->checkCurrentEmployeeBenefitBalance($employeeContractId);
        $response           = [];

        try {

            // Check parameters
            if ($employeeContractId === NULL) {
                throw new Exception(Dict::getValueWithLang("errorNoEmployeeContractId", $lang));
            }

            // Render
            $response['check']           = $check;
            $response['status']          = 1;
            $response['discount_amount'] = $check == true ? App::getSetting('discountAmount') . '%' : '0%';
			$response['balance_amount']	 = $balance;
        } catch (Exception $ex) {
            $response['status']          = 2;
            $response['discount_amount'] = '0%';
            $response['error']           = $ex->getMessage();
        }

        echo json_encode($response);
    }

    /**
     * Calculate discount action
     *
     * @return void
     */
    public function actionCalculateDiscount()
    {
        $userId             = userID();
        $lang               = $userId ? Dict::getLang() : requestParam('lang', Dict::getLang());
        $employeeContractId = $this->getEmployeeContractId($userId);
        $totalAmount        = 0;
        $response           = [];

        try {

            // Check parameters
            if ($employeeContractId === NULL) {
                throw new Exception(Dict::getValueWithLang("errorNoEmployeeContractId", $lang));
            }

            if (requestParam('discountTotalAmount') !== null) {

                $discountTotalAmount = (float)str_replace(' ', '', requestParam('discountTotalAmount'));
                if ($discountTotalAmount == (float)App::getSetting('discountTotalAmountMin')) {

                    throw new Exception(Dict::getValueWithLang("errorDiscountTotalAmountTooLow", $lang));
                }
                if ($discountTotalAmount > (float)App::getSetting('discountTotalAmountMax')) {

                    throw new Exception(Dict::getValueWithLang("errorDiscountTotalAmountTooHigh", $lang));
                }
            } else {
                throw new Exception(Dict::getValueWithLang("errorDiscountTotalAmountBlank", $lang));
            }

            // Save data
            $model                       = new EmployeeShopDiscount();
            $check                       = $this->checkDiscount($employeeContractId);
            $discount_amount             = $check == true ? App::getSetting('discountAmount') : '0';
            $model->employee_contract_id = $employeeContractId;
            $model->currency_code        = App::getSetting('currencyCode');
            $model->discount_amount      = (float)($discount_amount/100);
            $model->total_amount         = $discountTotalAmount;
            $model->discount_measure     = round($model->total_amount * $model->discount_amount,2);
            $model->paid_amount          = round($model->total_amount - $model->discount_measure,2);
			$model->status 				 = Status::DRAFT; 

            $model->save();

            // Render
            $currency_short_name                     = $this->getCurrencyShortName($model->currency_code);
            $response['discount_amount']             = $discount_amount . '%';
            $response['status']                      = 1;
            $response['discountMeasure']             = number_format($model->discount_measure, $this->getDecimalPlaceLength($model->discount_measure), ',', ' ') . ' ' . $currency_short_name;
            $response['paidAmount']                  = number_format($model->paid_amount, $this->getDecimalPlaceLength($model->paid_amount), ',', ' ') . ' ' . $currency_short_name;
            $cumulativeDiscount                      = $this->calculateCumulativeDiscountMeasure($employeeContractId);
            $cumulativeDiscountDate                  = date_create($cumulativeDiscount["start_date"]);
            $response['cumulativeDiscountStartDate'] = date_format($cumulativeDiscountDate, 'Y.m.d');
            $response['cumulativeDiscountMeasure']   = number_format($cumulativeDiscount["sum_measure"], $this->getDecimalPlaceLength($cumulativeDiscount["sum_measure"]), ',', ' ') . ' ' . $currency_short_name;
            $response['error']                       = $model->getErrors();
			$response['discountId']					 = $model->row_id;
        } catch (Exception $ex) {
            $response['status'] = 2;
            $response['error']  = $ex->getMessage();
        }

        echo json_encode($response);
    }

	public function actionSaveDiscountStatus()
	{
		$discountRowId 	= requestParam("discountId");
		$event			= requestParam("event");
		$response		= [];

		if ($event) {
			$newStatus = ($event === 'accept') ? Status::PUBLISHED : Status::DELETED;
		} else {
			$response['status'] = 2;
		}

		try {
			$employeeShopDiscount = EmployeeShopDiscount::model()->findByAttributes(['row_id' => $discountRowId]);

			$employeeShopDiscount->status = $newStatus;

			$employeeShopDiscount->save();

			$response['status'] = 1;
		} catch (Exception $e) {
			$response['status'] = 2;
			$response['error'] 	= $e->getMessage();
		}

		echo json_encode($response);
	}

	public function actionSaveUsedBalance() 
	{
		$usedBalance 				= requestParam("usedBalance");
		$userId						= userID();
		$employeeContractId 		= $this->getEmployeeContractId($userId);
		$currencyCode				= App::getSetting('currencyCode');
		$response					= [];
		$existingSameUsedBalance	= $this->getLastSavedUsedBalance($employeeContractId, $usedBalance);

		try {
			if (!$existingSameUsedBalance) {
				$employeeShopBalance 						= new EmployeeShopBalance();
				$employeeShopBalance->employee_contract_id 	= $employeeContractId;
				$employeeShopBalance->currency_code			= $currencyCode;
				$employeeShopBalance->paid_amount			= $usedBalance;

				$employeeShopBalance->save();

				$response['status'] = 1;
			} else {
				$response['status'] = 2;
			}
		} catch (Exception $e) {
			$response['status'] = 2;
			$response['error'] 	= $e->getMessage();
		}

		echo json_encode($response);
	}

	private function getLastSavedUsedBalance(string $employeeContractId, string $usedBalance)
	{
		$beforeTime = date('Y-m-d H:i:s', strtotime('-5 seconds'));

		$criteria = new CDbCriteria([
			'condition' => 'employee_contract_id=:employeeContractId AND paid_amount=:paidAmount AND status=:status AND 
							created_on BETWEEN :beforeTime AND :currentTime',
			'params' => [
				':employeeContractId'	=> $employeeContractId,
				':paidAmount'			=> $usedBalance,
				':status'				=> Status::PUBLISHED,
				':currentTime'			=> date('Y-m-d H:i:s'),
				':beforeTime'			=> $beforeTime
			]
		]);

		$lastSavedUsedBalance = EmployeeShopBalance::model()->find($criteria);

		if ($lastSavedUsedBalance !== null) {
			return true;
		} else {
			return false;
		}
	}

    private function checkDiscount(string $employeeContractId = NULL) : ?string
    {
        $result                      = false;
        $current_year_first_today    = " ea.day BETWEEN (MAKEDATE(EXTRACT(YEAR FROM CURDATE()),1)) AND CURDATE()";
        $previous_month_first_last   = " ea.day BETWEEN (LAST_DAY(CURDATE() - INTERVAL 2 MONTH) + INTERVAL 1 DAY) AND (LAST_DAY(CURDATE() - INTERVAL 1 MONTH))";
        $cond_unjustified_absence    = " ea.state_type_id = 'def32968390fb987c823da0cbf7d3bd8'";
        $cond_justified_but_not_paid = " ea.state_type_id = '269b59b4efbbb4ef1d527492dc06cb60'";
        $cond_sick_leave             = "
         ea.state_type_id IN (
            '11d5ce859c686c0dc3c9727d19cfbf5f',
            '12dd1237b950bfa8ece94c120b5d4f8f',
            '5b70cb93b84b05e1803b7825c55c8be1',
            '645929a42799fe05e27801ca605624ca',
            '7477f10be8bf995d7a48c104f1d8fe1c',
            '7b3cf78f9fd2a404567fe572fcb0eaf9',
            '879e1d423b540a25f8c152948bb5066a',
            '8f6615bc77b4099011f7e9e5cbce6023',
            'a8fd428dd18c27257aceb1629c75d637',
            'ab518692163bdcc355dace7d919a47b5',
            'bac18782b00e0921f91817826d517b70'
        )";

        if ($employeeContractId !== null) {

            $sql_unjustified_absence = "
                SELECT
                    IF(COUNT(ea.row_id) > 0, 'FALSE', 'TRUE') AS unjustified_absence
                FROM
                    employee_absence ea
                WHERE
                    ea.employee_contract_id = '" . $employeeContractId . "'
                    AND ea.status = " . Status::PUBLISHED . "
                    AND " . $cond_unjustified_absence . "
                    AND " . $previous_month_first_last ." 
            ";
            $unjustified_absence = dbFetchValue($sql_unjustified_absence);

            $sql_no_paid_absence = "
                SELECT
                    IF(COUNT(ea.row_id) > 0, 'FALSE', 'TRUE') AS justified_but_not_paid
                FROM
                    employee_absence ea
                WHERE
                    ea.employee_contract_id = '" . $employeeContractId . "'
                    AND ea.status = " . Status::PUBLISHED . "
                    AND " . $cond_justified_but_not_paid . "
                    AND " . $previous_month_first_last ." 
            ";
            $no_paid_absence = dbFetchValue($sql_no_paid_absence);

            $sql_sick_pay_5day = "
                SELECT
                    IF(COUNT(ea.row_id) > 5, 'FALSE', 'TRUE') AS sick_leave
                FROM
                    employee_absence ea
                WHERE
                    ea.employee_contract_id = '" . $employeeContractId . "'
                    AND ea.status = " . Status::PUBLISHED . "
                    AND " . $cond_sick_leave . "
                    AND " . $previous_month_first_last ." 
            ";
            $sick_pay_5day = dbFetchValue($sql_sick_pay_5day);

            $sql_sick_pay_30day = "
                SELECT
                    IF(COUNT(ea.row_id) > 30, 'FALSE', 'TRUE') AS sick_leave
                FROM
                    employee_absence ea
                WHERE
                    ea.employee_contract_id = '" . $employeeContractId . "'
                    AND ea.status = " . Status::PUBLISHED . "
                    AND " . $cond_sick_leave . "
                    AND " . $current_year_first_today ."
            ";
            $sick_pay_30day = dbFetchValue($sql_sick_pay_30day);

            if ($unjustified_absence === 'TRUE' && $no_paid_absence === 'TRUE' && $sick_pay_5day === 'TRUE' && $sick_pay_30day == 'TRUE') {
                $result = true;
            }
        }

        return $result;
    }

	private function checkCurrentEmployeeBenefitBalance(string $employeeContractId)
	{
		$totalBalance = 0;
		$spentBalance = 0;
		$empId = Employee::getEmployeeEmpIDByEcID($employeeContractId);
		$employeeId = Employee::getEmployeeIdByEmpId($empId);
		$modelAndColumnToSave = explode(";",App::getSetting("employeeBenefitBalanceModelAndColumn"));
		$balanceModel 	= $modelAndColumnToSave[0];
		$balanceColumn 	= $modelAndColumnToSave[1];

		$criteria = new CDbCriteria([
			'select'	=> 'SUM(' . $balanceColumn . ') as total',
			'condition' => 'employee_id=:employeeId AND status=:status',
			'params' => [
				':employeeId'		=> $employeeId,
				':status'			=> Status::PUBLISHED
			]
		]);

		$employeeBenefitBalance = $balanceModel::model()->find($criteria);

		if ($employeeBenefitBalance) {
			$totalBalance = (int) $employeeBenefitBalance->total;
		}

		$criteria = new CDbCriteria([
			'select'	=> 'IF(SUM(paid_amount) IS NOT NULL, SUM(paid_amount), 0) as total_spent',
			'condition' => 'employee_contract_id=:employeeContractId AND status=:status',
			'params' => [
				':employeeContractId'	=> $employeeContractId,
				':status'				=> Status::PUBLISHED
			]
		]);

		$employeeSpentBalance = EmployeeShopBalance::model()->find($criteria);

		if ($employeeSpentBalance) {
			$spentBalance = (int) $employeeSpentBalance->total_spent;
		}

		return $totalBalance - $spentBalance;
	}

    /**
     * Get employee contract id
     *
     * @param string|null $userId
     * @return string|null
     */
    private function getEmployeeContractId(string $userId = NULL) : ?string
    {
        $result = NULL;

        if ($userId !== null) {
            $sql = "
                SELECT
                    ec.employee_contract_id
                FROM
                    user
                LEFT JOIN employee e ON
                    user.employee_id = e.employee_id
                    AND e.status = " . Status::PUBLISHED . "
                    AND CURDATE() BETWEEN e.valid_from AND IFNULL(e.valid_to, '" . App::getSetting("defaultEnd") . "')
                LEFT JOIN employee_contract ec ON
                    e.employee_id = ec.employee_id
                    AND ec.status = " . Status::PUBLISHED . "
                    AND CURDATE() BETWEEN ec.valid_from AND IFNULL(ec.valid_to, '" . App::getSetting("defaultEnd") . "')
                    AND CURDATE() BETWEEN ec.ec_valid_from AND IFNULL(ec.ec_valid_to, '" . App::getSetting("defaultEnd") . "')
                WHERE
                    user.user_id = '" . $userId . "'
                    AND user.status = " . Status::PUBLISHED . "
                    AND CURDATE() BETWEEN user.valid_from AND IFNULL(user.valid_to, '" . App::getSetting("defaultEnd") . "')
            ";
            $result = dbFetchValue($sql);
        }

        return $result;
    }


    /**
     * Calculate cunmulative discount measure
     *
     * @param string|null $employeeContractId
     * @return array|null
     */
    private function calculateCumulativeDiscountMeasure(string $employeeContractId = NULL) : ?array
    {
        $result = NULL;

        if ($employeeContractId !== null) {

            $result = [
                'sum_measure' => NULL,
                'start_date'  => NULL
            ];

            $sql_sum = "
                SELECT
                    sum(esd.discount_measure) over (ORDER BY created_on) as cumulative_measure
                FROM
                    employee_shop_discount esd
                WHERE
                    esd.employee_contract_id = '" . $employeeContractId . "'
                    AND esd.status = " . Status::PUBLISHED . "
                    AND CURDATE() BETWEEN esd.valid_from AND IFNULL(esd.valid_to, '" . App::getSetting("defaultEnd") . "')
                ORDER BY
                    esd.created_on DESC                    
                LIMIT 1                    
            ";
            $result['sum_measure'] = dbFetchValue($sql_sum);

            $sql_date = "
                SELECT
                    esd.created_on AS start_date
                FROM
                    employee_shop_discount esd
                WHERE
                    esd.employee_contract_id = '" . $employeeContractId . "'
                    AND esd.status = " . Status::PUBLISHED . "
                    AND CURDATE() BETWEEN esd.valid_from AND IFNULL(esd.valid_to, '" . App::getSetting("defaultEnd") . "')
                ORDER BY
                    esd.created_on ASC
                LIMIT 1
            ";
            $result['start_date'] = dbFetchValue($sql_date);
        }

        return $result;
    }

    /**
     * Get currency short name
     *
     * @param string|null $currencyCode
     * @return string|null
     */
    private function getCurrencyShortName(string $currencyCode = NULL) : ?string
    {
        $result = NULL;

        if ($currencyCode !== null) {
            $sql = "
                SELECT
                    IF(c.short_name IS NULL, c.code, c.short_name) AS short_name 
                FROM
                    currency c
                WHERE
                    c.code = '" . $currencyCode . "'
                    AND c.status = " . Status::PUBLISHED . "
                    AND CURDATE() BETWEEN c.valid_from AND IFNULL(c.valid_to, '" . App::getSetting("defaultEnd") . "')
                LIMIT 1
            ";
            $result = dbFetchValue($sql);
        }

        return $result;
    }

    /**
     * Get decimal place length
     *
     * @param float $value
     * @return int
     */
    private function getDecimalPlaceLength(float $value = 0) : int
    {
        $result = 0;

        if ($value > 0) {

            $frac    = $value % 1;
            $rtesult = $frac > 0 ? 2 : 0;

        }

        return $result;
    }

    /**
     * Get real card number
     *
     * @param mixed $inCardNumber
     * @return false|float|int
     */
	private function getRealCardNumber($inCardNumber)
	{
		if (empty($inCardNumber)) {
			return false;
		}

        $maxLengthBinCardNumber = (int)App::getSetting('maxLengthBinCardNumber');
		$binCardNumber          = decbin((int)$inCardNumber);

		if (strlen($binCardNumber) > $maxLengthBinCardNumber) {

			$binCardNumber = substr($binCardNumber, -1*$maxLengthBinCardNumber);
		}

		$outCardNumber = bindec($binCardNumber);

		return $outCardNumber;
	}

    /**
     * Get card number decimal format from hex
     *
     * @param string $hex
     * @return float|int
     */
	private function getCardNumberDecimalFormatFromHex(string $hex)
	{
		$decimalCardNumber = hexdec($hex);
		
		return $decimalCardNumber;
	}


    /**
     * Get user
     *
     * @param string $userId
     * @param string $username
     * @return mixed|User
     */
	private function getUser(string $userId, string $username)
	{
        $statusPublished     = Status::STATUS_PUBLISHED;
		$model_user			 = new User;
		$criteria			 = new CDbCriteria();
		$criteria->condition = ($username !== null && $username !== "") ? "`username` = '{$username}'" : "`user_id` = '{$userId}'";
        $criteria->condition .= "AND `status` = '{$statusPublished}'";
		$user				 = $model_user->findAll($criteria);

		return $user[0];
	}

    /**
     * Check user password in db
     *
     * @param string $userId
     * @param string $username
     * @param string $password
     * @return bool
     */
	private function checkUserPasswordInDb(string $userId, string $username, string $password)
	{
		$userCondition = ($username !== null && $username !== "") ? "`username` = '{$username}'" : "`user_id` = '{$userId}'";
		
		$SQL = "
            SELECT
                row_id
            FROM
                `user`
            WHERE
                {$userCondition}
                AND `password` = '$password'
                AND `status` = ".Status::PUBLISHED."
            ORDER BY
                `password_date`
            LIMIT 1
        ";

		$result = dbFetchAll($SQL);

		if (count($result) > 0) {
			return true;
		} else {
			return false;
		}
	}

    /**
     * Get employee name by user id
     *
     * @param string $user_id
     * @return mixed|null
     */
	private function getEmployeeNameByUserId(string $user_id)
	{
		$fullname = '';

		$sql = "
            SELECT
                CONCAT(e.`last_name`,' ',e.`first_name`) as fullname
            FROM
                `user` u
            LEFT JOIN
                employee e ON u.`employee_id` = e.`employee_id`
                AND e.`status` = ".Status::PUBLISHED."
                AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."')
            WHERE
                u.`user_id` = '$user_id'
                AND u.`status` = ".Status::PUBLISHED."
                AND CURDATE() BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '".App::getSetting("defaultEnd")."')
            LIMIT 1
        ";

		$result = dbFetchAll($sql);

		if (count($result) > 0) {
			$fullname = $result[0]['fullname'];
		} else {
			$fullname = null;
		}

		return $fullname;
	}

    /**
     * Get dictionary texts
     *
     * @return array
     */
	public function getDictionaryTexts()
	{
        $lang = userID() ? Dict::getLang() : requestParam('lang', Dict::getLang());

		$result = [
            "cplatform_welcome_system"	=> Dict::getValueWithLang("cplatform_welcome_system", $lang),
            "mobileApp"					=> Dict::getValueWithLang("mobile_app", $lang),
			"withCard"					=> Dict::getValueWithLang("with_card", $lang),
			"withPassword"				=> Dict::getValueWithLang("with_password", $lang),
			"chooseAuthenticationMode"	=> Dict::getValueWithLang("choose_authentication_mode", $lang),
			"username"					=> Dict::getValueWithLang("username", $lang),
			"password"					=> Dict::getValueWithLang("password", $lang),
			"login"						=> Dict::getValueWithLang("cplatform_login", $lang),
			"loginForAuthentication"	=> Dict::getValueWithLang("login_for_authentication", $lang),
			"scanCard"					=> Dict::getValueWithLang("scan_card", $lang),
			"submit"					=> Dict::getValueWithLang("cplatform_submit", $lang),
			"typePin"					=> Dict::getValueWithLang("type_pin", $lang),
			"welcome"					=> Dict::getValueWithLang("cplatform_welcome", $lang),
            "welcomeSystem" 			=> Dict::getValueWithLang("cplatform_welcome_system", $lang),
			"changePassword"			=> Dict::getValueWithLang("cplatform_change_password", $lang),
			"prevPassword"				=> Dict::getValueWithLang("prev_password", $lang),
			"newPassword"				=> Dict::getValueWithLang("new_password", $lang),
			"newPasswordAgain"			=> Dict::getValueWithLang("new_password_again", $lang),
			"save"						=> Dict::getValueWithLang("save", $lang),
			"checkPin"					=> Dict::getValueWithLang("checkPin", $lang),
			"changePin"					=> Dict::getValueWithLang("changePin", $lang),
			"currentPin"				=> Dict::getValueWithLang("currentPin", $lang),
			"newPin"					=> Dict::getValueWithLang("newPin", $lang),
			"newPinAgain"				=> Dict::getValueWithLang("newPinAgain", $lang),
			"savePin"					=> Dict::getValueWithLang("save", $lang),
			"setPin"					=> Dict::getValueWithLang("setPin", $lang),
            "approveGtc"	    		=> Dict::getValueWithLang("approveGtc", $lang),
            "discountAmount"	    	=> Dict::getValueWithLang("discountAmount", $lang),
            "discountTotalAmount"	    => Dict::getValueWithLang("discountTotalAmount", $lang),
            "discountMeasure"	    	=> Dict::getValueWithLang("discountMeasure", $lang),
            "discountPaidAmount"	    => Dict::getValueWithLang("discountPaidAmount", $lang),
            "discountTotalMeasure"	    => Dict::getValueWithLang("discountTotalMeasure", $lang),
			"oktaurl"					=> baseURL().'/login/login'
		];

		return $result;
	}

    /**
     * Test network connectivity
     *
     */
	private function testNetworkConnectivity()
	{
		$randomNumber       = $_GET["inetconntest"];
		$hashedRandomNumber = md5($randomNumber);
		$content            = "<body>$hashedRandomNumber</body>";
		die ($content) ;
	}

}