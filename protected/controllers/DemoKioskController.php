<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\components\MyActiveForm;
	use app\models\ProductivityButtonConfig;
	use app\models\Registration;
	use app\models\Status;
	use Yang;

`/yii2-only';


#yii2: done

Yang::import('application.components.KioskGrid.controllers.KioskGridController');

class DemoKioskController extends KioskGridController
{

	public function actionGetSectionLayout()
	{
		$html = "";
		
		$pbc = new ProductivityButtonConfig();
		$crit = new CDbCriteria();
		$crit->condition = "`layout_level` = 1 AND `status` = ".Status::PUBLISHED."";
		$crit->order = "`button_order` ASC";
		$results = $pbc->findAll($crit);
		
		$sections_array = [];
		
		foreach ($results as $res) {
			$sections_array[] = [
								'button_class'		=> $res->button_class,
								'button_name'		=> Dict::getValue($res->button_dict_id),
								'button_id'			=> $res->button_id,
								'button_label'		=> $res->button_label
								];	
		}
		
		$html .= parent::getRowStart();

		$no = 0;
		foreach ($sections_array as $sections) {
			if ($no && $no % 3 === 0) {
				$html .= $this->getRowEnd();
				$html .= $this->getRowStart();
			}

			$html .= $this->getSectionButton($sections);

			$no++;
		}

		$html .= parent::getRowEnd();

		$resp = [
			"html"			=> $html,
		];

		echo json_encode($resp);
	}
	
	public function actionGetJobTaskLayout()
	{
		$parent_button_id = requestParam('parent_button_id');
		$html = "";
		
		$pbc = new ProductivityButtonConfig();
		$crit = new CDbCriteria();
		$crit->condition = "`layout_level` = 2 AND `status` = ".Status::PUBLISHED." AND `parent_button_id` = $parent_button_id";
		$crit->order = "`button_order` ASC";
		$results = $pbc->findAll($crit);
		
		$job_tasks_array = [];
		
		foreach ($results as $res) {
			$job_tasks_array[] = [
								'button_class'		=> $res->button_class,
								'button_name'		=> Dict::getValue($res->button_dict_id),
								'button_id'			=> $res->button_id,
								'button_label'		=> $res->button_label
								];	
		}
		
		$html .= parent::getRowStart();

		$no = 0;
		foreach ($job_tasks_array as $job_task) {
			if ($no && $no % 10 === 0) {
				$html .= $this->getRowEnd();
				$html .= $this->getRowStart();
			}

			$html .= $this->getJobTaskButton($job_task);

			$no++;
		}

		$html .= parent::getRowEnd();

		$resp = [
			"html"			=> $html,
		];

		echo json_encode($resp);
		
	}
	
	private function getSectionButton($button_config = []) 
	{
		$html = '
				<div class="gridCell productivity_buttons section_buttons">
					<div class="gridButton gridTable" data-value="'.$button_config['button_id'].'">
						<div class="gridCell button_label">
							'.$button_config['button_label'].'
						</div>
						<div class="gridCell jobTasks '.$button_config['button_class'].'">
						</div>
						<div class="gridCell button_name">
							'.$button_config['button_name'].'
						</div>
					</div>
				</div>
				';

		return $html;
	}
	
	private function getJobTaskButton($button_config = [])
	{
		$classes = explode(" ",$button_config['button_class']);
		$img_src = $classes[0];
		
		$html = '
				<div class="gridCell productivity_buttons job_task_buttons">
					<div class="gridButton gridTable '.$button_config['button_class'].'" data-value="'.$button_config['button_id'].'">
						<div class="gridCell button_label">'.$button_config['button_label'].'</div>
						<div class="gridCell button_name">'.$button_config['button_name'].'</div>
						<div class="buttonImageContainer">
							<img src="/images/buttons/productivity/'.$img_src.'.png" class="button_images" />
						</div>
					</div>
				</div>
				';

		return $html;
	}
	
	public function actionCheckCardNumberInDb()
	{
		$card_number = requestParam('cardNumber');
		
		$real_card_number = parent::getRealCardNumber($card_number);
		
		$sql = "
				SELECT
					CONCAT(e.`last_name`,' ',e.`first_name`) as fullname,
					ec.`employee_contract_id` as employee_contract_id,
					fs.`file_url` as image_url
				FROM `employee_card` ecard
				LEFT JOIN employee_contract ec ON
						ecard.`employee_contract_id` = ec.`employee_contract_id`
					AND ec.`status` = ".Status::PUBLISHED."
					AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '".App::getSetting("defaultEnd")."')
					AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '".App::getSetting("defaultEnd")."')
				LEFT JOIN employee e ON
						ec.`employee_id` = e.`employee_id`
					AND e.`status` = ".Status::PUBLISHED."
					AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."')
				LEFT JOIN file_storage fs ON
						e.`employee_image` = fs.`file_id`
				WHERE
						ecard.`card` = '".$real_card_number."'
					AND ecard.`status` = ".Status::PUBLISHED."
					AND CURDATE() BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`, '".App::getSetting("defaultEnd")."')
				LIMIT 1
				";
		
		$result = dbFetchAll($sql);
		
		$response = [];
		
		if (count($result) > 0) {
			$response['status'] = 1;
			$response['fullname'] = $result[0]['fullname'];
			$response['employee_contract_id'] = $result[0]['employee_contract_id'];
			$response['image_url'] = $result[0]['image_url'];
		} else {
			$response['status'] = 2;
			$response['error'] = Dict::getValue("error_invalid_card");
		}
		
		echo json_encode($response);
	}
	
	public function actionGetEmployeeFullNameAndContractIdByCard() {
		parent::actionGetEmployeeFullNameAndContractIdByCard();
	}
	
	/*public function actionSave() {
		$params    = requestParam('form');

		$cardNumber = (int)$params["cardNumber"];
		$selectedCost = $params["costId"];

		if (!empty($cardNumber) && !empty($selectedCost)) {
			$cardNumber = $this->getRealCardNumber($cardNumber);

			if ((int)App::getSetting("wfm_used_seconds")) {
				$time = date("Y-m-d H:i:s");
			} else {
				$time = date("Y-m-d H:i");
			}
			
			$updateSql = "UPDATE `registration` r SET status = '" . Status::DELETED . "' 
								WHERE r.card = '" . $cardNumber . "' 
									AND ABS(TIMESTAMPDIFF(SECOND, r.time, '". $time . "')) <= " . self::TIME_DIFF_FOR_DUPLICATION_CLEAR . " 
									AND r.event_type_id = 'COST' 
									AND r.status = '" . Status::PUBLISHED . "'";
			dbExecute($updateSql);

			$new_reg = new Registration;
			$new_reg->terminal_id = "rk_001";
			$new_reg->card = $cardNumber;
			$new_reg->time = $time;
			$new_reg->event_type_id = "COST";
			$new_reg->cost_id = $selectedCost;
			$new_reg->status = Status::PUBLISHED;
			$new_reg->created_by = "kiosk";
			$new_reg->created_on = date("Y-m-d H:i:s");

			if ($new_reg->validate()) {
				$new_reg->save();
			} else {
				$error = MyActiveForm::_validate($new_reg);

				$arr = (array) json_decode($error);

				foreach ($arr as $value) {
					foreach ($value as $val) {
						$msg .= $val . "<br/>";
					}
				}
			}
		}
	}*/
}
