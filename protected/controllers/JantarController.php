<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\controllers\JantarController;
	use app\models\Status;
	use Yang;

`/yii2-only';


ini_set('max_execution_time', 3600);

class JantarController extends Controller {

	const READER_ID_LENGTH = 1;

	private $defaultEnd;
	private $publishedStatus;

	private $jantarTerminalSettings = [];
	private $currentJantarAction;
	private $sessionId = "";

	private $existingUsersInEaseDb = [];
	private $existingUsersInJantarDb = [];
	private $notExistingUsersInJantarDb = [];
	private $terminatedEmployeesInEaseDb = [];
	private $currentAccessLevelNamesInEaseDb = [];

	private $visitorCardDataInEaseDb = [];

	private $useCodeksTemporaryCards;
	private $useOnlyCodeksGroups;
	private $isTimeAttendance = "false"; // Jantar time management - default false (type string, is using in Jantar API XML request)!

	private $controllers;

	public function actionIndex() {
		$this->jantarTerminalSettings = Yang::getParam('jantarTerminalSettings');
		$this->currentJantarAction = requestParam('action');
		$this->defaultEnd = App::getSetting("defaultEnd");
		$this->publishedStatus = Status::PUBLISHED;
		$this->useCodeksTemporaryCards = App::getSetting("useCodeksTemporaryCards");
		$this->useOnlyCodeksGroups = App::getSetting("useOnlyCodeksGroups");

		if ($this->login())
		{
			switch($this->currentJantarAction)
			{
				case 'user':
					$this->addUserDataToJantarDB();
					break;
				case 'userDelete':
					$this->deleteTerminatedEmployeesFromJantarDb();
					break;
				case 'group':
					$this->addGroupDataToJantarDB();
					break;
				case 'visitor':
					$this->addVisitorCardDataToJantarDB();
					break;
				case 'sendtodevices':
					$this->controllers = $this->getAllActiveJantarControllers();

					if (count($this->controllers) > 0) {
						$this->sendDataToTerminals($this->controllers);
					}

					break;
			}

			$this->logout();
		} else {
			Yang::log('Login was unsuccessful.','error','system.Jantar');
		}
	}

	private function addUserDataToJantarDB()
	{
		$this->existingUsersInEaseDb       = $this->getAllExistingUsersFromEaseDb();

		$counter = count($this->existingUsersInEaseDb);
		if ($counter > 0) {
			for ($i = 0; $i < $counter; $i++) {
				$this->addUser($this->existingUsersInEaseDb[$i]);
			}
		}
	}

	private function deleteTerminatedEmployeesFromJantarDb()
	{
		$this->existingUsersInJantarDb = $this->getUserDataFromJantarDB(null, null);
		$this->terminatedEmployeesInEaseDb = $this->getTerminatedEmployeesFromEaseDb();

		$terminatedUserIdsInJantarDb = [];
		$terminatedUserIdsToDelete = [];

		$num = count($this->terminatedEmployeesInEaseDb);

		if ($num > 0)
		{

			for ($i = 0; $i < $num; $i++)
			{
				$personalId = $this->terminatedEmployeesInEaseDb[$i]['personalid'];

				if (array_key_exists($personalId, $this->existingUsersInJantarDb))
				{
					$terminatedUserIdsInJantarDb[] = [
						'userId' => $this->existingUsersInJantarDb[$personalId]['userId'],
						'personalId' => $personalId
					];
				}
			}

			foreach ($terminatedUserIdsInJantarDb as $userData) {
				if ($this->validateUserForDeletion($userData['personalId'])) {
					$terminatedUserIdsToDelete[] = $userData['userId'];
					Yii::log("User {$userData['personalId']} validated for deletion", 'log', 'test');
				} else {
					Yii::log("User {$userData['personalId']} failed validation for deletion", 'log', 'test');
				}
			}

			foreach ($terminatedUserIdsToDelete as $userId) {
				$this->deleteNotValidUserFromJantarDB($userId);
			}
		}

		return true;
	}

	private function validateUserForDeletion($personalId)
	{
		try {
			if (!$this->isEmployeeTerminated($personalId)) {
				Yii::log("User {$personalId} is not terminated - deletion prevented", 'log', 'system.Jantar');
				return false;
			}

			return true;

		} catch (Exception $e) {
			Yii::log("Validation error for user {$personalId}: " . $e->getMessage(), 'log', 'system.Jantar');
			return false;
		}
	}

	private function isEmployeeTerminated($personalId)
	{
		$SQL = "
			SELECT 
				CASE 
					WHEN MAX(e.`valid_to`) < CURDATE() THEN 1
					ELSE 0
				END as is_terminated
			FROM `employee` e
			WHERE 
					e.`emp_id` = '{$personalId}'
				AND e.`status` = {$this->publishedStatus}
			GROUP BY e.`emp_id`
    	";

		$result = dbFetchValue($SQL);
		return $result == 1;
	}

	private function addGroupDataToJantarDB()
	{
		$this->currentAccessLevelNamesInEaseDb = $this->getAllAccessLevelsFromEaseDB();

		if (count($this->currentAccessLevelNamesInEaseDb) > 0) {
			for ($i = 0;$i < count($this->currentAccessLevelNamesInEaseDb); $i++) {
				$this->addGroupToAccessList($this->currentAccessLevelNamesInEaseDb[$i]);
			}
		}
	}

	private function addVisitorCardDataToJantarDB()
	{
		$this->visitorCardDataInEaseDb = $this->getVisitorCardDataFromEaseDB();

		if (count($this->visitorCardDataInEaseDb) > 0) {
			for ($i = 0; $i < count($this->visitorCardDataInEaseDb); $i++) {
				$this->addUser($this->visitorCardDataInEaseDb[$i]);
			}
		}
	}

	private function createCurlConnection($url, $header, $request, $returnTransfer = 0)
	{
		$curl = curl_init();

		curl_setopt($curl, CURLOPT_HEADER , 0);
		curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
		curl_setopt($curl, CURLOPT_POST, 0);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $request);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, $returnTransfer);
		curl_setopt($curl, CURLOPT_SSL_OPTIONS, CURLSSLOPT_NATIVE_CA);
		//curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		//curl_setopt($curl, CURLOPT_PROXY_SSL_VERIFYPEER, false);
		//curl_setopt($curl, CURLOPT_SSL_VERIFYSTATUS, false);
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_TIMEOUT, 300);
		//curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 300);

		$resultOfCurl = $this->executeCurl($curl,$returnTransfer);
		curl_close($curl);

		return $resultOfCurl;
	}

	private function executeCurl($curl, $returnTransfer)
	{
		$data = curl_exec($curl);

		if ($returnTransfer && $data !== false) {
			return $data;
		} else if (!$returnTransfer && $data !== false) {
			return true;
		} else {
			$error = curl_error($curl);
			Yii::log($error, 'log', 'system.Jantar');
			return false;
		}
	}

	private function login()
	{
		$url = $this->jantarTerminalSettings['host'] . ":" . $this->jantarTerminalSettings['port'] . "/access/login";
		$loginHeader = ['Content-Type: application/xml', 'charset=UTF-8'];

		$loginRequest = "
						<login_request>
							<username>" . $this->jantarTerminalSettings['username'] . "</username>
							<password>" . $this->jantarTerminalSettings['password'] . "</password>
						</login_request>";
		$resultOfCurl = $this->createCurlConnection($url, $loginHeader, $loginRequest, 1);

		if ($resultOfCurl) {
			$response = simplexml_load_string($resultOfCurl);
			$this->sessionId = $response->sessionid;
			return true;
		} else {
			return false;
		}
	}

	private function logout()
	{
		$url = $this->jantarTerminalSettings['host'] . ":" . $this->jantarTerminalSettings['port'] . "/access/logout";

		$logoutHeader = ['POST','/access/logout','HTTP/1.1','Content-Type: application/xml', 'charset=UTF-8',"sessionid: $this->sessionId"];

		$logoutRequest = "<login_request></login_request>";

		$resultOfCurl = $this->createCurlConnection($url, $logoutHeader, $logoutRequest, 0);

		if ($resultOfCurl) {
			$this->sessionId = "";
			return true;
		} else {
			Yang::log('Logout was unsuccessful.','error','system.Jantar');
			return false;
		}
	}

	//group = access_level

	private function addUser($user)
	{
		$updateMode = false;
		$insertMode = false;
		$nochanges = false;

		$cardType = 'UserCard';
		$username = $user["username"];
		$firstName = $user["firstname"];
		$lastName = $user["lastname"];
		$language = $user["language"];

		$currentUsersDataByIdInJantarDb = $this->getUserDataFromJantarDB($user["personalid"], null);

		if (empty($currentUsersDataByIdInJantarDb))
		{
			$currentUsersDataInJantarDb = $this->getUserDataFromJantarDB(null, $user["card_number"]);
		} else {
			$currentUsersDataInJantarDb = $currentUsersDataByIdInJantarDb;
		}

		$cardCondition = 	(
			isset($user["card_number"]) &&
			isset($currentUsersDataInJantarDb["card_number"]) &&
			($user["card_number"] !== $currentUsersDataInJantarDb["card_number"])
		) ? true : false;

		$validFromCondition = 	(
			isset($user["validfrom"]) &&
			isset($currentUsersDataInJantarDb["validfrom"]) &&
			($user["validfrom"] !== $currentUsersDataInJantarDb["validfrom"])
		) ? true : false;

		$validTillCondition = 	(
			isset($user["validtill"]) &&
			isset($currentUsersDataInJantarDb["validtill"]) &&
			($user["validtilldate"] !== $currentUsersDataInJantarDb["validtill"])
		) ? true : false;

		$accessLevelCondition = false;

		if (!$this->useOnlyCodeksGroups)
		{
			$usersGroupId = $this->getAccessLevelIdByName($user["access_level"]);
			$accessLevelCondition = 	(
				isset($user["access_level"]) &&
				isset($currentUsersDataInJantarDb["access_level"]) &&
				($user["access_level"] !== $currentUsersDataInJantarDb["access_level_name"])
			) ? true : false;
		}

		$picture                   = isset($currentUsersDataInJantarDb["picture"]) && $currentUsersDataInJantarDb["picture"] != null ? $currentUsersDataInJantarDb["picture"] : "";
		$isAlternativeActionActive = (int)$currentUsersDataInJantarDb["isAlternativeActionActive"] === 1 ? "true" : "false";

		if (!empty($currentUsersDataInJantarDb) && !empty($user))
		{
			if ($cardCondition || $validFromCondition || $validTillCondition || $accessLevelCondition) {
				$updateMode = true;
			} else {
				$nochanges = true;
			}
		} else if (!empty($user) && empty($currentUsersDataInJantarDb)) {
			$insertMode = true;
		} else {
			$nochanges = true;
		}

		if (!$nochanges)
		{
			$url = $this->jantarTerminalSettings['host'] . ":" . $this->jantarTerminalSettings['port'];

			$url .= ($updateMode) ? "/access/users/update" : "/access/users/add";

			$addUsersHeader = ['POST','HTTP/1.1','Content-Type: application/xml', 'Connection: keep-alive', 'charset=UTF-8', "sessionid: $this->sessionId"];

			$addUsersRequest = ($updateMode) ? '<UsersServiceRequest id="' . $currentUsersDataInJantarDb["userId"] . '">' : '<UsersServiceRequest>';

			if (!$this->useOnlyCodeksGroups) {
				$addUsersRequest .= '
									<group>' . $usersGroupId . '</group>
									<setgroup>true</setgroup>';
			}

			$addUsersRequest .='
									<user ';

			$addUsersRequest .=	($updateMode) ? 'id = "' . $currentUsersDataInJantarDb["userId"] . '" ' : "";
			$addUsersRequest .= '
										username = "' . $username . '" 
										personalid = "' . $user["personalid"] . '" 
										name = "' . $firstName . '" 
										email=""
										lastname = "' . $lastName . '" 
										card = "' . $user["card_number"] . '" 
										externalid="" 
										exportid="" 
										language = "'.$language.'" 
										password="" 
										tastartdate = "' . $user["validfrom"] . '" 
										validfrom = "' . $user["validfrom"] . '" 
										validtill = "' . $user["validtill"] . '" 
										picture = "' . $picture . '"
										editownta="false" 
										editownyeardata="false" 
										processownabsences="false" 
										processownpermits="false" 
										viewowntimeandattendance="false" 
										AllowPlaceReservations="false" 
										AllowGuests="false" 
										IsAlternativeActionActive="' . $isAlternativeActionActive . '"
										pin="" 
										usekeypad="false" 
										master="false" 
										follow="false" 
										hide="false" 
										linkcode="0" 
										cardtype = "' . $cardType . '" 
										isloginallowed = "true" 
										calendarid="0" 
										istimeattendance="' . $this->isTimeAttendance . '" 
										customfield1="" 
										customfield2="" 
										customfield3=""
									></user>
								</UsersServiceRequest>';

			$resultOfCurl = $this->createCurlConnection($url, $addUsersHeader, $addUsersRequest, 0);

			if ($resultOfCurl) {

				return true;
			} else {
				Yang::log('Couldnt add/update any users to JantarDB', 'error', 'system.Jantar');
				return false;
			}
		}
	}

	private function deleteNotValidUserFromJantarDB($jantarUserId)
	{
		$url = $this->jantarTerminalSettings['host'] . ":" . $this->jantarTerminalSettings['port'] . "/access/users/delete";

		$deleteUserHeader = ['POST','HTTP/1.1','Content-Type: application/xml', 'Connection: keep-alive', 'charset=UTF-8', "sessionid: $this->sessionId"];

		$deleteUserRequest = '<UsersServiceRequest id="' . $jantarUserId . '"></UsersServiceRequest>';

		$resultOfCurl = $this->createCurlConnection($url, $deleteUserHeader, $deleteUserRequest, 0);

		if ($resultOfCurl) {
			return true;
		} else {
			Yang::log('Couldnt delete user from JantarDB', 'error', 'system.Jantar');
			return false;
		}
	}

	private function getUserDataFromJantarDB($employeeId = null, $cardNumber = null)
	{
		$userDataInJantarDb = [];

		$dbConnection = Yii::app()->JantarDB;

		$SQL = "
            SELECT 
                userTable.[Id] as userId,
                userTable.[Card] as card_number,
                userTable.[Picture] as picture,
                userTable.[PersonalId] as personalId,
                CONVERT(varchar, userTable.[ValidFrom], 23) as validfrom,
                CONVERT(varchar, userTable.[ValidTill], 23) as validtill,
                accessgroup.[aid] as access_level,
				accessCollections.[Name] as access_level_name,
				userTable.[IsAlternativeActionActive] as isAlternativeActionActive
            FROM
                [codeksDatabase].[dbo].[Users] as userTable
            LEFT JOIN
                [codeksDatabase].[dbo].[UserAccessGroup] as accessgroup ON
                userTable.[Id] = accessgroup.[codeksuid]
                AND accessgroup.[Deleted] <> 1
			INNER JOIN
				[codeksDatabase].[dbo].[AccessesCollections] as accessCollections ON accessgroup.[aid] = accessCollections.[Id]	
            WHERE 
					userTable.[Deleted] <> 1
        ";

		if ($employeeId !== null)
		{
			$SQL .= "
					AND userTable.[PersonalId] = '{$employeeId}'
					";
		}

		if ($cardNumber !== null)
		{
			$SQL .= "
					AND userTable.[Card] = '{$cardNumber}'
					";
		}

		$results = $dbConnection->createCommand($SQL)->queryAll();


		if (count($results) > 0)
		{
			if ($employeeId !== null || $cardNumber !== null) {
				$userDataInJantarDb = $results[0];
				unset($this->existingUsersInJantarDb[$results[0]['personalId']]);
			} else {
				for ($i = 0; $i < count($results); $i++) {
					$userDataInJantarDb[$results[$i]['personalId']] = [
						'userId'		=> $results[$i]['userId'],
						'personalId'	=> $results[$i]['personalId'],
						'card'			=> $results[$i]['card_number'],
						'picture'	    => $results[$i]['picture']
					];
				}
			}
		}

		return $userDataInJantarDb;
	}

	/**
	 * Get not existing user data from Jantar DB - for delete
	 *
	 * @return array
	 */
	private function getNotExistingUserDataFromJantarDB()
	{
		$notExistingUserDataInJantarDb = [];

		$dbConnection = Yii::app()->JantarDB;

		$SQL = "
            SELECT 
                userTable.[Id] as userId
            FROM
                [codeksDatabase].[dbo].[Users] as userTable
            LEFT JOIN
                [codeksDatabase].[dbo].[UserAccessGroup] as accessgroup ON
                userTable.[Id] = accessgroup.[codeksuid]
                AND accessgroup.[Deleted] <> 1
			INNER JOIN
				[codeksDatabase].[dbo].[AccessesCollections] as accessCollections ON accessgroup.[aid] = accessCollections.[Id]	
            WHERE 
                userTable.[Deleted] <> 1
                AND userTable.ValidTill < GETUTCDATE()
                AND userTable.ValidTill <> '0001-01-01'
            ORDER BY
                userTable.[Id] 
        ";

		$results = $dbConnection->createCommand($SQL)->queryAll();

		if (count($results) > 0)
		{
			for ($i = 0; $i < count($results); $i++) {
				$notExistingUserDataInJantarDb[$i]['userId'] = $results[$i]['userId'];
			}
		}

		return $notExistingUserDataInJantarDb;
	}

	private function getAllActiveJantarControllers()
	{
		$controllers = [];

		$dbConnection = Yii::app()->JantarDB;

		$SQL = "
				SELECT 
					[Id]
				FROM [codeksDatabase].[dbo].[Controllers]
				WHERE [Deleted] = 0
				";

		$results = $dbConnection->createCommand($SQL)->queryAll();

		if (count($results) > 0)
		{
			for ($i = 0; $i < count($results); $i++) {
				$controllers[] = $results[$i]['Id'];
			}
		}

		return $controllers;
	}

	private function getGroupAccessRevision($groupId)
	{
		$accessRevisionId = '';

		$dbConnection = Yii::app()->JantarDB;

		$SQL = "SELECT [Id] FROM [codeksDatabase].[dbo].[AccessRevisions] WHERE [GroupId] = '{$groupId}'";

		$results = $dbConnection->createCommand($SQL)->queryAll();

		if (count($results) > 0) {
			$accessRevisionId = $results[0]['Id'];
		}

		return $accessRevisionId;
	}

	private function addGroupToAccessList($accessLevelName)
	{
		$currentAccessLevelDetails = $this->getAccessLevelDetailsFromEaseDB($accessLevelName);
		$currentAccessLevelId = $this->getAccessLevelIdByName($accessLevelName);
		$accessRevisionId = $this->getGroupAccessRevision($currentAccessLevelId);
		$url = $this->jantarTerminalSettings['host'] . ":" . $this->jantarTerminalSettings['port'] . "/access/access/updateaccesslist";

		$addGroupToAccessListHeader = ['POST','HTTP/1.1','Content-Type: application/xml', 'charset=UTF-8', "sessionid: $this->sessionId"];

		$addGroupToAccessListRequest = '
				<accesseslist_request 
						AccessRevisionId="' . $accessRevisionId . '" 
						accesscollectionid="' . $currentAccessLevelId . '" 
						TimetableId="0" 
						CalendarId="0" 
						RegionId="0"
				>';

		for ($i = 0; $i < count($currentAccessLevelDetails); $i++)
		{
			$placeId = $this->getPlaceIdByTerminalAndReaderId($currentAccessLevelDetails[$i]['terminal_id']);

			$addGroupToAccessListRequest .= '
					<access_request AccessRevisionId="' . $accessRevisionId . '" TimetableId="0" CalendarId="0" RegionId="0">
						<parent>' . $placeId . '</parent>
						<access
							id="0"
							user="0"
							group="' . $currentAccessLevelId . '"
							place="' . $placeId . '"
							disable="false"
							time="2"
							action="1"
							macro="0"
							dynamicaccess="0"
							cleardynamicaccess="false"
							EnableAlternativeAction="false"
							AlternativeAction="0"
							AlternativeMacro="0"
							AccessRevisionId="' . $accessRevisionId . '"	
						></access>
					</access_request>';
		}

		$addGroupToAccessListRequest .= '
				</accesseslist_request>';

		$resultOfCurl = $this->createCurlConnection($url, $addGroupToAccessListHeader, $addGroupToAccessListRequest, 0);

		if ($resultOfCurl) {
			return true;
		} else {
			Yang::log('Couldnt add group to access list.', 'error', 'system.Jantar');
			return false;
		}
	}

	/*
	 * GROUP TYPES:
	 *		'TimeAttendance': Munkaidő-nyilvántartó csoport,
	 *		'AccessControl': Beléptető csoport,
	 *		'KeyManager': Kulcsmenedzser csoport,
	 *		'RelayController': Relé vezérlő csoport,
	 *		'FrontDeskVisitors': FrontDesk visitors collection
	 */

	private function addGroupToJantarDB($groupType,$groupName)
	{
		$url = $this->jantarTerminalSettings['host'] . ":" . $this->jantarTerminalSettings['port'] . "/access/access/creategroup";

		$addGroupHeader = ['POST','HTTP/1.1','Content-Type: application/xml', 'charset=UTF-8', "sessionid: $this->sessionId"];

		$addGroupRequest = '
							<access_request>
								<name>' . $groupName . '</name>
								<accesscollectiontype>' . $groupType . '</accesscollectiontype>
								<keypadid>0</keypadid>
								<timetableid>0</timetableid>
							</access_request>
							';

		$resultOfCurl = $this->createCurlConnection($url, $addGroupHeader, $addGroupRequest, 0);

		if ($resultOfCurl) {
			return true;
		} else {
			Yang::log('Couldnt add group to db.', 'error', 'system.Jantar');
			return false;
		}
	}

	private function sendDataToTerminals($controllers)
	{
		$url = $this->jantarTerminalSettings['host'] . ":" . $this->jantarTerminalSettings['port'] . "/access/monitor/sendtables";

		$sendDataHeader = ['POST','HTTP/1.1','Content-Type: application/xml', 'charset=UTF-8', "sessionid: $this->sessionId"];

		$sendDataRequest = '
							<monitor_request>
								<dummy>dummy</dummy>
								<reset>false</reset>
								<SendKeypads>false</SendKeypads>
								<sendall>false</sendall>';
		for ($i = 0; $i < count($controllers); $i++) {
			$sendDataRequest .= '<controllerid>' . $controllers[$i] . '</controllerid>';
		}

		$sendDataRequest .= '</monitor_request>';

		$resultOfCurl = $this->createCurlConnection($url, $sendDataHeader, $sendDataRequest, 0);

		if ($resultOfCurl) {
			return true;
		} else {
			Yang::log('Couldnt send user data to terminals.', 'error', 'system.Jantar');
			return false;
		}
	}

	private function getAccessLevelIdByName($accessLevelName)
	{
		$accessLevelId = "";

		$dbConnection = Yii::app()->JantarDB;

		$SQL = "SELECT 
					[Id] as access_level_id
				FROM [codeksDatabase].[dbo].[AccessesCollections] 
				WHERE 
					[Name] = '" . $accessLevelName . "'
				";

		$results = $dbConnection->createCommand($SQL)->queryAll();

		if (count($results) > 0) {
			$accessLevelId = $results[0]['access_level_id'];
		}

		return $accessLevelId;
	}

	private function getPlaceIdByTerminalAndReaderId($terminalAndReaderId)
	{
		$terminalId = substr($terminalAndReaderId, 0, -JantarController::READER_ID_LENGTH);
		$readerId = substr($terminalAndReaderId, -1, JantarController::READER_ID_LENGTH);

		$placeId = "";

		$dbConnection = Yii::app()->JantarDB;

		$SQL = "
				SELECT 
					[ConnectedPlace] as place_id
				FROM [codeksDatabase].[dbo].[Readers] 
				WHERE 
						[Parent] = '" . $terminalId . "'
					AND [ReaderNumber] = '" . $readerId . "'
					AND [Deleted] <> 1
				";

		$results = $dbConnection->createCommand($SQL)->queryAll();

		if (count($results) > 0) {
			$placeId = $results[0]['place_id'];
		}

		return $placeId;
	}

	private function getAllAccessLevelsFromEaseDB()
	{
		$accessLevels = [];

		$SQL = "SELECT * FROM `ac_access_level` WHERE `status` = " . Status::PUBLISHED . "";

		$results = dbFetchAll($SQL);

		if (count($results) > 0) {
			for ($i = 0; $i < count($results); $i++) {
				$accessLevels[] = $results[$i]['ac_access_level_name'];
			}
		}

		return $accessLevels;
	}

	private function getAccessLevelDetailsFromEaseDB($accessLevelName)
	{
		$accessLevelDetails = [];

		$SQL = "
				SELECT 
					pal.*
				FROM `pcs_access_level` pal
				LEFT JOIN `ac_access_level` aal ON
						pal.`acl_id` = aal.`ac_access_level_id`
					AND aal.`status` = {$this->publishedStatus}
				WHERE
						aal.`ac_access_level_name` = '" . $accessLevelName . "'
					AND	pal.`status` = {$this->publishedStatus}
					AND CURDATE() BETWEEN pal.`valid_from` AND IFNULL(pal.`valid_to`,'{$this->defaultEnd}')
				";

		$results = dbFetchAll($SQL);

		if (count($results) > 0) {
			$accessLevelDetails = $results;
		}

		return $accessLevelDetails;
	}

	private function getVisitorCardDataFromEaseDB()
	{
		$cardDataFromEase = [];

		$SQL = "
				SELECT
					v.`visitor_name` as username,
					'visitor' as card_type,
					v.`visitor_id` as personalid,
					'hu' as language,
					v.`visitor_name` as firstname,
					'VISITOR' as lastname,
					ecard.`card` as card_number,
					access_level.`ac_access_level_name` as access_level,
					ecard.`valid_from` as validfrom,
					IF(ecard.`valid_from` = ecard.`valid_to`,DATE_ADD(ecard.`valid_from`, INTERVAL 1 DAY),IFNULL(ecard.`valid_to`,'{$this->defaultEnd}')) as validtill
				FROM `visitor` v
				LEFT JOIN `employee_card` ecard ON
						ecard.`employee_contract_id` = v.`visitor_id`
					AND ecard.`status` = {$this->publishedStatus}
					AND CURDATE() BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`,'{$this->defaultEnd}')
					AND ecard.`card_type` = 3
				LEFT JOIN `ac_access_level` access_level ON
						ecard.`acl` = access_level.`ac_access_level_id`
					AND access_level.`status` = {$this->publishedStatus}
				WHERE
						v.`status` = {$this->publishedStatus}
					AND CURDATE() BETWEEN v.`valid_from` AND IFNULL(v.`valid_to`,'{$this->defaultEnd}')
					AND ecard.`card` IS NOT NULL
					AND access_level.`ac_access_level_name` IS NOT NULL
				";

		$results = dbFetchAll($SQL);

		if (is_array($results) && count($results) > 0) {
			$cardDataFromEase = $results;
		}

		return $cardDataFromEase;
	}

	private function getAllExistingUsersFromEaseDb()
	{
		$existingUsersFromEaseDB = [];

		$untilYesterdayExistedUsers = $this->getUntilYesterdayExistedUsersFromEaseDB();
		$todayExistingUsers = $this->getTodayExistingUsersFromEaseDB();

		$allActiveCardsToday = array_column($todayExistingUsers, 'card_number');

		if (date('H') < 12) {
			for ($i = 0; $i < count($untilYesterdayExistedUsers); $i++)
			{
				if (!in_array($untilYesterdayExistedUsers[$i]["card_number"], $allActiveCardsToday)) {
					$untilYesterdayExistedUsers[$i]["validtill"] = date("Y-m-d") . "T12:00:00";
				}
			}
		}

		$existingUsersFromEaseDB = array_merge($untilYesterdayExistedUsers, $todayExistingUsers);

		return $existingUsersFromEaseDB;
	}

	private function getUntilYesterdayExistedUsersFromEaseDB()
	{
		$validToCondition = " AND DATE_ADD(CURDATE(), INTERVAL -1 DAY) = e.`valid_to`";

		$untilYesterdayExistedUsers = $this->getUsersFromEaseDB('yesterday', $validToCondition);

		return $untilYesterdayExistedUsers;
	}

	private function getTodayExistingUsersFromEaseDB()
	{
		$todayExistingUsers = $this->getUsersFromEaseDB('onlyToday');

		return $todayExistingUsers;
	}

	private function getUsersFromEaseDB(string $dateParam, string $extraCondition = '')
	{
		$usersFromEase = [];

		$withoutTemporaryCardsCondition = " AND ecard.`card_type` != 2 ";

		$validTillDate = "IF(ecard.`valid_from` = ecard.`valid_to`,DATE_ADD(ecard.`valid_from`, INTERVAL 1 DAY),IFNULL(ecard.`valid_to`,'{$this->defaultEnd}'))";

		$SQL = "
				SELECT 
					IFNULL(u.`username`, e.`emp_id`) as username,
					e.`emp_id` as personalid,
					IFNULL(u.`lang`,'hu') as language,
					IFNULL(e.`first_name`,'') as firstname,
					IFNULL(e.`last_name`,'') as lastname,
					ecard.`card` as card_number,
					'normal' as card_type,";

		if (!$this->useOnlyCodeksGroups) {
			$SQL .= "access_level.`ac_access_level_name` as access_level,";
		}

		$SQL .= "
					ecard.`valid_from` as validfrom,
					CONCAT(
						{$validTillDate},
						'T',
						'23:59:59'
					) as validtill,
					{$validTillDate} as validtilldate
				FROM `employee` e
				LEFT JOIN `user` u ON
						e.`employee_id` = u.`employee_id`
					AND u.`status` = {$this->publishedStatus}
					AND (" . $this->getDateCondition("u",[['from' => 'valid_from', 'to' => 'valid_to']],$dateParam) . ")
				LEFT JOIN `employee_contract` ec ON
						e.`employee_id` = ec.`employee_id`
					AND ec.`status` = {$this->publishedStatus}
					AND (" . $this->getDateCondition("ec",[['from' => 'valid_from', 'to' => 'valid_to'],['from' => 'ec_valid_from', 'to' => 'ec_valid_to']],$dateParam) . ")
				LEFT JOIN `employee_card` ecard ON
						ec.`employee_contract_id` = ecard.`employee_contract_id`
					AND ecard.`status` = {$this->publishedStatus}
					AND (" . $this->getDateCondition("ecard",[['from' => 'valid_from', 'to' => 'valid_to']],$dateParam) . ")";

		if (!$this->useOnlyCodeksGroups)
		{
			$SQL .= "
				LEFT JOIN `ac_access_level` access_level ON
						ecard.`acl` = access_level.`ac_access_level_id`
					AND access_level.`status` = {$this->publishedStatus}";
		}

		$SQL .= "
				WHERE 
						e.`status` = {$this->publishedStatus}
					AND (" . $this->getDateCondition("e",[['from' => 'valid_from', 'to' => 'valid_to']],$dateParam) . ")
					{$extraCondition}
					AND ecard.`card` IS NOT NULL";

		if ($this->useCodeksTemporaryCards) {
			$SQL .= $withoutTemporaryCardsCondition;
		}

		if (!$this->useOnlyCodeksGroups)
		{
			$SQL .= "
					AND access_level.`ac_access_level_name` IS NOT NULL
				";
		}

		$results = dbFetchAll($SQL);

		if (is_array($results) && count($results) > 0) {
			$usersFromEase = $results;
		}

		return $usersFromEase;
	}

	private function getTerminatedEmployeesFromEaseDb()
	{
		$results = [];

		$SQL = "
				SELECT 
					e.`emp_id` as personalid,
					MAX(e.valid_from) as max_valid_from,
					MAX(e.valid_to) as max_valid_to
				FROM `employee` e
				WHERE 
					e.`status` = {$this->publishedStatus}
				GROUP BY e.emp_id
				HAVING
					MAX(e.`valid_to`) < CURDATE()
				ORDER BY personalid";

		$results = dbFetchAll($SQL);

		return $results;
	}

	private function getDateCondition(string $table = null, array $validFields = [], string $daysToCheck = 'onlyToday')
	{
		$dateCondition = "";
		$dateConditionElements = [];

		if ($table === null) {
			return "";
		}

		switch($daysToCheck) {
			case 'yesterdayTodayAndTomorrow':
				$dates = ["DATE_ADD(CURDATE(), INTERVAL -1 DAY) ", "CURDATE() ", "DATE_ADD(CURDATE(), INTERVAL 1 DAY) "];
				break;
			case 'yesterdayAndToday':
				$dates = ["DATE_ADD(CURDATE(), INTERVAL -1 DAY) ", "CURDATE() "];
				break;
			case 'todayAndTomorrow':
				$dates = ["CURDATE() ", "DATE_ADD(CURDATE(), INTERVAL 1 DAY) "];
				break;
			case 'yesterday':
				$dates = ["DATE_ADD(CURDATE(), INTERVAL -1 DAY) "];
				break;
			case 'tomorrow':
				$dates = ["DATE_ADD(CURDATE(), INTERVAL 1 DAY) "];
				break;
			default:
				$dates = ["CURDATE() "];
		}

		for ($i = 0; $i < count($validFields); $i++)
		{
			foreach ($dates as $date) {
				$dateConditionElements[$i][] = $date . " BETWEEN " . $table . "." . $validFields[$i]['from'] . " AND IFNULL(" . $table . "." . $validFields[$i]['to'] . ",'{$this->defaultEnd}')";
			}
			$dateConditionElements[$i] = implode(" OR ", $dateConditionElements[$i]);
		}

		$dateCondition = implode(" AND ", $dateConditionElements);

		return $dateCondition;
	}
}
