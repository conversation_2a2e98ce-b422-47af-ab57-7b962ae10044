<?php

class OptionController extends Grid2Controller
{
	public function __construct()
	{
		parent::__construct("option");
	}
	
	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("Option");

		parent::setControllerPageTitleId("page_title_option");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);

		$this->LAGridRights->overrideInitRights("export_xls",		false);
		$this->LAGridRights->overrideInitRights("export_xlsx",		false);
		$this->LAGridRights->overrideInitRights("export_pdf_node",	false);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);

		$this->LAGridDB->enableSQLMode();

		$SQL ="
			SELECT
				`row_id`,
				`option_id`,
				`option_value`
			FROM `option` o
			WHERE
					o.`status`=".Status::PUBLISHED."
				AND (`option_id`='{option_id}' OR '{option_id}'='' OR '{option_id}'='ALL')
			";

		$this->LAGridDB->setSQLSelection($SQL, "row_id");
		parent::G2BInit();
	}
	
	public function search()
	{	
		return array(
			 'option_id'	=> array(
									'col_type'	=> 'combo',
									'options'	=>	array(
														'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
														'sql'	=> "
																SELECT
																	option_id as `id`,
																	d.`dict_value` as `value`
																FROM `option_config` oc
																LEFT JOIN `dictionary` d ON
																		d.`dict_id`=oc.option_id
																	AND d.`valid`=1
																	AND d.`lang`='".Dict::getLang()."'
																WHERE
																		oc.`status`=".Status::PUBLISHED."
																	AND oc.`type`='".OptionConfig::TYPE_COMBO."'
																ORDER BY option_id",
															'array'		=> array(array("id"=>"ALL","value"=>Dict::getValue("all")))),
											'label_text'=>Dict::getValue("option"),
											'default_value'=>'ALL',
											),
                'submit'	=> array('col_type'=>'searchBarReloadGrid', 'gridID' => 'dhtmlxGrid','width'=>'*', 'label_text'=>''),
		);
	}
	
	public function columns()
	{	
		return array(
			'option_id'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'combo','width' => 300,
										'options'	=> array(
															'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
															'sql'	=> "
																		SELECT
																			option_id as `id`,
																			d.`dict_value` as `value`
																		FROM `option_config` oc
																		LEFT JOIN `dictionary` d ON
																				d.`dict_id`=option_id
																			AND d.`valid`=1
																			AND d.`lang`='".Dict::getLang()."'
																		WHERE
																				oc.`status`=".Status::PUBLISHED."
																			AND oc.`type`='".OptionConfig::TYPE_COMBO."'
																		ORDER BY option_id
																	",
															'array'	=> array(array("id"=>"","value"=>"")),)),
			'option_value'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 300),
		);
	}
}
?>