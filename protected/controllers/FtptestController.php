<?php

'yii2-only`;

	namespace app\controllers;
	use Yang;

`/yii2-only';


#yii2: done

class FtptestController extends Controller
{
	public function actionIndex()
	{
		ini_set('display_errors', 1);

		echo "FTP Test"."<BR>";

		$ftp_address   = requestParam('address');
		$ftp_user_name = requestParam('user');
		$ftp_user_pass = requestParam('pass');
		$timeout		= 10;

		$ftp_port      = requestParam('port');

//		$ftp_address	= "ttwa.login.hu";
//		$ftp_user_name	= "ftp";
//		$ftp_user_pass	= "Kacsacsor1";
//		$ftp_port		= 21;

		if(!empty($ftp_address) && !empty($ftp_user_name) && !empty($ftp_user_pass) && !empty($ftp_port)){
		
			$conn_id = ftp_connect($ftp_address, $ftp_port, $timeout) or die("FTP Couldn't connect to $ftp_address");
			echo "conn_id: ".$conn_id."<BR>";
			$login_result = ftp_login($conn_id, $ftp_user_name, $ftp_user_pass);
			echo "login_result: ".$login_result."<BR>";
			if ((!$conn_id) || (!$login_result)) {
				die("FTP connection has failed !");
			}

			ftp_pasv($conn_id, true);
			$contents = ftp_nlist($conn_id, ".");
			var_dump($contents);

		} else {
			var_dump("Missing params!");
		}
	/*	
		$ftp_port = 29652;

		$conn_id = ftp_connect($ftp_address, $ftp_port, $timeout) or die("FTP Couldn't connect to $ftp_address");
		echo "conn_id: ".$conn_id."<BR>";
		$login_result = ftp_login($conn_id, $ftp_user_name, $ftp_user_pass);
		echo "login_result: ".$login_result."<BR>";
		if ((!$conn_id) || (!$login_result)) {
			die("FTP connection has failed !");
		}

		ftp_pasv($conn_id, true);
		$contents = ftp_nlist($conn_id, ".");
		var_dump($contents);

	*/
		
	}
}
	
	?>