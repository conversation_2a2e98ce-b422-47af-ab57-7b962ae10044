<?php

declare(strict_types=1);

use Components\Grid2Core\Builder\Grid2ColumnDescriptorBuilder;
use Components\Grid2Core\Transformer\Grid2ColumnDescriptorToArrayTransformer;
use Components\SchedulingAssistantCore\Enum\SchedulingAssistantGroupTypeEnum;
use Components\SchedulingAssistantCore\Provider\SchedulingAssistantGroupTypeProvider;
use Components\SchedulingAssistantInfeasibleConstraintList\Builder\InfeasibleConstraintListBuilder;
use Components\SchedulingAssistantInfeasibleConstraintList\Builder\InfeasibleConstraintModelsToListBuilder;

Yang::loadComponentNamespaces('SchedulingAssistantInfeasibleConstraintList');
Yang::loadComponentNamespaces('SchedulingAssistantCore');
Yang::loadComponentNamespaces('WorkScheduleCore');
Yang::loadComponentNamespaces('Grid2Core');
Yang::loadComponentNamespaces('Employee');
Yang::loadComponentNamespaces('Competency');
Yang::loadComponentNamespaces('Core');

class ReportSchedulingAssistantSolverInfeasibleConstraintController extends Grid2Controller
{
    public const CONTROLLER_ID = "SchedulingAssistant/reportSchedulingAssistantSolverInfeasibleConstraint";
    public const SESSION_PROCESS_ID = 'ReportSchedulingAssistantSolverInfeasibleConstraint_processId';
    public const SEARCHFIELD_PROCESS_ID = 'processId';

    private array $dayTypeGroup;
    private array $daytype;
    private array $competency;
    private array $workgroup;

    public function __construct()
    {
        parent::__construct(self::CONTROLLER_ID);
        Yang::unsetSessionValue(self::SESSION_PROCESS_ID);
        $this->daytype = (new Daytype())->getAllData();
        $this->dayTypeGroup = (new DaytypeGroup())->getAllData();
        $this->competency = (new Competency())->getAllData();
        $this->workgroup = (new Workgroup())->getAllData();
    }

    public function columns(): array
    {
        $ruleSql = $this->getRuleSql();
        $transformer = new Grid2ColumnDescriptorToArrayTransformer();
        $builder = new Grid2ColumnDescriptorBuilder();
        $columnDescriptors = [];
        $columnDescriptors[] = $builder->reset()
            ->setName(InfeasibleConstraintModelsToListBuilder::FIELD_DATE)
            ->enableGrid()
            ->enableExport()
            ->setReadOnlyColType()
            ->setAlign('center')
            ->setWidth(100)
            ->setExportAs(InfeasibleConstraintModelsToListBuilder::FIELD_DATE)
            ->build();
        $columnDescriptors[] = $builder->reset()
            ->setName(InfeasibleConstraintModelsToListBuilder::FIELD_EMPLOYEE_NAME)
            ->enableGrid()
            ->enableExport()
            ->setReadOnlyColType()
            ->setAlign('left')
            ->setWidth(200)
            ->build();
        $columnDescriptors[] = $builder->reset()
            ->setName(InfeasibleConstraintModelsToListBuilder::FIELD_DATA)
            ->enableGrid()
            ->enableExport()
            ->setReadOnlyColType()
            ->setAlign('left')
            ->setMaxWidth()
            ->build();
        $columns = [];
        foreach ($columnDescriptors as $columnDescriptor) {
            $columns[$columnDescriptor->getName()] = $transformer->transform($columnDescriptor);
        }

        return $columns;
    }

    private function getRuleSql(): string
    {
        return "
			SELECT 
				`lookup_value` as id,
				`dict_value` as value
			FROM
				`app_lookup`
			LEFT JOIN `dictionary` ON
					`app_lookup`.`dict_id` 	= `dictionary`.`dict_id`
					AND `dictionary`.`valid`	= 1
					AND `dictionary`.`lang`		= '" . Dict::getLang() . "'
			WHERE
					`app_lookup`.`valid`		= 1
					AND `app_lookup`.`lookup_id`	= 'SchedulingAssistantRuleId'
			ORDER BY
				`dict_value`
		";
    }

    public function attributeLabels(): array
    {
        return [
            InfeasibleConstraintModelsToListBuilder::FIELD_DATE => Dict::getValue('date'),
            InfeasibleConstraintModelsToListBuilder::FIELD_EMPLOYEE_NAME => Dict::getValue('fullname'),
            InfeasibleConstraintModelsToListBuilder::FIELD_DATA => Dict::getValue(
                'SchedulingAssistantRuleSettingsFieldName'
            ),
        ];
    }

    protected function G2BInit()
    {
        $this->setControllerPageTitleId(
            "page_title_reportSchedulingAssistantSolverInfeasibleConstraint"
        );
        $this->setExportFileName(
            Dict::getValue(
                "page_title_reportSchedulingAssistantSolverInfeasibleConstraint"
            )
        );
        $this->maxDays = 365;

        $this->LAGridRights->overrideInitRightSwitches(
            '

		[x]  paging
		[x]  search
		[x]  search header
		[x]  select
		[x]  column move
		[x]  col sorting
		[x]  reload sortings
		[x]  init open search
		[x]  export xlsx
		[ ]  export csv
		[ ]  export xls
		[ ]  details
		[ ]  multi select
		[ ]  export_pdf_node

		'
        );

        if (!empty(requestParam(self::SEARCHFIELD_PROCESS_ID)) ||
            !empty($this->getProcessIdFromSession())
        ) {
            $this->LAGridRights->overrideInitRights("init_open_search", false);
        }

        $this->LAGridDB->enableArrMode();

        parent::G2BInit();
    }

    protected function getProcessIdFromSession(): string
    {
        return Yang::session(self::SESSION_PROCESS_ID, '');
    }

    protected function search(): array
    {
        $this->setProcessIdToSession(requestParam(self::SEARCHFIELD_PROCESS_ID) ?? '');
        $processId = $this->getProcessIdFromSession();
        $search = [];
        $submit = ['submit' => ['col_type' => 'searchBarReloadGrid', 'width' => '*', 'label_text' => '']];

        $art = new ApproverRelatedGroup;
        $gargSQL = $art->getApproverReleatedGroupSQL("Company", "workSchedule", false, "NOW()");
        $gargSQL["where"] = $gargSQL["where"] ?? "";

        $published = Status::PUBLISHED;
        $backOneMonth = Date("Y-m-d", strtotime("-1 month", strtotime(date("F") . "1")));
        $groupType = (new SchedulingAssistantGroupTypeProvider())->provide();
        if (($groupType === SchedulingAssistantGroupTypeEnum::COMPANY)) {
            $gargSQL = $art->getApproverReleatedGroupSQL("Company", "workSchedule", false, "NOW()");
            $gargSQL["where"] = $gargSQL["where"] ?? "";
            $sql = sprintf(
                "			
			SELECT 	proc.process_id as id,
					CONCAT(proc.process_id, ' - ', company.company_name) as value,
					proc.*
			FROM scheduling_assistant_solver_process proc
			LEFT JOIN company ON 
			    	proc.group_id IN ('company_id', 'company') 				
			    AND proc.group_value = company.company_id
				AND company.status = %d
				AND proc.to_day BETWEEN company.valid_from AND default_end(company.valid_to)
			WHERE 	proc.solving_status = 3
				AND	DATE(proc.created_on) >= DATE('%s')
				%s			
			ORDER BY proc.process_id DESC LIMIT 100;
		",
                $published,
                $backOneMonth,
                $gargSQL["where"]
            );
        } elseif ($groupType === SchedulingAssistantGroupTypeEnum::UNIT) {
            $gargSQL = $art->getApproverReleatedGroupSQL("Unit", "workSchedule", false, "NOW()");
            $gargSQL["where"] = $gargSQL["where"] ?? "";
            $sql = sprintf(
                "			
			SELECT 	proc.process_id as id,
					CONCAT(proc.process_id, ' - ', unit.unit_name) as value,
					proc.*
			FROM scheduling_assistant_solver_process proc
			LEFT JOIN unit ON 
			    	proc.group_id IN ('unit_id', 'unit') 				
			    AND proc.group_value = unit.unit_id
				AND unit.status = %d
				AND proc.to_day BETWEEN unit.valid_from AND default_end(unit.valid_to)
			WHERE 	proc.solving_status = 3
				AND	DATE(proc.created_on) >= DATE('%s')
				%s			
			ORDER BY proc.process_id DESC LIMIT 100;
		",
                $published,
                $backOneMonth,
                $gargSQL["where"]
            );
        }

        $process[self::SEARCHFIELD_PROCESS_ID] = [
            'label_text' => Dict::getValue("process_id"),
            'col_type' => 'combo',
            'options' => [
                'mode' => Grid2Controller::G2BC_QUERY_MODE_SQL,
                'sql' => $sql,
            ],
            'default_value' => $processId,
        ];

        return Yang::arrayMerge($search, $process, $submit);
    }

    protected function setProcessIdToSession($processId): void
    {
        if (!empty($processId)) {
            Yang::setSessionValue(self::SESSION_PROCESS_ID, $processId);
        }
    }

    protected function dataArray($gridID, $filter): array
    {
        return (new InfeasibleConstraintListBuilder())->build(
            is_array($filter) ? $filter : []
        );
    }

    protected function getSQL($filter): string
    {
        $whereFromFilters = $this->getSqlWhereFromFilters($filter);
        $processFromRequestId = $this->getProcessIdFromSession();
        $processId = empty($processFromRequestId) ? $filter[self::SEARCHFIELD_PROCESS_ID] ?? '' : $processFromRequestId;
        $whereProcessId = (!empty($processId)) ? "AND (`ic`.`process_id` = '" . $processId . "')" : "AND 1=0";

        return '
			SELECT  employee.emp_id,
			' . Employee::getParam("fullname_with_emp_id", "employee") . ' as employee_name,
			sic.*
			FROM
			(
				SELECT
					data->>"$.rule_id" as data_rule_id,
					IF (data->>"$.day" IS NOT NULL,
						data->>"$.day",
						IF (data->>"$.period_begin" IS NOT NULL,
							data->>"$.period_begin",
							IF (data->>"$.year" IS NOT NULL AND data->>"$.month" IS NOT NULL,
								CONCAT( data->>"$.year", "-",
										IF (LENGTH(data ->> "$.month") = 1,
										   CONCAT("0", data ->> "$.month"),
										   data ->> "$.month"
										),
										"-01"
								),
								IF (data->>"$.frame_begin" IS NOT NULL,
									data->>"$.frame_begin",
									""
								)
							)
						)
					) as valid_from,
					IF (data->>"$.day" IS NOT NULL,
						data->>"$.day",
						IF (data->>"$.period_end" IS NOT NULL,
							data->>"$.period_end",
							IF (data->>"$.year" IS NOT NULL AND data->>"$.month" IS NOT NULL,
								LAST_DAY(
									CONCAT(data ->> "$.year", "-",
										   IF(LENGTH(data ->> "$.month") = 1,
											  CONCAT("0", data ->> "$.month"),
											  data ->> "$.month"
											   ),
										   "-01"
										)
								),
								IF (data->>"$.frame_end" IS NOT NULL,
									data->>"$.frame_end",
									""
								)
							)
						)
					) as valid_to,
				   ic.*
				FROM scheduling_assistant_solver_infeasible_constraint ic
				WHERE 1 ' . $whereProcessId . '
			) sic
			LEFT JOIN employee_contract ON
					employee_contract.`employee_contract_id` = data->>"$.employee_id"
				AND employee_contract.`status` = ' . Status::PUBLISHED . '
				AND (       sic.`valid_from`
									BETWEEN     GREATEST(employee_contract.`valid_from`, employee_contract.`ec_valid_from`)
											AND LEAST(default_end(employee_contract.`valid_to`), default_end(employee_contract.`ec_valid_to`))
						OR  GREATEST(employee_contract.`valid_from`, employee_contract.`ec_valid_from`)
									BETWEEN     sic.`valid_from` 
											AND default_end(sic.`valid_to`)
					)
			LEFT JOIN employee ON
					employee.employee_id = employee_contract.employee_id
				AND employee.status = ' . Status::PUBLISHED . '
				AND (	sic.`valid_from`
						BETWEEN     
							GREATEST(employee.`valid_from`, employee_contract.`valid_from`, employee_contract.`ec_valid_from`)
						AND LEAST(default_end(employee.`valid_to`), default_end(employee_contract.`valid_to`), default_end(employee_contract.`ec_valid_to`))
					OR  GREATEST(employee.`valid_from`, employee_contract.`valid_from`, employee_contract.`ec_valid_from`)
						BETWEEN     
							sic.valid_from
						AND default_end(sic.`valid_to`)
				)
			LEFT JOIN unit ON
					unit.`unit_id`= ' . EmployeeGroup::getActiveGroupSQL("unit_id", "employee") . '
				AND unit.`status` = ' . Status::PUBLISHED . '
				AND (		employee.`valid_from` BETWEEN unit.`valid_from` AND default_end(unit.`valid_to`)
						OR	unit.`valid_from` BETWEEN employee.`valid_from` AND default_end(employee.`valid_to`)
				)
			LEFT JOIN workgroup ON
					workgroup.`workgroup_id` = ' . EmployeeGroup::getActiveGroupSQL(
                "workgroup_id",
                "employee_contract"
            ) . '
				AND workgroup.`status` = ' . Status::PUBLISHED . '
				AND (		GREATEST(employee_contract.`valid_from`, employee_contract.`ec_valid_from`)
				 			BETWEEN 
				 				workgroup.`valid_from` 
				 			AND default_end(workgroup.`valid_to`)
						OR	workgroup.`valid_from` 
							BETWEEN
								GREATEST(employee.`valid_from`, employee_contract.`valid_from`, employee_contract.`ec_valid_from`)
							AND LEAST(default_end(employee.`valid_to`), default_end(employee_contract.`valid_to`), default_end(employee_contract.`ec_valid_to`))
				)
			WHERE 1
		' . $whereFromFilters;
    }

    protected function getSqlWhereFromFilters($filter): string
    {
        $whereFromFilters = "";
        if (!empty($filter['valid_from']) && !empty($filter['valid_to'])) {
            $whereFromFilters .= "
			AND (		sic.`valid_from` BETWEEN '" . $filter['valid_from'] . "' AND '" . $filter['valid_to'] . "' 
					OR	'" . $filter['valid_from'] . "' BETWEEN sic.`valid_from` AND sic.`valid_to`
			)";
        }
        if (!empty($filter["company"]) && $filter["company"] !== 'ALL') {
            $whereFromFilters .= "
			AND (`employee`.`company_id` = '" . $filter["company"] . "')";
        }
        if (!empty($filter["payroll"]) && $filter["payroll"] !== 'ALL') {
            $whereFromFilters .= "
			AND (`employee`.`payroll_id` = '" . $filter["payroll"] . "')";
        }
        if (!empty($filter["unit"]) && $filter["unit"] !== 'ALL') {
            $whereFromFilters .= "
			AND (`unit`.`unit_id` = '" . $filter["unit"] . "')";
        }
        if (!empty($filter["workgroup"]) && $filter["workgroup"] !== 'ALL') {
            $whereFromFilters .= "
			AND (`workgroup`.`workgroup_id` = '" . $filter["workgroup"] . "')";
        }
        if (!empty($filter["employee_contract"]) && $filter["employee_contract"] !== 'ALL') {
            $whereFromFilters .= "
			AND (`employee_contract`.`employee_contract_id` = '" . $filter["employee_contract"] . "')";
        }

        return $whereFromFilters;
    }

    protected function getInfeasibleConstraintData($jsonData): string
    {
        $data = json_decode($jsonData);
        if (isset($data->rule_id)) {
            unset($data->rule_id);
        }
        if (isset($data->employee_id)) {
            unset($data->employee_id);
        }
        if (isset($data->month)) {
            $data->month = $this->getMonthNameFromNumber($data->month);
        }
        if (isset($data->competency_id)) {
            $data->competency_id = $this->getCompetencyName($data->competency_id);
        }
        if (isset($data->shift_id)) {
            $data->shift_id = $this->getShiftName($data->shift_id);
        }
        if (isset($data->shift_ids)) {
            $data->shift_ids = $this->getShiftNames($data->shift_ids);
        }
        if (isset($data->shiftgroup_ids)) {
            $data->shiftgroup_ids = $this->getShiftGroupNames($data->shiftgroup_ids);
        }
        if (isset($data->next_shift_id)) {
            $data->next_shift_id = $this->getShiftName($data->next_shift_id);
        }
        if (isset($data->workgroup_id)) {
            $data->workgroup_id = $this->getWorkgroupName($data->workgroup_id);
        }
        if (isset($data->workgroup_ids)) {
            $data->workgroup_ids = $this->getWorkgroupNames($data->workgroup_ids);
        }


        $ret = '';
        foreach ($data as $key => $value) {
            $keyTranslated = Dict::getValue($key);
            $key = (empty($keyTranslated) ? $key : $keyTranslated);
            $value = is_array($value) ? json_encode($value) : $value;
            $ret .= $key . ': ' . $value . ' ';
        }
        return $ret;
    }

    protected function getMonthNameFromNumber($monthNumber): string
    {
        $dateObj = DateTime::createFromFormat('!m', (string)$monthNumber);
        $monthName = $dateObj->format('F');
        return Dict::getValue(strtolower($monthName));
    }

    protected function getCompetencyName($competencyId): string
    {
        return $this->competency[$competencyId]["competency_name"] ?? "";
    }

    protected function getShiftName($shiftId): string
    {
        return $this->daytype[$shiftId]["name"] ?? "";
    }

    protected function getShiftNames($shiftIds): string
    {
        $shiftNames = '';
        foreach ($shiftIds as $shiftId) {
            $shiftNames .= $this->getShiftName($shiftId) . ', ';
        }
        return $shiftNames;
    }

    protected function getShiftGroupNames($shiftGroupIds): string
    {
        $shiftGroupNames = '';
        foreach ($shiftGroupIds as $shiftGroupId) {
            $shiftGroupNames .= $this->getShiftGroupName($shiftGroupId) . ', ';
        }
        return $shiftGroupNames;
    }

    protected function getShiftGroupName($shiftGroupId): string
    {
        return $this->dayTypeGroup[$shiftGroupId]["daytype_group_name"] ?? "";
    }

    protected function getWorkgroupName($workgroupId): string
    {
        return $this->workgroup[$workgroupId]["workgroup_name"] ?? "";
    }

    protected function getWorkgroupNames($workgroupIds): string
    {
        $workgroupNames = '';
        foreach ($workgroupIds as $workgroupId) {
            $workgroupNames .= $this->getWorkgroupName($workgroupId) . ', ';
        }
        return $workgroupNames;
    }
}
