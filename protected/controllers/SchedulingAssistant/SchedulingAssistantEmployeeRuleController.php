<?php

use Components\Grid2Core\Exception\Grid2ModelErrorException;
use Components\HTTPCore\Builder\SystemErrorResponseBuilder;
use Components\HTTPCore\ResponseSender\DefaultResponseSender;
use Components\SchedulingAssistant\Enum\SchedulingAssistantEmployeeRuleJsonStorageEnum;
use Components\SchedulingAssistant\Enum\SchedulingAssistantEmployeeRuleTypeEnum;
use Components\SchedulingAssistant\PreProcessor\SchedulingAssistantEmployeeInRotationPreProcessor;
use Components\SchedulingAssistantCore\Enum\SchedulingAssistantGroupTypeEnum;
use Components\SchedulingAssistantCore\Provider\SchedulingAssistantGroupTypeProvider;
use Components\SchedulingAssistantCore\Provider\TemplateEventsModelByWorkgroupIdProvider;
use Components\SchedulingAssistantCore\Validator\SchedulingAssistantEmployeeRotationDataValidator;
use Components\SchedulingAssistantCore\Validator\SchedulingAssistantEmployeeRotationEmployeeInRotationDataValidator;
use Components\SchedulingAssistantCore\Validator\SchedulingAssistantEmployeeRotationMontlyWorktimeDataValidator;
use Components\SchedulingAssistantEmployeeRule\Descriptor\EmployeeRuleRotationCloneProcessDescriptor;
use Components\SchedulingAssistantEmployeeRule\Handler\EmployeeRuleRotationCloneHandler;
use Components\SchedulingAssistantExecutor\Builder\SchedulingAssistantEmployeeRuleSimpleValidationExceptionAjaxResponsePartBuilder;
use Components\SchedulingAssistantExecutor\Builder\SchedulingAssistantEmployeeRuleValidationExceptionAjaxResponsePartBuilder;
use Components\SchedulingAssistantExecutor\Exception\SchedulingAssistantEmployeeRuleValidationException;

Yang::loadComponentNamespaces('SchedulingAssistantWorkScheduleEdit');
Yang::loadComponentNamespaces('SchedulingAssistantEmployeeRule');
Yang::loadComponentNamespaces('SchedulingAssistantExecutor');
Yang::loadComponentNamespaces('SchedulingAssistantCore');
Yang::loadComponentNamespaces('SchedulingAssistant');
Yang::loadComponentNamespaces('Grid2Core');
Yang::loadComponentNamespaces('HTTPCore');
Yang::loadComponentNamespaces('Employee');
Yang::loadComponentNamespaces('Core');

/**
 * Scheduling Assistant employee rule controller
 *
 */
class SchedulingAssistantEmployeeRuleController extends Grid2Controller
{

	private $publishedStatus;
	private $defaultEnd;
    private string $groupType;

	public function __construct()
	{
		$this->publishedStatus = Status::PUBLISHED;
		$this->defaultEnd = App::getSetting('defaultEnd');
        $this->groupType = (new SchedulingAssistantGroupTypeProvider())->provide();
		parent::__construct("SchedulingAssistant/schedulingAssistantEmployeeRule");
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("SchedulingAssistantEmployeeRule", "dhtmlxGrid");
		parent::setControllerPageTitleId("page_title_schedulingAssistantEmployeeRule");
		parent::enableMultiGridMode();

		$this->LAGridRights->overrideInitRights("paging", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search_header", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("select", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("column_move", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload_sortings", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("col_sorting", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("modify", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("delete", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("details", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("multi_select", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search", true, "dhtmlxGrid");

		$path = Yang::addAsset(Yang::getAlias('application.assets.SchedulingAssistant'), false, -1, true);
		Yang::registerScriptFile($path . '/js/SchedulingAssistantEmployeeRule.js');


		parent::G2BInit();
	}

    public function search()
    {
        return [
            'date' 	 => ['col_type'=>'ed', 'dPicker'=>true, 'width'=>'*', 'label_text'=>Dict::getValue("date"), 'default_value'=>date('Y-m-d')],
            'submit' => ['col_type'=>'searchBarReinitGrid', 'width'=>'*', 'label_text'=>''],
        ];
    }

    /**
     * Index action
     *
     * @param string $layout
     * @param string $view
     * @param array $params
     */
    public function actionIndex($layout = '//Grid2/layouts/indexLayout', $view = '/Grid2/index', $params = array())
    {
        $rowId           = requestParam('selectedID');

        if (!isset($rowId) || empty($rowId)) {
            parent::actionIndex($layout = '//Grid2/layouts/indexLayout', $view = '/Grid2/index', $params);
            return;
        }
        /** @var SchedulingAssistantEmployeeRule $ruleModel */
        $ruleModel = SchedulingAssistantEmployeeRule::model()->findByPk($rowId);
        $ruleModel->parameter_value = json_decode($ruleModel->parameter_value, true);
        $employeeContractData = $this->getEmployeeContract($ruleModel->employee_contract_id, $ruleModel->valid_from);
        $employeeWorkgroupId = $employeeContractData['workgroup_id'];
        $employeeName = $employeeContractData['value'];
        $templateEventsProvider = new TemplateEventsModelByWorkgroupIdProvider();
        $templateEvents = $templateEventsProvider($employeeWorkgroupId);
        $maximumDaysNumber = count($ruleModel->parameter_value['days'] ?? []);
        $events = [];
        for ($dayNumber = 1; $dayNumber <= $maximumDaysNumber ; $dayNumber++) {
            foreach ($templateEvents as $templateEvent) {
                $events[] = [
                    'id' => $templateEvent->template_event_id,
                    'day_number' => $dayNumber,
                    'name' => $templateEvent->name
                ];
            }
        }

        // Params
        $params['upload_period']      = Dict::getValue("upload_period");
        $params['monday']             = ucfirst(Dict::getValue("monday"));
        $params['tuesday']            = ucfirst(Dict::getValue("tuesday"));
        $params['wednesday']          = ucfirst(Dict::getValue("wednesday"));
        $params['thursday']           = ucfirst(Dict::getValue("thursday"));
        $params['friday']             = ucfirst(Dict::getValue("friday"));
        $params['saturday']           = ucfirst(Dict::getValue("saturday"));
        $params['sunday']             = ucfirst(Dict::getValue("sunday"));
        $params["weekdays"]           = [
            $params['monday'],
            $params['tuesday'],
            $params['wednesday'],
            $params['thursday'],
            $params['friday'],
            $params['saturday'],
            $params['sunday']
        ];
        $params['valid_from']         = $ruleModel->valid_from;
        $params['valid_to']           = $ruleModel->valid_to;
        $params['repeat']             = $maximumDaysNumber;
        $params['rowId']              = $ruleModel->row_id;
        $params['employeeContractId'] = $ruleModel->employee_contract_id;
        $params['name']               = Dict::getValue("name");
        $params['fullName']           = $employeeName;
        $params['days']               = $ruleModel->parameter_value["days"];
        $params['events']             = $events;

        parent::actionIndex($layout = '//Grid2/layouts/indexLayout', $view = 'application.views.SchedulingAssistant.schedulingAssistantEmployeeRule.index', $params);
    }

    /**
     * Form save
     *
     */
	public function actionSaveParameters()
	{
		$data = requestParam('data');

		if (!is_null($data)) {

			$data = json_decode(html_entity_decode($data), true);
			$employee_contract_id = $data["employee_contract_id"];
			$parameter_value = [];
			$rowId = $data['row_id'];

			foreach ($data["days"] as $key => $row) {
				$day_number = $row["day_number"];
				$row["parameters"][SchedulingAssistantEmployeeInRotationPreProcessor::WORKING] = filter_var($row["parameters"][SchedulingAssistantEmployeeInRotationPreProcessor::WORKING], FILTER_VALIDATE_BOOLEAN);

				if (
					isset($row["parameters"][SchedulingAssistantEmployeeInRotationPreProcessor::FROM])
					&& $row["parameters"][SchedulingAssistantEmployeeInRotationPreProcessor::FROM] == ""
				) {

					unset($row["parameters"][SchedulingAssistantEmployeeInRotationPreProcessor::FROM]);
				}

				if (
					isset($row["parameters"][SchedulingAssistantEmployeeInRotationPreProcessor::TO])
					&& $row["parameters"][SchedulingAssistantEmployeeInRotationPreProcessor::TO] == ""
				) {

					unset($row["parameters"][SchedulingAssistantEmployeeInRotationPreProcessor::TO]);
				}

				if (empty($row["parameters"][SchedulingAssistantEmployeeInRotationPreProcessor::TEMPLATE_EVENT_IDS])) {

					unset($row["parameters"][SchedulingAssistantEmployeeInRotationPreProcessor::TEMPLATE_EVENT_IDS]);
				}

				if (isset($row["parameters"][SchedulingAssistantEmployeeInRotationPreProcessor::TEMPLATE_EVENT_IDS])) {
					foreach ($row["parameters"][SchedulingAssistantEmployeeInRotationPreProcessor::TEMPLATE_EVENT_IDS] as &$value) {
						$value = (int) $value;
					}
				}

				$parameter_value["days"][$day_number] = $row["parameters"];
			}

			// Validate row
			$rule = SchedulingAssistantEmployeeRule::model()->findByPk($rowId);
			try {
				(new SchedulingAssistantEmployeeRotationDataValidator())
					->setException(new SchedulingAssistantEmployeeRuleValidationException($rule))
					->validate($parameter_value["days"]);
			} catch (\Exception $e) {
				$response = (new SystemErrorResponseBuilder())
					->setException($e)
					->addPartBuilder(
						new SchedulingAssistantEmployeeRuleValidationExceptionAjaxResponsePartBuilder()
					)
					->build();
				ob_end_clean();
				(new DefaultResponseSender())->send($response);
				ob_end_flush();

				return;
			}

			// Update row
			$filter = "`row_id`='" . $rowId . "'";
			$updated_row_num = SchedulingAssistantEmployeeRule::model()->updateAll(
				[
					'parameter_type' => 'employee-in-rotation',
					'parameter_value' => json_encode($parameter_value),
					'modified_by' => userID(),
					'modified_on' => date('Y-m-d H:i:s')
				],
				$filter
			);
		}

		$status = [
			'status' => 2,
			'text' => "",
			'message' => "",
		];

		echo json_encode($status);
	}

	protected function fetchGridData($filter, $isExport = false, $excelExport = false, $csvString = false)
	{
		$gridID = requestParam('gridID');
		if ($gridID != 'dhtmlxGrid') { return []; }

        $filter = requestParam('searchInput');

		$sql = $this->getSchedulingAssistantEmployeeRule($filter);
		$scResult = dbFetchAll($sql);

		$retArr = [
			"data"	=> [],
			"log"	=> "***schedulingAssistantEmployeeRule***",
		];

		foreach($scResult as $value) {
			$row["pk"] = $value["row_id"];
			$row["columns"]['employee_contract_id']['data'] = $value["employee_contract_id"];
            $row['columns']['company_id']['data'] = $value['company_id'];
			$row["columns"]['parameter_type']['data'] = $value["parameter_type"];
			$row["columns"]['parameter_value']['data'] = $this->fetchGridDataParameterTypes($value["parameter_type"], $value["parameter_value"]);
			$row["columns"]['valid_from']['data'] = $value["valid_from"];
			$row["columns"]['valid_to']['data'] = $value["valid_to"];
			$row["columns"]['note']['data'] = $value["note"];

			$retArr['data'][] = $row;

		}
		$retArr["dhtmlxGrid"] = true;
		return $retArr;
	}

	private function fetchGridDataParameterTypes($parameterType, $value)
	{
		$dataArray = json_decode($value, true);
		$result = '';
		if ($parameterType == "monthly-worktime") {
			$result = Dict::getValue("date").': '.$dataArray['year'] . '-'.$dataArray['month'] . ', ' .
					Dict::getValue("worktimehour").': '.$dataArray['worktime'] / 3600;
		}
		elseif ($parameterType == "employee-in-rotation") {
            $days = isset($dataArray['days']) ? count($dataArray['days']) : 0;
            $result = Dict::getValue('employeeinrotation', [], 'ttwa-base').', '.Dict::getValue('number_of_repeated_days', [], 'ttwa-wfm').': '.$days;
        }
		elseif($parameterType == "work-in-selected-shiftgroups-only") {
			$daytypegroup = new DaytypeGroup();
			$c = new CDbCriteria();
			$c->condition = "`daytype_group_id` IN ('".implode("','", $dataArray['shift_group_ids'])."')";
			$c->order = "`daytype_group_name` ASC";
			$daytypegroupResult = $daytypegroup->findAll($c);

			$daytypegroupName = [];
			foreach($daytypegroupResult as $value) {
				$daytypegroupName[] = $value->daytype_group_name;
			}
			$result .= implode(', ', $daytypegroupName);
		}
		elseif($parameterType == "work-in-selected-days-only") {
			$dayNames = [
						0 => Dict::getValue('monday'),
						1 => Dict::getValue('tuesday'),
						2 => Dict::getValue('wednesday'),
						3 => Dict::getValue('thursday'),
						4 => Dict::getValue('friday'),
						5 => Dict::getValue('saturday'),
						6 => Dict::getValue('sunday')
			];
			$daysNamesArray = [];
			foreach($dataArray['days'] as $value) {
				$daysNamesArray[] = $dayNames[$value];
			}
			$result .= implode(', ', $daysNamesArray);
		}

		return $result;

	}

	public function actionDialog($layout = '//Grid2/layouts/dialogLayout', $view = '/Grid2/dialog', $additionalParams = [])
	{
		$this->G2BInit();
		$dialogMode = (int)requestParam('dialogMode');
		$editPK = requestParam('editPK');
        if (isset($editPK)) {
            $crit = new CDbCriteria();
            $crit->select = "`parameter_type`";
            $crit->condition = "row_id = $editPK";
            $schedulingModifyResult = SchedulingAssistantEmployeeRule::model()->find($crit);
        }
        $isClone = (bool)requestParam('isClone', false);
		if ($dialogMode == 1) {
			if ('monthly-worktime' == $schedulingModifyResult->parameter_type) {
				$_POST['generate_from'] = 'monthlyWorktime';
				$_POST['actionSaveUrl'] = 'monthlyWorktimeUpdate';
			}
            elseif ('employee-in-rotation' == $schedulingModifyResult->parameter_type) {
                $_POST['generate_from'] = 'employeeInRotation';
                $_POST['actionSaveUrl'] = 'employeeInRotationUpdate';
            }
		} elseif ($isClone) {
            if ('monthly-worktime' == $schedulingModifyResult->parameter_type) {
                $_POST['generate_from'] = 'monthlyWorktime';
                $_POST['actionSaveUrl'] = 'monthlyWorktime';
            }
            elseif ('employee-in-rotation' == $schedulingModifyResult->parameter_type) {
                $_POST['generate_from'] = 'employeeInRotationCopy';
                $_POST['actionSaveUrl'] = 'employeeInRotationCopy';
            }
        }
		parent::actionDialog('//Grid2/layouts/dialogLayout', '/Grid2/dialog', []);
	}

    /**
     * Get status buttons
     *
     * @param null $gridID
     * @return mixed
     */
	protected function getStatusButtons($gridID = null)
	{
		$buttons = [];

        $buttons["schedulingAssistantEmployeeRuleMod"] = [
         "type"    => "button",
         "id"      => "schedulingAssistantEmployeeRuleMod",
         "class"   => "schedulingAssistantEmployeeRuleMod",
         "name"    => "schedulingAssistantEmployeeRuleMod",
         "img"     => "/images/status_icons/st_next.png",
         "label"   => "Részletek",
         "onclick" => "schedulingAssistantEmployeeRuleMod('" . Dict::getValue("please_select_line") . "');",
        ];

		$buttons["monthlyWorktime"] = [
			"type"    => "button",
			"id"      => "monthlyWorktime",
			"class"   => "openAddDialogIcon",
			"name"    => "monthlyWorktime",
			"img"     => "/images/status_icons/st_add.png",
			"label"   => "Hozzáadás (havi munkaidő)",
			"onclick" => "monthlyWorktime('" . Dict::getValue("backup") . "', '" . Dict::getValue("errors") . "');",
		];

        $buttons["employeeInRotation"] = [
         "type"    => "button",
         "id"      => "employeeInRotation",
         "class"   => "openAddDialogIcon",
         "name"    => "employeeInRotation",
         "img"     => "/images/status_icons/st_add.png",
         "label"   => "Hozzáadás (dolgozói forgás)",
         "onclick" => "employeeInRotation('" . Dict::getValue("backup") . "', '" . Dict::getValue("errors") . "');",
        ];

        $buttons["schedulingAssistantEmployeeRuleCopy"] = [
            "type" => "button",
            "id" => "schedulingAssistantEmployeeRuleCopy",
            "class" => "schedulingAssistantEmployeeRuleCopy",
            "name" => "schedulingAssistantEmployeeRuleCopy",
            "img" => "/images/status_icons/st_copy.png",
            "label" => "schedulingAssistantEmployeeRuleCopy",
            "onclick" => "G2BCopyDialog('dhtmlxGrid',getSelected('dhtmlxGrid'),'./dialog?isClone=1','./saveClone','./gridData',null,'".$this->getControllerPageTitle()." - ".Dict::getValue("copy_item")."','".Dict::getValue("please_select_line")."');",
        ];

		$originalButtons = parent::getStatusButtons();

		return Yang::arrayMerge($buttons, $originalButtons);
	}

	public function actionmonthlyWorktime()
	{
		// Validate row
		try {
			$parameters = [requestParam('dialogInput_monthlyWorktime')];
			$params = reset($parameters);

			$writeInTable = new SchedulingAssistantEmployeeRule();
			(new SchedulingAssistantEmployeeRotationMontlyWorktimeDataValidator())
				->setException(new SchedulingAssistantEmployeeRuleValidationException($writeInTable))
				->validate($parameters);

			$ecId = $params['employee_contract_id'];
			$worktime = $params['worktime'];
			$worktimeSec = $worktime * 3600;
			$yearMonth = $params['year_month'];
			$year = date("Y", strtotime($params['year_month']));
			$month = date("n", strtotime($params['year_month']));
			$validFrom = $params['valid_from'];
			$validTo = $params['valid_to'];
			$note = $params['note'];

			$parameterAttributes = [
				SchedulingAssistantEmployeeRuleJsonStorageEnum::WORKTIME => $worktimeSec,
				SchedulingAssistantEmployeeRuleJsonStorageEnum::YEAR => $year,
				SchedulingAssistantEmployeeRuleJsonStorageEnum::MONTH => $month,

			];

			$writeInTable->employee_contract_id = $ecId;
			$writeInTable->parameter_type = 'monthly-worktime';
			$writeInTable->parameter_value = json_encode($parameterAttributes);
			$writeInTable->status = $this->publishedStatus;
			$writeInTable->note = $note;
			$writeInTable->valid_from = $validFrom;
			$writeInTable->valid_to = $validTo;
			$writeInTable->worktime = $worktime;
			$writeInTable->year_month = $yearMonth;

			$status = $this->saveRoleByGroup($writeInTable, 'save');
			echo json_encode($status);
		} catch (\Exception $e) {
			$response = (new SystemErrorResponseBuilder())
				->setException($e)
				->addPartBuilder(
					new SchedulingAssistantEmployeeRuleSimpleValidationExceptionAjaxResponsePartBuilder()
				)
				->build();
			ob_end_clean();
			(new DefaultResponseSender())->send($response);
			ob_end_flush();
		}
	}

	public function actionmonthlyWorktimeUpdate()
	{
		try {
			$parameters = [requestParam('dialogInput_monthlyWorktime')];
			$params = reset($parameters);

			$rowId = $params['row_id'];
			$ecId = $params['employee_contract_id'];
			$worktime = $params['worktime'];
			$worktimeSec = $worktime * 3600;
			$yearMonth = $params['year_month'];
			$year = date("Y", strtotime($params['year_month']));
			$month = date("n", strtotime($params['year_month']));

			$parameterAttributes = [
				SchedulingAssistantEmployeeRuleJsonStorageEnum::WORKTIME => $worktimeSec,
				SchedulingAssistantEmployeeRuleJsonStorageEnum::YEAR => $year,
				SchedulingAssistantEmployeeRuleJsonStorageEnum::MONTH => $month,

			];

			$parameterValue = json_encode($parameterAttributes);
			$validFrom = $params['valid_from'];
			$validTo = $params['valid_to'];
			$note = $params['note'];

			$scSave = SchedulingAssistantEmployeeRule::model()->findByPk($rowId);
			(new SchedulingAssistantEmployeeRotationMontlyWorktimeDataValidator())
				->setException(new SchedulingAssistantEmployeeRuleValidationException($scSave))
				->validate($parameters);

			$scSave->attributes = [
				'employee_contract_id' => $ecId,
				'parameter_value' => $parameterValue,
				'note' => $note,
				'valid_from' => $validFrom,
				'valid_to' => $validTo,
				'worktime' => $worktime,
				'year_month' => $yearMonth
			];

			$status = $this->saveRoleByGroup($scSave, 'update');

			echo json_encode($status);

		} catch (\Exception $e) {
			$response = (new SystemErrorResponseBuilder())
				->setException($e)
				->addPartBuilder(
					new SchedulingAssistantEmployeeRuleSimpleValidationExceptionAjaxResponsePartBuilder()
				)
				->build();
			ob_end_clean();
			(new DefaultResponseSender())->send($response);
			ob_end_flush();
		}
	}

	public function actionEmployeeInRotation()
	{
		// Validate row
		try {
			$parameters = [requestParam('dialogInput_employeeInRotation')];
			$params = reset($parameters);
			$ecId = $params['employee_contract_id'];
			$validFrom = $params['valid_from'];
			$validTo = $params['valid_to'];
			$daynumber = (int) $params['days_number'];
			$note = $params['note'];

			$writeInTable = new SchedulingAssistantEmployeeRule();
			(new SchedulingAssistantEmployeeRotationEmployeeInRotationDataValidator())
				->setException(new SchedulingAssistantEmployeeRuleValidationException($writeInTable))
				->validate($parameters);

			$writeInTable->employee_contract_id = $ecId;
			$writeInTable->parameter_type = 'employee-in-rotation';
			$writeInTable->parameter_value = $this->getParameterValueJsonTemplate($daynumber);
			$writeInTable->status = $this->publishedStatus;
			$writeInTable->note = $note;
			$writeInTable->valid_from = $validFrom;
			$writeInTable->valid_to = $validTo;
			$writeInTable->worktime = null;
			$writeInTable->year_month = null;

			$status = $this->saveRoleByGroup($writeInTable, 'save');
			$status['newId'] = $writeInTable->row_id;
			echo json_encode($status);
		} catch (\Exception $e) {
			$response = (new SystemErrorResponseBuilder())
				->setException($e)
				->addPartBuilder(
					new SchedulingAssistantEmployeeRuleSimpleValidationExceptionAjaxResponsePartBuilder()
				)
				->build();
			ob_end_clean();
			(new DefaultResponseSender())->send($response);
			ob_end_flush();
		}
	}

    public function actionEmployeeInRotationCopy()
    {
        $status = [
            'status' => 1,
            'title' => '',
            'error'	=> ''
        ];

        $parameters  = requestParam('dialogInput_employeeInRotationCopy');
        $rowId       = $parameters['row_id'];
        $ecId        = $parameters['employee_contract_id'];
        $validFrom   = $parameters['valid_from'];
        $validTo     = $parameters['valid_to'];
        $note        = $parameters['note'];
        $offset      = (int)$parameters['offset'];

        $descriptor = new EmployeeRuleRotationCloneProcessDescriptor();
        $descriptor->setEmployeeContractId($ecId)
            ->setFrom($validFrom)
            ->setTo($validTo)
            ->setNote($note)
            ->setOffset($offset)
            ->setOriginalRowId($rowId);
        $handler = new EmployeeRuleRotationCloneHandler();
        try {
            $handler->handle($descriptor);
            $status['newId'] = $descriptor->getCloned()->row_id;
        } catch (Grid2ModelErrorException $e) {
            $status['status'] = 0;
            $status['error'] = $e->getErrorMessage();
        }

        echo json_encode($status);
    }

	public function actionEmployeeInRotationUpdate()
	{
		// Validate row
		try {
			$parameters = [requestParam('dialogInput_employeeInRotation')];
			$params = reset($parameters);

			$rowId = $params['row_id'];
			$ecId = $params['employee_contract_id'];
			$validFrom = $params['valid_from'];
			$validTo = $params['valid_to'];
			$note = $params['note'];

			$scSave = SchedulingAssistantEmployeeRule::model()->findByPk($rowId);
			$scSave->attributes = [
				'employee_contract_id' => $ecId,
				'note' => $note,
				'valid_from' => $validFrom,
				'valid_to' => $validTo
			];

			(new SchedulingAssistantEmployeeRotationEmployeeInRotationDataValidator())
				->setException(new SchedulingAssistantEmployeeRuleValidationException($scSave))
				->validate($parameters);

			$status = $this->saveRoleByGroup($scSave, 'update');

			echo json_encode($status);
		} catch (\Exception $e) {
			$response = (new SystemErrorResponseBuilder())
				->setException($e)
				->addPartBuilder(
					new SchedulingAssistantEmployeeRuleSimpleValidationExceptionAjaxResponsePartBuilder()
				)
				->build();
			ob_end_clean();
			(new DefaultResponseSender())->send($response);
			ob_end_flush();
		}

	}

	public function columns()
	{
		$ecSQL = $this->getEmployeeContractSql();
		$shiftgroupsSQL = $this->getShiftGroupsSql();
		$parameterTypeArray = $this->getParamterTypeArray();
        $jsonValues = [];
        if (isset($_POST['editPK'])) {
            $model = SchedulingAssistantEmployeeRule::model()->findByPk($_POST['editPK']);
            $jsonValues = json_decode($model['parameter_value'], true);
        }
		$columns["dhtmlxGrid"] = [
			'row_id'	=> ['width' => 10, 'col_type' => 'ro', 'grid' => false, 'export' => false, 'window' => false],
			'employee_contract_id'	=>  ['width' => 250, 'col_type' => 'combo', 'grid' => true, 'export' => false, 'align' => 'left', 'window' => false,
									'options'	=>	[
										'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
												'sql' => $ecSQL,
												'array'	=> [["id" => "", "value" => ""]]
									],
								],
            'company_id'			=>
			[
				'export'	=> true,
				'grid'		=> true,
				'window'	=> true,
				'col_type'	=> 'combo',
				'options'   =>
				[
					'mode'                              => Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'               => Company::class,
					'comboId'                           => 'company_id',
					'comboValue'                        => 'company_name',
				],
				'width' 	=> "200",
			],
			'parameter_type'	=> ['width' => 200, 'col_type' => 'combo', 'grid' => true, 'export' => false, 'window' => false,
									'options'	=>	[
										'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
										'array'	=> $parameterTypeArray
									],
								],
			'parameter_value'	=> ['width' => 500, 'col_type' => 'ro', 'grid' => true, 'export' => false, 'window' => false],
			'valid_from'	=> ['width' => 200, 'col_type' => 'ro', 'grid' => true, 'export' => false, 'window' => false],
			'valid_to'		=> ['width' => 200, 'col_type' => 'ro', 'grid' => true, 'export' => false, 'window' => false],
			'note'			=> ['width' => 200, 'col_type' => 'ro', 'grid' => true, 'export' => false, 'window' => false],

			'worktime'		=> ['width' => 200, 'col_type' => 'ed', 'grid' => false, 'export' => false, 'window' => true],
			'year_month' 	=> ['width' => 200, 'col_type' => 'ed', 'mPicker' => true, 'grid' => false, 'export' => false, 'window' => true],
			'shiftgroups'	=> ['width' => 200, 'col_type' => 'combo', 'export' => false, 'grid' => false, 'window'	=>	true,
										'default_value' => '', 'multiple' => 1,
										'options'	=>	[
											'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
											'sql' => $shiftgroupsSQL,
											'array'	=> [["id" => "", "value" => ""]]
										],
									],
			'monday' 		=> ['width' => 80,	'col_type' => 'cb', 'grid' => false, 'export' => false, 'window' => true],
			'tuesday' 		=> ['width' => 80,	'col_type' => 'cb', 'grid' => false, 'export' => false, 'window' => true],
			'wednesday' 	=> ['width' => 80,	'col_type' => 'cb', 'grid' => false, 'export' => false, 'window' => true],
			'thursday' 		=> ['width' => 80,	'col_type' => 'cb', 'grid' => false, 'export' => false, 'window' => true],
			'friday' 		=> ['width' => 80,	'col_type' => 'cb', 'grid' => false, 'export' => false, 'window' => true],
			'saturday' 		=> ['width' => 80,	'col_type' => 'cb', 'grid' => false, 'export' => false, 'window' => true],
			'sunday' 		=> ['width' => 80,	'col_type' => 'cb', 'grid' => false, 'export' => false, 'window' => true,],
		];

		$columns["monthlyWorktime"] = [
			'employee_contract_id'	=> 	['width' => 200, 'col_type' => 'combo', 'export' => false, 'window'	=>	true,
											'default_value' => '',
											'options'	=>	[
												'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
												'sql' => $ecSQL,
												'array'	=> [["id" => "", "value" => ""]]
											],
										],
			'worktime'		=> ['width' => 200, 'col_type' => 'ed', 'grid' => false, 'export' => false, 'window' => true],
			'year_month' 	=> ['width' => 200, 'col_type' => 'ed', 'mPicker' => true, 'grid' => false, 'export' => false, 'window' => true],
			'valid_from'	=> ['width' => 200, 'col_type' => 'ed', 'dPicker' => true, 'grid' => false, 'export' => false, 'window' => true, 'line_break' => true],
			'valid_to'		=> ['width' => 200, 'col_type' => 'ed', 'dPicker' => true, 'grid' => false, 'export' => false, 'window' => true],
			'note'			=> ['width' => 200, 'col_type' => 'ed', 'grid' => false, 'export' => false, 'window' => true],

		];

        $daysNumber = isset($jsonValues['days']) ? count($jsonValues['days']) : null;
        $columns["employeeInRotation"] = [
            'employee_contract_id' => [
                'width'         => 200,
                'col_type'      => 'combo',
                'export'        => false,
                'window'        => true,
                'default_value' => '',
                'options'       => [
                    'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
                    'sql'   => $ecSQL,
                    'array'	=> [["id" => "", "value" => ""]]
                ],
            ],
            'days_number' => [
                'width' => 200,
                'col_type' => 'ed',
                'grid' => false,
                'export' => false,
                'window' => true,
                'mandatory' => true,
                'default_value' => $daysNumber,
                'readonly' => isset($daysNumber)
            ],
            'valid_from'	=> ['width' => 200, 'col_type' => 'ed', 'dPicker' => true, 'grid' => false, 'export' => false, 'window' => true, 'line_break' => true],
            'valid_to'		=> ['width' => 200, 'col_type' => 'ed', 'dPicker' => true, 'grid' => false, 'export' => false, 'window' => true],
            'note'			=> ['width' => 200, 'col_type' => 'ed', 'grid' => false, 'export' => false, 'window' => true],
        ];

        $columns["employeeInRotationCopy"] = [
            'employee_contract_id' => [
                'width'         => 200,
                'col_type'      => 'combo',
                'export'        => false,
                'window'        => true,
                'default_value' => '',
                'options'       => [
                    'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
                    'sql'   => $ecSQL,
                    'array'	=> [["id" => "", "value" => ""]]
                ],
            ],
            'offset'        => ['width' => 200, 'col_type' => 'ed', 'grid' => true, 'export' => false, 'window' => true, 'default_value' => 0],
            'valid_from'	=> ['width' => 200, 'col_type' => 'ed', 'dPicker' => true, 'grid' => false, 'export' => false, 'window' => true, 'line_break' => true],
            'valid_to'		=> ['width' => 200, 'col_type' => 'ed', 'dPicker' => true, 'grid' => false, 'export' => false, 'window' => true],
            'note'			=> ['width' => 200, 'col_type' => 'ed', 'grid' => false, 'export' => false, 'window' => true],
        ];


		return $columns;
	}

	public function attributeLabels()
	{
		$attributeLabels['dhtmlxGrid'] = [
			'employee_contract_id'	=> Dict::getValue("fullname"),
            'company_id'            => Dict::getValue("company"),
			'parameter_type'	=> Dict::getValue("parameter_type"),
			'parameter_value'	=> Dict::getValue("parameter_value"),
			'valid_from'		=> Dict::getValue("valid_from"),
			'valid_to'			=> Dict::getValue("valid_to"),
			'note'				=> Dict::getValue("note"),
		];

		$attributeLabels['monthlyWorktime'] = [
			'employee_contract_id'		=> Dict::getValue("fullname"),
			'worktime'			=> Dict::getValue("worktimehour"),
			'year_month'		=> Dict::getValue("year_month"),
			'note'				=> Dict::getValue("note"),
			'valid_from'		=> Dict::getValue("valid_from"),
			'valid_to'			=> Dict::getValue("valid_to"),
		];

        $attributeLabels['employeeInRotation'] = [
         'employee_contract_id'	=> Dict::getValue("fullname"),
         'days_number'          => Dict::getValue('number_of_repeated_days'),
         'note'			     	=> Dict::getValue("note"),
         'valid_from'	    	=> Dict::getValue("valid_from"),
         'valid_to'	    		=> Dict::getValue("valid_to"),
        ];

        $attributeLabels['employeeInRotationCopy'] = [
            'employee_contract_id'	=> Dict::getValue("fullname"),
            'note'			     	=> Dict::getValue("note"),
            'valid_from'	    	=> Dict::getValue("valid_from"),
            'valid_to'	    		=> Dict::getValue("valid_to"),
            'offset'                => Dict::getValue("offset", [], 'ttwa-SchedulingAssistant')
        ];

		return $attributeLabels;
	}

    /**
     * Get parameter_value json template
     *
     * @param int $daynumber
     * @return false|string
     */
	private function getParameterValueJsonTemplate($daynumber = 0)
    {
        $json     = "";
        $working  = filter_var(true, FILTER_VALIDATE_BOOLEAN);
        $template = [
            "days" => []
        ];

        for ($i = 1; $i <= $daynumber; $i++) {

            $template["days"][$i] = ["working" => $working];
        }
        $json = json_encode($template);

        return $json;
    }

	private function getParamterTypeArray()
	{
		$parameterType = [
			[
				'id' => 'monthly-worktime',
				'value' => Dict::getValue("monthyworktime")
			],
            [
             'id' => 'employee-in-rotation',
             'value' => Dict::getValue("employeeinrotation")
            ],
			[
				'id' => 'work-in-selected-shiftgroups-only',
				'value' => Dict::getValue("workinselectedshiftgroupsonly")
			],
			[
				'id' => 'work-in-selected-days-only',
				'value' => Dict::getValue("workinselecteddaysonly")
			]
		];
		return $parameterType;

	}

	private function getShiftGroupsSql()
	{
		$sql = "
			SELECT 
				daytype_group_id as id,
				daytype_group_name as value
			FROM
				daytype_group
			WHERE
				visibility = 1
		";

		return $sql;

	}

    private function getEmployeeContract(string $employeeContractId, string $validFrom): array
    {
        $sql = $this->getEmployeeContractSql($employeeContractId, $validFrom);
        $employeeContractData = dbFetchAll($sql);
        
        return reset($employeeContractData);
    }

	private function getEmployeeContractSql(?string $employeeContractId = null, ?string $validFrom = null)
	{
        $statusPublished = $this->publishedStatus;
        $defaultEnd = $this->defaultEnd;

        $art = new ApproverRelatedGroup;
        if ($this->groupType === SchedulingAssistantGroupTypeEnum::COMPANY) {
            $gargSQL = $art->getApproverReleatedGroupSQLWithoutCalc("Employee", "employeeManagement");
        } elseif ($this->groupType === SchedulingAssistantGroupTypeEnum::UNIT) {
            $gargSQL = $art->getApproverReleatedGroupSQLWithoutCalc("Employee", "workSchedule");
        }
        $validFrom = $validFrom ?? (new \DateTime())->format('Y-m-d');

        $joinsSQL = $gargSQL["join"];
        $whereSQL = $gargSQL["where"];
        $sql = "
            SELECT
                `employee_contract`.`employee_contract_id` AS id,
                `workgroup`.`workgroup_id`,
                " . Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]) . " AS value
            FROM `employee`
            LEFT JOIN `employee_contract` ON
                    `employee_contract`.`employee_id` = `employee`.`employee_id`
                AND `employee_contract`.`status` = {$statusPublished}
                AND '{$validFrom}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$defaultEnd}')
                AND '{$validFrom}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$defaultEnd}')
                " . EmployeeGroup::getAllActiveLeftJoinSQL("employee_contract", "'{$validFrom}'", "", "employee") . "
                " . EmployeeGroup::getAllBaseTablesWithGroup("employee", "employee_contract", "'{$validFrom}'") . "
            {$joinsSQL}
            WHERE
                    `employee`.`status` = {$statusPublished}
                {$whereSQL}
                AND '{$validFrom}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$defaultEnd}')";
        if (isset($employeeContractId)) {
            $sql .= "AND `employee_contract`.`employee_contract_id` = '{$employeeContractId}'";
        }
        $sql .= " ORDER BY value";

        return $sql;
	}

	private function getSchedulingAssistantEmployeeRule(array $filter = [])
	{
		$statusPublished = $this->publishedStatus;
		$defaultEnd = $this->defaultEnd;
        $filterDate = $filter['date'];

		$art = new ApproverRelatedGroup;
        if ($this->groupType === SchedulingAssistantGroupTypeEnum::COMPANY) {
            $gargSQL = $art->getApproverReleatedGroupSQLWithoutCalc("Employee", "employeeManagement");
        } elseif($this->groupType === SchedulingAssistantGroupTypeEnum::UNIT) {
            $gargSQL = $art->getApproverReleatedGroupSQLWithoutCalc("Employee", "workSchedule");
        }

		$joinsSQL = $gargSQL["join"];
		$whereSQL = $gargSQL["where"];

        $usedType = "'".implode("','", [
            SchedulingAssistantEmployeeRuleTypeEnum::MONTHLY_WORKTIME,
            SchedulingAssistantEmployeeRuleTypeEnum::EMPLOYEE_IN_ROTATION
        ])."'";
        $sql = "
			SELECT 
				scheduling_assistant_employee_rule.row_id, 
				scheduling_assistant_employee_rule.employee_contract_id, 
				company.company_id,
				unit.unit_id,
				scheduling_assistant_employee_rule.parameter_type, 
				scheduling_assistant_employee_rule.parameter_value, 
				scheduling_assistant_employee_rule.valid_from, 
				scheduling_assistant_employee_rule.valid_to,
				scheduling_assistant_employee_rule.note
			FROM
				scheduling_assistant_employee_rule
			LEFT JOIN employee_contract ON employee_contract.employee_contract_id = scheduling_assistant_employee_rule.employee_contract_id
					AND `employee_contract`.`status` = {$statusPublished}
					AND '{$filterDate}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$defaultEnd}')
					AND '{$filterDate}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$defaultEnd}')
			LEFT JOIN employee ON employee.employee_id = employee_contract.employee_id
				AND `employee`.`status` = {$statusPublished}
				AND '{$filterDate}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$defaultEnd}')
			" . EmployeeGroup::getAllActiveLeftJoinSQL("employee_contract", "'{$filterDate}'", "", "employee") . "
			" . EmployeeGroup::getAllBaseTablesWithGroup("employee", "employee_contract", "'{$filterDate}'") . "
			{$joinsSQL}
			WHERE
				scheduling_assistant_employee_rule.status = {$statusPublished}
				AND scheduling_assistant_employee_rule.parameter_type IN (". $usedType .")
				{$whereSQL}
			ORDER BY row_id
		";

		return $sql;
	}

	protected function saveRoleByGroup($model, $type='save')
	{
		$status = 1;
		$title = '';
		$error = '';

		if ($this->checkAttributesChanges($model)) {

			if ($model->validate()) {
				if ($type === 'update')	$model->update();
				if ($type === 'save')	$model->save();
			} else {
				$status = 0;
				$title = Dict::getValue("error");
				$error = "<div style=\"text-align: center; font-weight: bold; font-size: large;\">" . Dict::getValue("error") . "</div>" .
					getErrorsFromModel($model);
			}
		}

		$status = [
			'status' => $status,
			'title' => $title,
			'error'	=> $error
		];
		return $status;

	}

	protected function checkAttributesChanges($model)
	{
		$oldAttributes = $model->getOldAttributes();
		$oldRuleSettings = json_decode($oldAttributes['rule_setting']);
		unset($oldAttributes['rule_setting']);
		$attributes = $model->attributes;
		$ruleSettings = json_decode($attributes['rule_setting']);
		unset($attributes['rule_setting']);
		//$result1 = array_diff_assoc($oldAttributes, $attributes);
		//$result2 = array_diff_assoc($oldRuleSettings, $ruleSettings);
		if ($attributes == $oldAttributes && $oldRuleSettings == $ruleSettings) {
			return false;
		}
		return true;
	}

	public function filters()
	{
		return [
			'accessControl', // perform access control for CRUD operations
		];
	}

	public function accessRules()
	{
		return [
			['allow', // allow authenticated users to access all actions
				'users' => ['@'],
			],
			['deny',  // deny all users
				'users' => ['*'],
			],
		];
	}
}
