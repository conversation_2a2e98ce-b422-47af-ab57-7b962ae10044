<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\DbSync;
	use app\components\Dict;
	use Yang;

`/yii2-only';


#yii2: done

class DbSyncController extends Controller
{
	public function actionIndex() {
		$syncProcessId = requestParam('spid');

		$dbSync = new DbSync;
		if($dbSync->checkSyncProcessId($syncProcessId)) {
			$dbSync->run($syncProcessId);
		} else {
			header('Content-Type: text/html; charset=utf-8');
			die(Dict::getModuleValue("ttwa-base","missing_parameter"));
		}
	}

	public function actionRunAll() {
		$this->layout = "//layouts/ajax";
		$ajax = true;
		$defaultEnd  = App::getSetting("defaultEnd");
		$debugType = requestParam('debugType');
		$debugType != 'FileLog' ? $debugType = "echo" : $debugType = "FileLog";
		$response = array();
		$responseAll = array();

		$sql = "SELECT sync_process_id
			FROM sync_config
			WHERE (`status` = 2) AND (NOW() BETWEEN `valid_from` AND IFNULL(`valid_to`,'$defaultEnd'))
			ORDER BY row_id ASC";

		$processRows = dbFetchAll($sql);

		if(is_array($processRows) && count($processRows) > 0)
		{
			foreach ($processRows as $processRow) {
				$syncProcessId = $processRow['sync_process_id'];

				$dbSync = new DbSync;
				$response = $dbSync->run($syncProcessId, $ajax);
				$dbSync->debugSwitch($debugType, json_encode($response));
			}
		} else {
			$dbSync = new DbSync;
			$response = [
					'sync_process_id' => Dict::getModuleValue("ttwa-base","database_synchronization"),
					'response_code' => '203',
					'message' => Dict::getModuleValue("ttwa-base","not_set"),'nincs beállítva',
					'created_on' => date('Y-m-d H:i:s'),
				];
			$dbSync->debugSwitch($debugType, json_encode($response));
		}
	}

	public function actionRunProcess() {
		$this->layout = "//layouts/ajax";
		$ajax = true;
		$syncProcessId = requestParam('spid');
		$debugType = requestParam('debugType');
		$debugType != 'FileLog' ? $debugType = "echo" : $debugType = "FileLog";
		$response = [];
		$dbSync = new DbSync;
		$response = $dbSync->run($syncProcessId, $ajax);
		$responses = [
			'sync_process_id' => $response['sync_process_id'],
			'response_code' => $response['response_code'],
			'message' => $response['message'],
			'created_on' => $response['created_on'],
		];
		$dbSync->debugSwitch($debugType, json_encode($responses));
	}
}
?>