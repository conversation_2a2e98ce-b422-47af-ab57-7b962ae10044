<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\components\Messaging;
	use app\models\Employee;
	use app\models\Message;
	use app\models\Status;
	use app\models\User;
	use app\models\ahpCore\EmployeeAbsence;
	use Yang;

`/yii2-only';


#yii2: done

class MessageController extends Controller
{
	public $layout = '//Grid2/layouts/indexLayout';

	/**
	 * @return array action filters
	 */
	public function filters()
	{
		return array(
			'accessControl', // perform access control for CRUD operations
		);
	}

	/**
	 * Specifies the access control rules.
	 * This method is used by the 'accessControl' filter.
	 * @return array access control rules
	 */
	public function accessRules()
	{
		return array(
			array('allow', // allow authenticated users to access all actions
				'users'=>array('@'),
			),
			array('deny',  // deny all users
				'users'=>array('*'),
			),
		);
	}

	public function actionIndex()
	{

		$this->layout = "ajax";

		if (!App::getRight(null,"chatMenu")) {
			return false;
		}

		$limit = requestParam('limit');

		$dynCount = requestParam('dynCount');

		$oneThread = requestParam('oneThread');
		$oneThreadMode = false;
		$oneThreadMsgGroupID = null;

		if (!empty($oneThread)) {
			$oneThreadMode = true;
			$oneThreadMsgGroupID = $oneThread;
		}

		$m = new Messaging();
		if(!empty($oneThreadMsgGroupID))
		{
			$m->findThreadParamsByMgID($oneThreadMsgGroupID);
		}
		$msgs = $m->getMessagesByUser(null, $limit, true);

		$msgCount = 0;

		$prev_message_group_id = 0;

		$prev_editable = true;

		$retHTML = "";

		foreach ($msgs as $msg) {
			$sender_user_fullname = null;
			$recipient_user_fullname = null;

			if (!$oneThreadMode) {
				if (isset($msg["firstInThread"]) && $msg["firstInThread"] === true) {
					if ($msgCount > 0) {
						$retHTML .= "</ol>";

						if ($prev_editable) {
							$retHTML .= '<textarea id="'.$prev_message_group_id.'" class="messageBox">';
							$retHTML .= '</textarea>';
						}

						$retHTML .= "</div>";
						$retHTML .= "</div>";
					}

					if ($msgCount === 0 && (int)$dynCount === 0) {
						$class = "";
					} else {
						$class = "hidden";
					}

					if ($msg["sender"] === userID()) { // én vagyok a küldő
						$chatWithUser = $msg["recipient"];
						// right
					} else { // én vagyok a címzett
						$chatWithUser = $msg["sender"];
						//left
					}

					if (empty($msg["message_subject"])) {
						$msg["message_subject"] = Dict::getValue("message");
					}

					$prev_editable = !$msg["threadClosed"];

					$retHTML .= "<div id=\"chatThreadContents_".$msg["message_group_id"]."\" title=\"".($prev_editable?"":Dict::getValue("locked"))."\" class=\"chatThreadContents ".($msg["threadClosed"]?"closed":"")."\">";
					$retHTML .= "<div class=\"chatThreadTitle\">".$msg["message_subject"]." - ".(!empty(Employee::getEmployeeFullnameByUserID($chatWithUser))?Employee::getEmployeeFullnameByUserID($chatWithUser).' ('.User::getUserNameById($chatWithUser).')':User::getUserNameById($chatWithUser))." - ".date("Y.m.d.", strtotime($msg["created_on"]))."</div>";
					$retHTML .= "<div class=\"chatThreadContent $class\">";
					$retHTML .= "<input id=\"chatWithUser_".$msg["message_group_id"]."\" type=\"hidden\" value=\"$chatWithUser\" />";
					$retHTML .= "<ol id=\"chatThreadConversation_".$msg["message_group_id"]."\" class=\"conversation\" >";
				}
			}

			$class = "";

			if ($msg["sender"] === userID()) { // én vagyok a küldő
				$class = "myMessage";
			}

			$sender_user_id = $msg["sender"];
			$recipient_user_id = $msg["recipient"];

			$sender_user_fullname = Employee::getEmployeeFullnameByUserID($sender_user_id);
			$recipient_user_fullname = Employee::getEmployeeFullnameByUserID($recipient_user_id);

			$retHTML .= "<li class=\"$class\">";
				$retHTML .= "<div class=\"chatThreadItem\">";
//					if (!empty($sender_user_fullname) || !empty($recipient_user_fullname)) {
//						$retHTML .= "<div class=\"sender\">";
//							if (!empty($sender_user_fullname)) {
//								$retHTML .= Dict::getValue("sender").": ".$sender_user_fullname."<br/>";
//							}
//							if (!empty($recipient_user_fullname)) {
//								$retHTML .= Dict::getValue("recipient").": ".$recipient_user_fullname."<br/>";
//							}
//						$retHTML .= "</div>";
//					}
					$retHTML .= "<div class=\"message\">";
						$retHTML .= $msg["message"];
					$retHTML .= "</div>";
					$retHTML .= "<div class=\"date\">";
						if (!empty($sender_user_fullname)) {
							$retHTML .= Dict::getValue("sender").": <i>".$sender_user_fullname."</i><br/>";
						}
						if (!empty($recipient_user_fullname)) {
							$retHTML .= Dict::getValue("recipient").": <i>".$recipient_user_fullname."</i><br/>";
						}
						$retHTML .= date("Y.m.d. H:i:s", strtotime($msg["created_on"]))."<br/>";
					$retHTML .= "</div>";
				$retHTML .= "</div>";
			$retHTML .= "</li>";

			$prev_message_group_id = $msg["message_group_id"];

			$msgCount++;
		}

		if (!$oneThreadMode && count($msgs)) {
			$retHTML .= "</ol>";

			if ($prev_editable) {
				$retHTML .= '<textarea id="'.$prev_message_group_id.'" class="messageBox">';
				$retHTML .= '</textarea>';
			}

			$retHTML .= "</div>";
			$retHTML .= "</div>";

			$retHTML .= '<script type="text/javascript">';
				$retHTML .= "
					$('#myChat div.chatThreadContents div.chatThreadTitle').unbind('click');

					$('#myChat div.chatThreadContents div.chatThreadTitle').click(function () {
						console.log('CLICKED!', $(this).parent('.chatThreadContents').children('.chatThreadContent').length);

						if ($(this).parent('.chatThreadContents').children('.chatThreadContent').hasClass('hidden')) {
							console.log('REMOVE');
							$(this).parent('.chatThreadContents').children('.chatThreadContent').removeClass('hidden');
						} else {
							console.log('ADD');
							$(this).parent('.chatThreadContents').children('.chatThreadContent').addClass('hidden');
						}

//						var api = $('#myChat > .chatContent > .chatRealContent').data('jsp');
//						api.reinitialise();
					});

					$('#myChat div.chatThreadContents textarea').unbind('keypress');

					$('#myChat div.chatThreadContents textarea').bind('keypress', function(event) {
						if (event.which === 13) {
							event.preventDefault();

							if ($(this).val()) {
								showLoader(true);

								var message_group_id = $(this).attr('id');

								var data = 'id='+message_group_id+'&chatWithUser='+$('#chatWithUser_'+message_group_id).val()+'&value='+$(this).val();

								$.ajax({
									type: 'POST',
									url: '".baseURL()."/message/save',
									data: data,
									async: true,
									success: function(resp1) {
										$.ajax({
											type: 'POST',
											url: '".baseURL()."/message/index',
											data: 'oneThread='+message_group_id,
											async: true,
											dataType: 'json',
											success: function(resp2) {
//												console.log('#chatThreadConversation_'+message_group_id, resp2.html);
												$('#chatThreadConversation_'+message_group_id).html(resp2.html);

//												var api = $('#myChat > .chatContent > .chatRealContent').data('jsp');
//												api.reinitialise();

												hideLoader();
											},
											error: function() {
												hideLoader();
											}
										});
									},
									error: function() {
										hideLoader();
									}
								});

								$(this).val('');
							}
						}
					});
				";
			$retHTML .= '</script>';
		}

		if (count($msgs)) {
			echo json_encode(array(
				'msgCount' => count($msgs),
				'html' => $retHTML,
			));
		} else {
			echo json_encode(array(
				'msgCount' => 0,
				'html' => "",
			));
		}
	}

	public function actionSave()
	{
		$id = Yang::post('id');
		$chatWithUser = Yang::post('chatWithUser');
		$value = Yang::post('value');

		if (empty($id) || empty($chatWithUser) || empty($value)) {
			return false;
		}

//		$ids = explode("_", $id);

		$message_group_id = $id;

		$m = new Messaging();
		$m->findThreadParamsByMgID($message_group_id);
		$m->sendMessage(userID(), [$chatWithUser], [], "msg", null, $value);
	}

	public function actionIndex_()
	{
		$this->pageTitle = Dict::getValue(Yang::appName())." - ".Dict::getValue("page_title_message");

		$message_flows = self::getMessageFlows();
		$messages = self::getMessages();

		$this->render('index', array(
			'message_flows'=>$message_flows,
			'messages'=>$messages,
			'title'=>Dict::getValue("page_title_message"),
		));
	}

	/*public function actionCreate()
	{
		$ajax = Yang::post('ajax');
		$message_flow_id = Yang::post('message_flow_id');

		$model = new Message;
		$model->message_flow_id =  $message_flow_id;
		$model->sender_class =  'ahp/absenceapproval';
		$model->recipient = Yang::post('recipient');
		$model->message = Yang::post('new_message');
		$model->status = 2; // status_active
        $model->created_by = userID();
        $model->created_on = date('Y-m-d H:i:s');

        if($model->save())
        {
			$message_id = $model->message_id;
			Grid2AHPApproverController::sendMessageEmail($message_flow_id, $message_id);
        }

        if($ajax == "true")
        {
        	$this->layout = "//layouts/ajax";
        	echo json_encode(array(
				'status' => 'success',
				'ids' => Yang::post('message_flow_id'),
			));
        }
        else
        {
        	$this->redirect('index');
        }
	}

	public function actionMessagesData()
	{
		$ids = Yang::post('ids');
		$idsArray = explode(";", $ids);
		$message_flow_id = $idsArray[0];

		$messages = self::getMessagesByMessageFlowId($message_flow_id);

		$user_id = userID();
		$recipient = '';
		$messageContent = '';
		$i = 0;
		$msg = '';

		if(empty($messages) || count($messages) == 0)
		{
			$employeeAbsence = EmployeeAbsence::model()->findByAttributes(array('employee_absence_id'=>$message_flow_id));
			$recipient = $employeeAbsence['created_by'];

			$messageContent = "
			<div id=\"message_flow_1\">
			    <div class=\"row\">
			        <form name=\"message-form-box\" id=\"message-form-box\" method=\"post\" action=\"/message/create\">
			            <input type=\"hidden\" name=\"ajax\" value=\"true\">
			            <input type=\"hidden\" name=\"message_flow_id\" value=\"".$message_flow_id."\">
			            <input type=\"hidden\" name=\"recipient\" value=\"".$recipient."\">
			            <textarea name=\"new_message\" style=\"width:160px; color: gray\" onclick=\"this.value=''\" onblur=\"if(this.value=='') {this.value='Üzenet küldése';}\">".Dict::getValue("send_message")."</textarea>
			            <input type=\"button\" name=\"replyButton\" value class=\"addmodDialogSave\" title=\"".Dict::getValue("send_message")."\" onclick=\"addMessage();\">
			        </form>
			    </div>
			    <div id=\"message-box\" style=\"width:255px;height:100%;overflow:auto;\">
				";
		}

		foreach ($messages as $message) {
			$i++;

			if($i == 1)
			{
				($message['sender_user_id'] == $user_id) ? $recipient = $message['recip_user_id'] : $recipient = $message['sender_user_id'];

				$messageContent = "
			<div id=\"message_flow_1\">
			    <div class=\"row\">
			        <form name=\"message-form-box\" id=\"message-form-box\" method=\"post\" action=\"/message/create\">
			        	<input type=\"hidden\" name=\"ajax\" value=\"true\">
			            <input type=\"hidden\" name=\"message_flow_id\" value=\"".$message_flow_id."\">
			            <input type=\"hidden\" name=\"recipient\" value=\"".$recipient."\">
			            <textarea name=\"new_message\" style=\"width:147px; color: gray\" onclick=\"this.value=''\" onblur=\"if(this.value=='') {this.value='Válasz küldése';}\">Válasz küldése</textarea>
			            <input type=\"button\" name=\"replyButton\" value class=\"addmodDialogSave\" title=\"Válasz küldése\" onclick=\"addMessage();\">
			        </form>
			    </div>
			    <div id=\"message-box\" style=\"width:255px;height:100%;overflow:auto;\">
				";
			}
			if($msg != $message['message'])
            {
				$messageContent .= "
					    <div class=\"row\" style=\"width:223px;\">
					        <fieldset style=\"width:223px;\">
					            <legend style=\"font-size:11px\">".$message['sender']." (".$message['created_on'].")</legend>
	                			<strong><span style=\"font-size:14px\">".$message['message']."</span></strong>
					        </fieldset>
					    </div>
					    ";
			}
			$msg = $message['message'];
		}

		$messageContent .= "
				</div>
			</div>";

		echo $messageContent;
	}*/

	public function actionDialogMessagesData()
	{
		$ids = Yang::post('ids');
		$idsArray = explode(";", $ids);
		$message_flow_id = $idsArray[0];

		$messages = self::getMessagesByMessageFlowId($message_flow_id);

		$messageContent = '';
		$i = 0;
		$msg = '';

		foreach ($messages as $message) {
			$i++;

			if($msg != $message['message'])
            {
				$messageContent .= "
					    <div class=\"row\" style=\"width:480px;\">
					        <fieldset>
					            <legend style=\"font-size:11px\">".$message['sender']." (".$message['created_on'].")</legend>
	                			<strong><span style=\"font-size:14px\">".$message['message']."</span></strong>
					        </fieldset>
					    </div>
					    ";
			}
			$msg = $message['message'];
		}

		if(count($messages) == 0)
    		echo '<div class="row" style="padding:20px; font-weight:normal">Jelenleg még nincs üzenete.</div>';

		echo $messageContent;
	}

	/*public static function messagesHTML()
	{
		$messagesHTML = "
			<div style=\"margin:5px;margin-top:0px;border: solid 1px #d0d0d1;border-radius:3px;background:#F9F9FA;\" class=\"chartWidget\">
				<div class=\"header\" style=\"font-family:BenchNine;font-size:22px;width:100%;background:#0099DB;color:white;border-top-left-radius:3px;border-top-right-radius:3px;\"><span style=\"padding-left:10px;\">".Dict::getValue("page_title_message")."</span></div>
				<div class=\"container\" style=\"width:265px;\"><div id=\"messagesData\" style=\"margin: 0 auto;width:265px;display:none;padding:5px;height:100%;\"></div></div>
			</div>
		";
		return $messagesHTML.self::messagesJS();
	}

	private static function messagesJS()
	{
		$js = '
			<script type="text/javascript">
				function messagesData(ids) {
					$.ajax({
						type: "POST",
						url: "'.baseURL().'/message/messagesData",
						data: "ids="+ids,
						async: true,
						success: function(resp) {
							$("#messagesData").html(resp);
							$("#messagesData").fadeIn(1000);
						}
					});
				}
			</script>
		';
		return $js;
	}*/

	private static function getMessageFlows()
	{
		$lang = Dict::getLang();
		$user_id = userID();
		$view_all_messages = App::getRight('message',"view_all_messages");

		if($view_all_messages)
		{
			$message_filter = '';
		}
		else
		{
			$message_filter = " AND (m.recipient = '".$user_id."' OR m.created_by = '".$user_id."')";
		}

		$sql = "SELECT m.message_flow_id,
				CONCAT(e.last_name,' ',e.first_name) AS requester,
				(SELECT `dict_value` FROM `dictionary` WHERE `lang`='".$lang."' AND `dict_id` = a.`name_dict_id` AND `valid`=1) AS `state_type`,
				ea.`status`,
				IF (ea.`status` = 4 OR ea.`status` = 5 OR ea.`status` = 6, '".Dict::getValue("request_withdrawn")."', '".Dict::getValue("request")."') request_type,
				(SELECT CONCAT(MIN(`day`),' - ',MAX(`day`)) FROM `employee_absence` WHERE `employee_absence_id`=m.`message_flow_id`) AS `days`,
				CONCAT(e3.last_name,' ',e3.first_name) AS recipient,
				m.created_by AS sender_user_id,
				m.recipient AS recip_user_id
			FROM `message` m
			LEFT JOIN `employee_absence` ea ON ea.`employee_absence_id` = m.`message_flow_id` AND ea.`status`!=".Status::INVALID."
			LEFT JOIN `employee` e ON e.`employee_id` = ea.`employee_id`
			LEFT JOIN `state_type` a ON a.`state_type_id` = ea.`state_type_id` and a.`status`=".Status::PUBLISHED."
			LEFT JOIN `user` u2 ON u2.`user_id` = m.`recipient`
			LEFT JOIN `employee` e3 ON e3.`employee_id` = u2.`employee_id`
			WHERE (m.`status` = 2)".$message_filter."
			GROUP BY m.message_flow_id
			ORDER BY m.created_on DESC";

		$message_flows = dbFetchAll($sql);

		return $message_flows;
	}

	private static function getMessages()
	{
		$lang = Dict::getLang();
		$user_id = userID();
		$view_all_messages = App::getRight('message',"view_all_messages");

		if($view_all_messages)
		{
			$message_filter = '';
		}
		else
		{
			$message_filter = " AND (m.recipient = '".$user_id."' OR m.created_by = '".$user_id."')";
		}


		$sql = "SELECT m.message_flow_id,
				CONCAT(e.last_name,' ',e.first_name) AS requester,
				(SELECT `dict_value` FROM `dictionary` WHERE `lang`='".$lang."' AND `dict_id` = a.`name_dict_id` AND `valid`=1) AS `state_type`,
				ea.`status`,
				IF (ea.`status` = 4 OR ea.`status` = 5 OR ea.`status` = 6, '".Dict::getValue("request_withdrawn")."', '".Dict::getValue("request")."') request_type,
				(SELECT CONCAT(MIN(`day`),' - ',MAX(`day`)) FROM `employee_absence` WHERE `employee_absence_id`=m.`message_flow_id`) AS `days`,
				m.message,
				CONCAT(e2.last_name,' ',e2.first_name) AS sender,
				CONCAT(e3.last_name,' ',e3.first_name) AS recipient,
				m.created_by AS sender_user_id,
				m.recipient AS recip_user_id,
				m.created_on
			FROM `message` m
			LEFT JOIN `employee_absence` ea ON ea.`employee_absence_id` = m.`message_flow_id` AND ea.`status`!=".Status::INVALID."
			LEFT JOIN `employee` e ON e.`employee_id` = ea.`employee_id`
			LEFT JOIN `state_type` a ON a.`state_type_id` = ea.`state_type_id` AND a.`status`=".Status::PUBLISHED."
			LEFT JOIN `user` u ON u.`user_id` = m.`created_by`
			LEFT JOIN `user` u2 ON u2.`user_id` = m.`recipient`
			LEFT JOIN `employee` e2 ON e2.`employee_id` = u.`employee_id`
			LEFT JOIN `employee` e3 ON e3.`employee_id` = u2.`employee_id`
			WHERE (m.`status` = 2)".$message_filter."
			ORDER BY m.`created_on` DESC, m.`message_flow_id` ASC";

		$messages = dbFetchAll($sql);

		return $messages;
	}

	private static function getMessagesByMessageFlowId($message_flow_id)
	{
		$lang = Dict::getLang();
		$user_id = userID();

		if(!empty($message_flow_id))
		{
			$sql = "SELECT m.message_flow_id,
					CONCAT(e.last_name,' ',e.first_name) AS requester,
					(SELECT `dict_value` FROM `dictionary` WHERE `lang`='".$lang."' AND `dict_id` = a.`name_dict_id` AND `valid`=1) AS `state_type`,
					ea.`status`,
					IF (ea.`status` = 4 OR ea.`status` = 5 OR ea.`status` = 6, '".Dict::getValue("request_withdrawn")."', '".Dict::getValue("request")."') request_type,
					(SELECT CONCAT(MIN(`day`),' - ',MAX(`day`)) FROM `employee_absence` WHERE `employee_absence_id`=m.`message_flow_id`) AS `days`,
					m.message,
					CONCAT(e2.last_name,' ',e2.first_name) AS sender,
					CONCAT(e3.last_name,' ',e3.first_name) AS recipient,
					m.created_by AS sender_user_id,
					m.recipient AS recip_user_id,
					m.created_on
				FROM `message` m
				LEFT JOIN `employee_absence` ea ON ea.`employee_absence_id` = m.`message_flow_id` AND ea.`status`!=".Status::INVALID."
				LEFT JOIN `employee` e ON e.`employee_id` = ea.`employee_id`
				LEFT JOIN `state_type` a ON a.`state_type_id` = ea.`state_type_id` AND a.`status`=".Status::PUBLISHED."
				LEFT JOIN `user` u ON u.`user_id` = m.`created_by`
				LEFT JOIN `user` u2 ON u2.`user_id` = m.`recipient`
				LEFT JOIN `employee` e2 ON e2.`employee_id` = u.`employee_id`
				LEFT JOIN `employee` e3 ON e3.`employee_id` = u2.`employee_id`
				WHERE (m.`status` = 2) AND (m.recipient = '".$user_id."' OR m.created_by = '".$user_id."') AND (m.message_flow_id = '".$message_flow_id."')
				ORDER BY m.`created_on` DESC";

			$messages = dbFetchAll($sql);

			return $messages;
		}
	}
}
?>