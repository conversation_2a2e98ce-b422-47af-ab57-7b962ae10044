<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2History\Grid2HistoryController;
	use app\models\Company;
	use app\models\Status;
	use Yang;

`/yii2-only';

#yii2: done

class CompanyController extends Grid2HistoryController
{
	private $user;
	private $defaultEnd;
	private $statusPublished;
	private $usedGroup;
	private $heatmapGroups;
	private $companyControllerShowId;

	public function __construct()
	{
		parent::__construct("company");
		$this->user    					= userID();
		$this->defaultEnd				= App::getSetting("defaultEnd");
		$this->statusPublished 			= Status::PUBLISHED;
		$this->usedGroup 				= App::getSetting("heatmapGroup");
		$this->heatmapGroups 			= App::getSetting("heatmapGroups");
		$this->companyControllerShowId 	= App::getSetting("companyController_show_id");
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("Company");

		parent::setControllerPageTitleId("page_title_company");

		$this->LAGridRights->overrideInitRights("paging", true);
		$this->LAGridRights->overrideInitRights("search", true);
		$this->LAGridRights->overrideInitRights("search_header", true);
		$this->LAGridRights->overrideInitRights("select", true);
		$this->LAGridRights->overrideInitRights("multi_select", true);
		$this->LAGridRights->overrideInitRights("column_move", true);
		$this->LAGridRights->overrideInitRights("reload_sortings", true);
		$this->LAGridRights->overrideInitRights("details", false);
		$this->LAGridRights->overrideInitRights("column_move", true);
		$this->LAGridRights->overrideInitRights("col_sorting", true);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);
		$this->LAGridDB->enableSQLMode();

		$defaultEnd = $this->defaultEnd;
		$statusPublished = $this->statusPublished;
		$filters = Yang::session('company_filters',[]);

		if (isset($filters["date"]))
		{
			$art = new ApproverRelatedGroup;
			$gargSQL = $art->getApproverReleatedGroupSQL("Company", "companyMainData", false, "'{date}'", "AND", $filters["date"]);
	
			$SQL = "
				SELECT *
				FROM company
				WHERE `status` = $statusPublished
					AND '{date}' BETWEEN `valid_from` AND IFNULL(`valid_to`,'$defaultEnd')
					{$gargSQL["where"]}
				ORDER BY `company_name`
			";
			$this->LAGridDB->setSQLSelection($SQL, "row_id");
		}

		parent::G2BInit();
	}

	/**
	 * Kereső értékek átadása sessionnek az oszlopok felépítéséhez
	 * @return void
	 */
	public function actionSetInitProperties()
	{
		Yang::setSessionValue('company_filters', requestParam('searchInput'));

		parent::actionSetInitProperties();
	}

	public function search()
	{
		return [
			'date' 	 => ['col_type'=>'ed', 'dPicker'=>true, 'width'=>'*', 'label_text'=>Dict::getValue("date"), 'default_value'=>date('Y-m-d')],
			'submit' => ['col_type'=>'searchBarReinitGrid', 'width'=>'*', 'label_text'=>''],
		];
	}

	/**
	 * @return array customized grid columns (name=>params)
	 */
	public function columns()
	{
		$filters = Yang::session('company_filters',[]);
		if (!isset($filters["date"])) { return []; }

		$id = ($this->companyControllerShowId || $this->user == '6acd9683761b153750db382c1c3694f6') ?
				['company_id'	=> ['grid'=>true, 'width'=>260, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'edit'=>false]] :
				[];

		$column = [
			'company_name'		 => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed'],
			'registration_number'=> ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed'],
			'tax_number'		 => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed'],
			'vat_number'		 => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed'],
			'country'			 => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'combo',
				'options'	=>	[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'	=> new Country(),
					'comboId'				=> 'country_sign',
					'comboValue'			=> 'country_sign',
				],
			],
			'minimal_group_count' => ['grid'=>false ,'window'=>false ,'col_type'=>'ed', 'width'=>'300', 'default_value'=>'0'],
			'note'				 => ['grid'=>false, 'width'=>200, 'window'=>true, 'export'=> false, 'col_type'=>'ed', 'line_break'=>true],
			'valid_from'		 => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'dPicker'=>true],
			'valid_to'			 => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'dPicker'=>true],
		];
		if ($this->usedGroup == 'company' || strpos( $this->heatmapGroups, 'company') !== false) {
			$column["minimal_group_count"]['grid'] = true;
			$column["minimal_group_count"]['window'] = true;
		}
		$column = $this->columnRights($column);
		return Yang::arrayMerge($id, $column);
	}
}
