<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\models\Status;
	use Yang;

`/yii2-only';


#yii2: done

class ExportSAPController extends Controller
{
	private $sap_in	= "P10";
	private $sap_out= "P20";
	private $limit=200;
	
	public function actionIndex()
	{
		$SQL="
			SELECT
				asp_row_id as asp_row_id,
				reg_row_id as reg_row_id
			FROM `reg_to_sap` rtp
			ORDER BY created_on desc
			LIMIT 1
		";
		
		$sync=dbFetchRow($SQL);
		
		echo "asp_row_id: ".$sync['asp_row_id']. "</br>";
		echo "reg_row_id: ".$sync['reg_row_id']. "</br>";
		
		$SQL="
			SELECT distinct
				reg.`row_id` as eventid,
				DATE_FORMAT(DATE(reg.`time`), '%Y%m%d') as regdate,
				TIME_FORMAT(TIME(reg.`time`), '%H%i%S') as regtime,
				reg.`card` + 900000 as cardnum,
				IF(reg.`event_type_id`='NMB','".$this->sap_in."','".$this->sap_out."') as direction,
				reg.terminal_id as readerid
			FROM `registration` reg
			LEFT JOIN terminal t ON
					reg.terminal_id=t.terminal_id
				AND t.`status`=".Status::PUBLISHED."
				AND DATE(reg.`time`) BETWEEN t.`valid_from` AND IFNULL(t.`valid_to`, '".App::getSetting("defaultEnd")."') 
			WHERE 
					reg.`status`=".Status::PUBLISHED."
				AND (reg.`event_type_id`='NMB' OR reg.`event_type_id`='NMK')
				AND t.`important`=1
				AND reg.`row_id`>".$sync['reg_row_id']."
			ORDER BY eventid";
		
		$newRows=dbFetchAll($SQL);
		
		echo "Number of new rows: ".count($newRows). "</br>";
		
		$number=0;
		$asp_row_id=$sync['asp_row_id'];
		$send="INSERT ALL ";
		if(!empty($newRows))
		{
			$nowdate=date("Ymd");
			$nowtime=date("His");
			for($i=0;$i<count($newRows) && $number<$this->limit;++$i)
			{
				$card=$newRows[$i]['cardnum'];
				while(strlen($card)<8)
				{
					$card="0".$card;
				}
				++$asp_row_id;
				$send.="INTO CC1TEV (PDSNR, ZAUSW, LDATE, LTIME, ERDAT, ERTIM, SATZA, TERID, MANDT) values ";
				$send.="($asp_row_id,'$card','".$newRows[$i]['regdate']."','".$newRows[$i]['regtime']."',";
				$send.="'$nowdate','$nowtime','".$newRows[$i]['direction']."','".$newRows[$i]['readerid']."','010') ";
				++$number;
			}
			
			$send.="SELECT * FROM DUAL";
			
			echo "Insert is ready.</br>";
			
			$SAPDBName=Yang::getParam('SAPDBName');
			
			Yii::app()->$SAPDBName->createCommand($send)->execute();
			
			$save="INSERT INTO `reg_to_sap` values(null,$asp_row_id,".$newRows[$number-1]['eventid'].",NOW());";
			
			dbExecute($save);
		}
		
		
	}
}
?>
