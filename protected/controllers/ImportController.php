<?php

'yii2-only`;

	namespace app\controllers;
	use PDO;
	use app\components\Controller;
	use app\controllers\ImportController;
	use app\models\ImportLog;
	use app\models\Status;
	use Yang;

`/yii2-only';


#yii2: done

/*
 * <AUTHOR> Autonom Kft. (<EMAIL>)
 * @link http://www.login.hu
 * @version 1.0
 * @package -
 *
 * @file \protected\controllers\ImportController.php
 */

class ImportController extends Controller
{
	protected $mysql_charset_map = array(
										'big5'         => 'big5',
										'cp-866'       => 'cp866',
										'euc-jp'       => 'ujis',
										'euc-kr'       => 'euckr',
										'gb2312'       => 'gb2312',
										'gbk'          => 'gbk',
										'iso-8859-1'   => 'latin1',
										'iso-8859-2'   => 'latin2',
										'iso-8859-7'   => 'greek',
										'iso-8859-8'   => 'hebrew',
										'iso-8859-8-i' => 'hebrew',
										'iso-8859-9'   => 'latin5',
										'iso-8859-13'  => 'latin7',
										'iso-8859-15'  => 'latin1',
										'koi8-r'       => 'koi8r',
										'shift_jis'    => 'sjis',
										'tis-620'      => 'tis620',
										'utf-8'        => 'utf8',
										'windows-1250' => 'cp1250',
										'windows-1251' => 'cp1251',
										'windows-1252' => 'latin1',
										'windows-1256' => 'cp1256',
										'windows-1257' => 'cp1257',
										);
	public $imported_on		= '';

	public $layout			= 'main';

	const STATUS_ERROR = 0;
	const STATUS_OK = 1;

	const LOG_LEVEL_ALL = 0;
	const LOG_LEVEL_ERROR = 9;

	/**
	 * Log level:  0 - all / ... / 9 - error
	 * @var integer
	 */
	public $log_level	= 9;
	public $log_onto_screen	= true;
	public $console_mode	= false;
	public $log				= '';

	public $temp_table			= '';

	public $import_file_format	= array('charset'=> 'iso-8859-2',
									'field_terminated_by'=> ';',
									'enclosed_by' => '\"',
									'lines_terminated_by'=> '\\n',
									);

	public $import_temp_fields	= '';

	public $export_temp_fields = '';

	public $export_temp_group_by = '';
	// Összehasonlított mezők? array(mező -> array(mezők))
	public $compared_fields	= array();

	public $model			= '';

	public $table			= '';

	public $table_row_ids			= array();
	// Másolt? array(mező -> array(mezők))
	public $copied_fields	= array();

	public function __construct($model, $url, $temp_table, $import_file_format,
								$import_temp_fields, $export_temp_fields,
								$export_temp_group_by, $compared_fields,
								$table, $table_row_ids, $copied_fields,
								$imported_on
								)
	{
		parent::__construct($url);
		$this->set_data($model, $temp_table, $import_file_format,
								$import_temp_fields, $export_temp_fields,
								$export_temp_group_by, $compared_fields,
								$table, $table_row_ids, $copied_fields,
								$imported_on);
	}

	public function set_data($model, $temp_table, $import_file_format,
								$import_temp_fields, $export_temp_fields,
								$export_temp_group_by, $compared_fields,
								$table, $table_row_ids, $copied_fields,
								$imported_on= false)
	{
		if ($imported_on) {
			$this->imported_on			= $imported_on;
		} else {
			$this->imported_on			= date('Y-m-d H:i:s');
		}

		$this->model				= $model;
		$this->console_mode			= (get_class(Yii::app())=='CConsoleApplication');
		$this->temp_table			= $temp_table;
		if ($import_file_format) {
			$this->import_file_format	= $import_file_format;
		}
		$this->import_temp_fields	= $import_temp_fields;
		$this->export_temp_fields	= $export_temp_fields;
		$this->export_temp_group_by	= $export_temp_group_by;
		$this->compared_fields		= $compared_fields;
		$this->table				= $table;
		$this->table_row_ids		= $table_row_ids;
		$this->copied_fields		= $copied_fields;
	}

	public function print_log($log)
	{
		if ($this->log_onto_screen) {
			if ($this->console_mode) {
				print(mb_convert_encoding($log, 'Windows-1252') );	// ISO-8859-2	, 'UTF-8'
			} else {
				print(nl2br($log));
			}
		}
	}

	public function beforeAction($action)
	{
		if (Yang::isGuest()) {
			$this->redirect(array(Yang::getParam('permdeniedUrl')));
		}
	   return true;
	}

	public function actionIndex()
	{
		$this->layout = 'main';
		$this->render('index');
	}

	/**
	 * deleteTemporaryData(): Delete temporary data from $this->temp_table
	 */
	public function deleteTemporaryData()
	{
		$tempTable = $this->temp_table;
		dbExecute("TRUNCATE $tempTable;");

		$log = 'Átmeneti adatok törölve.' . "\n\r";	// Temporary data from $this->temp_table deleted
		$this->addToLog($log, ImportController::LOG_LEVEL_ALL);
	}

	/**
	 * importCSVFile($file) - import $file to $this->temp_table, Fontos, hogy az utolsó üres mezőt nem veszi figyelembe!
	 *
	 * @param string $file
	 * @throws CHttpException
	 */
	public function importCSVFile($file)
	{
		$field_number = count( explode(',', str_replace(' ', '', $this->import_temp_fields) ) );

		$log = 'Importált fájl: ' . basename($file) . "\n\r";
		$this->addToLog($log, ImportController::LOG_LEVEL_ALL);

		$charset = $this->import_file_format['charset'];
		if (isset($this->mysql_charset_map[strtolower($charset)])) {
			$charset = $this->mysql_charset_map[strtolower($charset)];
		}

		$file= str_replace('\\', '/', $file);
		// "LOAD DATA LOCAL INFILE forbidden" esetén -> C:\xampp\mysql\bin\my.ini:
		if (!defined('MYSQL_OPT_LOCAL_INFILE')) define('MYSQL_OPT_LOCAL_INFILE', true);
		if (!defined('CLIENT_LOCAL_FILES')) define('CLIENT_LOCAL_FILES', true);

		$connection = Yang::app()->db; // #see https://innote.login.hu/n-7wpd5adl

		$original_charset = 'utf8';
		$command_temp	 = $connection->createCommand('SHOW VARIABLES LIKE "CHARACTER_SET_DATABASE";');
		$dataReader_temp = $command_temp->query();
		if (($row_temp=$dataReader_temp->read()) !== false) {
			$original_charset = $row_temp['Value'];
		}

		$connection->setAttribute(PDO::MYSQL_ATTR_LOCAL_INFILE, true);
		$connection->getPdoInstance()->setAttribute(PDO::MYSQL_ATTR_LOCAL_INFILE, true);
		$connection->createCommand("SET CHARACTER SET '".$charset."';")->execute();
		$connection->createCommand("SET collation_connection = 'utf8_general_ci';")->execute();

		// Import with MySQL LOAD DATA
		//if (true)
		if (false)
		{
			// Info: http://dev.mysql.com/doc/refman/5.7/en/load-data.html
			$sql = "LOAD DATA LOCAL INFILE '".$file."'
						INTO TABLE `".$this->temp_table."`
						CHARACTER SET '".$charset."'
						FIELDS
							TERMINATED BY '".$this->import_file_format['field_terminated_by']."'
							ENCLOSED BY '".$this->import_file_format['enclosed_by']."'
						LINES
							TERMINATED BY '".$this->import_file_format['lines_terminated_by']."'
						 IGNORE 1 LINES
						(".$this->import_temp_fields.")
						";
			$transaction = $connection->beginTransaction();
			try
			{
				$connection->createCommand($sql)->execute();
				$transaction->commit();
			}
			catch(\Exception $e)
			{
				$log = 'HIBA!'."\n\r";
				$log .= print_r($e, true) . "\n\r";
				$this->addToLog($log, ImportController::LOG_LEVEL_ERROR);
				$transaction->rollBack();
			}
		} else {
			$row = 1;
			$num = 0;
			if (($file_res = fopen($file, "r")) !== FALSE) {
				while (($data = @fgetcsv($file_res, 0,
									$this->import_file_format['field_terminated_by'],
									$this->import_file_format['enclosed_by'])) !== FALSE) {
					if ($row == 1) {
						$num = 0;
						foreach ($data as $field) {
							if ($field!='') {
								$num++;
							}
						}
					} else {
						if ($field_number!=$num) {
							throw new CHttpException(500, 'Field number not equals with CSV file columns. (table:'.$field_number.'!='.$num.')');
						}
						if ($num>0) {
							$column = 0;
							$sql_delimiter = '';
							$sql = "INSERT INTO `".$this->temp_table."` (".$this->import_temp_fields.") VALUES (";
							foreach ($data as $value) {
								if ($column<$num) {
									$sql .= $sql_delimiter."'".$value."'";
									$sql_delimiter = ', ';
									$column++;
								}
							}
							$sql .= ");";
							$connection->createCommand($sql)->execute();
						}
					}
					$row++;
				}
				fclose($file_res);
			}
		}

		$connection->createCommand("SET CHARACTER SET '".$original_charset."';")->execute();

		$log = 'Feldolgozás kész!'."\n\r";
		$this->addToLog($log, ImportController::LOG_LEVEL_ALL);

		$className = $this->model.'Import';
		$count = 0;
		if (is_object($className)) {
			$results = $className::model()->findAll();
			$count = count( $results );
		}

		$log = 'Rekordok száma: '.$count."\n\r";
		$this->addToLog($log, ImportController::LOG_LEVEL_ALL);
	}

	/**
	 * importTempData(): Import temporary data from $this->temp_table table to model
	 */
	public function importTempData()
	{
		$rows_all	 = 0;
		$rows_new	 = 0;
		$rows_mod	 = 0;
		$rows_same	 = 0;

		$ok = true;

		set_time_limit(300);
		$new_valid_to		 = date('Y-m-d', strtotime('-1 day'));
		$new_valid_from		 = date('Y-m-d');
		$valid_to_infinity	 = NULL;

		$connection = Yang::app()->db; // #see https://innote.login.hu/n-7wpd5adl
		// 	other1, other2, other3
//		$sql= 'SELECT client_id, name1, name2, country, postal_code, city, street, tax_number, bank_country, bank_key, bank_account'
//				. ' FROM client_sap'
//				. ' GROUP BY client_id, name1, name2, country, postal_code, city, street, tax_number, bank_country, bank_key, bank_account'
//				. ' ORDER BY client_id, name1, name2'
//				. ';';

		$sql= 'SELECT '.$this->export_temp_fields
				. ' FROM '.$this->temp_table;
		if ($this->export_temp_group_by!='')
		{
			$sql.= ' GROUP BY '.$this->export_temp_group_by
					. ' ORDER BY '.$this->export_temp_group_by;
		}
		$sql .= ';';

		$command_temp	 = $connection->createCommand($sql);
		$dataReader_temp = $command_temp->query();

		$log = '';
		while(($row_temp=$dataReader_temp->read()) !== false) {
			$rows_all++;
			$log = '';

			$insert_as_new_record = true;

			$where_table_row_id = '';
			foreach ($this->table_row_ids as $table_row_id)
			{
				if ($where_table_row_id != '') {
					$where_table_row_id .= ' AND ';
				}
				$where_table_row_id .= $table_row_id.'=\''.$row_temp[$table_row_id].'\''.' ';
			}
			// Érvényes client adat keresése...
			$sql = 'SELECT * FROM '.$this->table
					. ' WHERE '.$where_table_row_id
					. ' AND valid_from<=\''.$new_valid_from.'\''
					. ' AND (valid_to IS NULL or valid_to>=\''.$new_valid_from.'\')'
					. ' AND status!=4;';

			$command_client = $connection->createCommand($sql);
			$dataReader_client = $command_client->query();
			// Client: Adatokk betöltése...
			//$this->print_log(__LINE__.": DB rows:\n");
			$found_old = false;
			while(($row_record=$dataReader_client->read()) !== false)
			{
				$found_old = true;
				$insert_as_new_record = false;

				$comparing = array();
				foreach ($this->compared_fields as $row_field => $temp_fields) {
					$new_value= '';
					foreach ($temp_fields as $temp_field) {
						if (isset($row_temp[$temp_field])) {
							$new_value .= $row_temp[$temp_field];
						} else {
							$new_value .= $temp_field;
						}
					}
					$comparing[$row_field]= $new_value;
				}

				foreach ($comparing as $field => $value) {
					if ($row_record[$field] != $value) {
						$insert_as_new_record= true;
					}
				}

				if ($insert_as_new_record) {
					$className = $this->model;
					$new_record = $className::model()->findByPk($row_record['row_id']);
					$new_record->valid_to	= $new_valid_to;
					$new_record->status		= Status::STATUS_ARCHIVED;
					$new_record->save(false);
					$log .= 'Változás mentve! '.$row_record['row_id'].'>'.$new_valid_to."\n\r";
				}
			}

			if ($insert_as_new_record) {
				$new_record = new $this->model;
				$new_record->setIsNewRecord(true);
				foreach ($this->copied_fields as $row_field => $temp_fields)
				{
					$new_value = '';
					foreach ($temp_fields as $temp_field)
					{
						if (isset($row_temp[$temp_field])) {
							$new_value .= $row_temp[$temp_field];
						} else {
							$new_value .= $temp_field;
						}
					}
					$new_record->$row_field = $new_value;
				}
				$className = $this->model;
				$new_record->valid_from				= $new_valid_from;
				$new_record->valid_to				= $valid_to_infinity;
				$new_record->status					= Status::PUBLISHED;
				$new_record->created_by				= $className.' import function';
				$new_record->created_on				= new CDbExpression('NOW()');

				foreach ($this->table_row_ids as $table_row_id)
				{
					$new_record->$table_row_id = $row_temp[$table_row_id];
				}

				if ($new_record->valid_from == '0000-00-00') {
					$new_record->valid_from = $new_valid_from;
				}
				if ($new_record->valid_to == '0000-00-00') {
					$new_record->valid_to = $valid_to_infinity;
				}
				if ($found_old) {
					$log .= 'MÓD: ';
					$rows_mod++;
				} else {
					$log .= 'ÚJ: ';
					$rows_new++;
				}
				$where_table_row_id= '';
				foreach ($this->table_row_ids as $table_row_id)
				{
					if ($where_table_row_id!='') {
						$where_table_row_id.= ',';
					}
					$where_table_row_id .= $row_temp[$table_row_id];
				}
				$log .= $where_table_row_id.' '.' adat:'."\n\r";	// $row_temp[$table_row_id]
				if (!$new_record->validate()) {
					foreach ($new_record->getErrors() as $field => $errors) {
						$log .= 'HIBA! '.$field.' mező:'."\n\r";
						foreach ($errors as $error) {
							$log .= '- '.$error."\n\r";
						}
					}
				}
				$this->addToLog($log, ImportController::LOG_LEVEL_ALL);
				$client_id = $new_record->save(false);
			} else {
				$rows_same++;
			}
		}

		$log .= 'Összes adat: ' . $rows_all . "\n\r";
		$log .= '- új         : ' . $rows_new . "\n\r";
		$log .= '- módosult   : ' . $rows_mod . "\n\r";
		$log .= '- változatlan: ' . $rows_same . "\n\r";

		$this->addToLog($log, ImportController::LOG_LEVEL_ALL);

		return $ok;
	}

	/**
	 * logImport(): Save import log to import_log table
	 *
	 * @param string $table
	 * @param string $imported_by
	 * @param string $file
	 * @param boolean $isOk
	 */
	public function logImport($table, $imported_by, $file, $isOk)
	{
		$import_log					 = new ImportLog;
		$import_log->setIsNewRecord(true);
		$import_log->table			 = $table;
		$import_log->imported_by	 = $imported_by;
		$import_log->imported_on	 = $this->imported_on;
		$import_log->path			 = dirname($file);
		$import_log->file			 = basename($file);
		$import_log->file_mdatetime	 = date("Y-m-d H:i:s", filemtime($file));
		$import_log->status			 = $isOk;
		$import_log->log			 = $this->log;
		if (!$import_log->validate()) {

		}
		$import_log->save(false);
		return $import_log->row_id;
	}

	public function addToLog($log, $level)
	{
		if ($level >= $this->log_level) {
			$this->log .= $log;
			$this->print_log($log);
		}
	}
}