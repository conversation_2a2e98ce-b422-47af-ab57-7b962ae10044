<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\API\InitUserEvent;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use app\models\Employee;
	use Yang;

`/yii2-only';


#yii2: done

class LogReportController extends Grid2Controller
{
	protected $filters;
	protected $gridSQL;
	protected $gridMode=1;
	protected $eventRowId;
	protected $userEvent;
	protected $rowId=0;
	protected $activity=0;

	public function __construct()
	{
		parent::__construct("logReport");
		if (!is_null(Yang::session('log_report_activity'))) $this->gridMode=Yang::session('log_report_activity');
	}

	//public function actionIndex()
	public function actionIndex($layout = '//Grid2/layouts/indexLayout', $view = '/Grid2/index', $params = array())
	{
		$params=[];
		$params['activity'] = $this->gridMode;

		$buttons1 = Array(
						array(
							"type"		=> "button",
							"id"		=> "asd",
							"class"		=> "asd",
							"name"		=> "asd",
							"img"		=> "/images/status_icons/st_accept.png",
							"label"		=> Dict::getModuleValue("ttwa-base","previous_year"),
							"onclick"	=> 'btnClick(11);',
						),

					);
		$params['logControllerButtons1'] = $buttons1;

		$buttons2 = Array(
						array(
							"id"		=> "logController21",
							"class"		=> "logController21",
							"name"		=> "logController21",
							"img"		=> "/images/status_icons/st_prev.png",
							"label"		=> Dict::getModuleValue("ttwa-base","previous_year"),
							"onclick"	=> 'btnClick(21);',
							),

						);
		$params['logControllerButtons2'] = $buttons2;

		parent::actionIndex('//Grid2/layouts/indexLayout', '//logReport/index', $params);
	}

	public function actionChangeGridMode()
        {
            Yang::setSessionValue('log_report_row_id', requestParam('row_id'));
            Yang::setSessionValue('log_report_activity', requestParam('activity'));
            $this->gridMode = requestParam('activity');
            echo "row_id: ".Yang::session('log_report_row_id')."/activity: ".Yang::session('log_report_activity');
	}

	public function actionSetInitProperties()
	{
		Yang::setSessionValue('log_report_filters', requestParam('searchInput'));
		
		parent::actionSetInitProperties();
	}

	protected function G2BInit()
	{
		$this->gridMode=1;
		$activity=Yang::session('log_report_activity',1);
		{
			if ($activity==2)
			{
				$this->gridMode=2;
				$this->eventRowId=Yang::session('log_report_row_id',0);
			}
			elseif ($activity==5)
			{
				$this->gridMode=3;
			}
			elseif ($activity==6)
			{
				$this->gridMode=4;
			}
			else
			{
				$this->gridMode=1;
			}
		}

		parent::setControllerPageTitleId("page_title_log_report","ttwa-base");
		$this->userEvent=new InitUserEvent();
		{
			$this->LAGridRights->overrideInitRights("paging",			true);
			$this->LAGridRights->overrideInitRights("search",			true);
			$this->LAGridRights->overrideInitRights("search_header",		true);
			$this->LAGridRights->overrideInitRights("select",			true);
			$this->LAGridRights->overrideInitRights("multi_select",		false);
			$this->LAGridRights->overrideInitRights("column_move",		true);
			$this->LAGridRights->overrideInitRights("col_sorting",		true);
			$this->LAGridRights->overrideInitRights("reload_sortings",	true);
			$this->LAGridRights->overrideInitRights("details",			false);
			$this->LAGridRights->overrideInitRights("init_open_search",	true);

			parent::setReportOrientation("landscape");

			$this->LAGridDB->enableSQLMode();

		}
		if ($this->gridMode==1)
		{
			$this->gridSQL=$this->getGridSQL();
            $this->rowId="row_id";
		}
		elseif ($this->gridMode==2)
		{
			$this->gridSQL=$this->getEventSQL();
            $this->rowId="row_id";
		}
		$this->LAGridDB->setSQLSelection($this->gridSQL, $this->rowId);//payroll

		parent::G2BInit();

	}

	public function getEventSQL()
	{
		if (empty($this->eventRowId)) return $this->getGridSQL();

		$defaultEnd	= App::getSetting("defaultEnd");
		$lang		= Dict::getLang();

		$sql="SELECT * FROM (SELECT * FROM user_event event".
				") e where row_id=".  $this->eventRowId;
		$res = dbFetchAll($sql);

		if(isset($res) AND is_array($res))
		{
			foreach ($res as $row)
			{
				$event=$row;
			}
		}
		if (!((isset($event) AND is_array($event))))
		{
			return $this->getGridSQL();
		}

		$date=$event['created_on'];
		$table=$event['table_name'];
		$new=$event['new_row_id'];
		$oldValues=$event['old_values'];
		unset($res);
		$sql="SELECT * from $table where row_id=$new";
		try
		{
			$res = dbFetchAll($sql);
		}
		catch (Exception $exc)
		{
		}

		if(isset($res) AND is_array($res))
		{
			foreach ($res as $row)
			{
				$newRec=$row;
				if (isset($row['pre_row_id']))
				{
					$old=$row['pre_row_id'];
				}
			}
		}
		if (isset($old) && !empty($old))
		{
			$sql="SELECT * from $table where row_id=$old";
			unset($res);
			$res = dbFetchAll($sql);
			if(isset($res) AND is_array($res))
			{
				foreach ($res as $row)
				{
					$oldRec=$row;
				}
			}
		}
		if (!(isset($oldRec) && is_array($oldRec)))
		{
			if (!empty($oldValues))
			{
				$oldRec=unserialize($oldValues);
			}
		}
		$sql2 = "SELECT a.COLUMN_NAME, a.COLUMN_COMMENT FROM information_schema.COLUMNS a WHERE a.TABLE_NAME = '$table'";
		$sql = "SHOW COLUMNS FROM $table";
		unset($res2);
		unset($res);
		try
		{
			$res = dbFetchAll($sql);
		}
		catch (Exception $exc)
		{
		}

		try
		{
			$res2 = dbFetchAll($sql2);
		}
		catch (Exception $exc)
		{
		}

		if (isset($oldRec) && is_array($oldRec) && !(isset($res) && is_array($res)))
		{
			$res=[];
			foreach ($oldRec as $key => $value)
			{
				$x=[];
				$x['Field']=$key;
				$x['sziszi']=true;
				$res[]=$x;
			}
		}
		$sql = "";
		$mi  = "";
		if (isset($res) && is_array($res))
		{
			foreach ($res as $row)
			{
				$field=$row['Field'];
				$fieldName=$row['Field'];
				if (!isset($row['sziszi']))
				{
					$fieldName=Dict::getValue($field);
					if (empty($fieldName) && isset($res2) && is_array($res2))
					{
						foreach ($res2 as $row2)
						{
							if (empty($fieldName) && $row2['COLUMN_NAME']==$field)
							{
								if (empty($fieldName)) $fieldName=$row2['COLUMN_COMMENT'];
								break;
							}
						}
					}
					if (empty($fieldName)) $fieldName=$row['Field'];
				}
				$showNew="";
				if (isset($newRec) && isset($newRec[$field]))
				{
					$showNew=$this->userEvent->showTableField($table, $field, $newRec[$field], $newRec, $date, $defaultEnd, $lang);
				}
				$showOld="";
				if (isset($oldRec) && isset($oldRec[$field]))
				{
					if (isset($newRec) && isset($newRec[$field]))
					{
						$showOld=$this->userEvent->showTableField($table, $field, $oldRec[$field], $oldRec, $date, $defaultEnd, $lang);
					}
					else
					{
						$showOld=htmlspecialchars($oldRec[$field]);
					}
				}
				$elteres="";
				if ($showNew<>$showOld)
				{
					$elteres="*****";
				}
				$sql .= $mi.'SELECT "'.$fieldName.'" as mezo, "'.$showNew.'" as uj, "'.$elteres.'" as modified, "'.$showOld.'" as regi';
				$mi=" UNION ";
			}
		}
		if (empty($sql))
		{
			$sql .= 'SELECT "Nincs mező" as mezo, "Nincs új érték" as uj, "Nincs etérés" as modified, "Nincs régi érték" as regi';
		}
		return $sql;
	}

	protected function getStatusButtons($gridID = null)
	{
		$actButtons = parent::getStatusButtons($gridID);

		$modButtons = [];

		if($this->gridMode == 1){
			$modButtons["next"] = array(
								"type" => "button",
								"id" => "changeMode",
								"class" => "view",
								"name" => "changeMode",
								"img" => "/images/status_icons/st_next.png",
								"label" => Dict::getModuleValue("ttwa-base","operation_view"),
								"onclick" => 'changeGridModeBtnClick()',
							);
		} elseif($this->gridMode == 2) {
			$modButtons["next"] = array(
								"type" => "button",
								"id" => "changeMode",
								"class" => "view",
								"name" => "changeMode",
								"img" => "/images/status_icons/st_prev.png",
								"label" => Dict::getModuleValue("ttwa-base","all"),
								"onclick" => 'changeGridModeBtnClick()',
							);
		}

		$buttons = Yang::arrayMerge($modButtons, $actButtons);

		return $buttons;
	}

	protected function getFieldSQL($table, $field)
	{
	}

	protected function getGridSQL()
	{
		$userId = userID();
		$sql="SELECT event.row_id, event.new_row_id, event.created_by, user.username, event.created_on, event_type.title AS event_name"
				. ", event.page_title"
				. ", event.table_name"
				. ", event.to_show as identified"
				. ", event.modified_fields AS modified"
				. " FROM user_event event"
				. " LEFT JOIN user"
				. " ON user.user_id=event.created_by"
				. " LEFT JOIN user_event_type event_type"
				. " ON event_type.event_code=event.event_code";
		$sql	.=" WHERE ('{user}'='0' OR event.created_by='{user}')";
		$sql	.=" AND (date(event.created_on) BETWEEN '{dateFrom}' AND '{dateTo}')";
		$sql	.=" AND (event.table_name LIKE '%{table_name}%')";
		$sql	.=" AND (event.to_show LIKE '%{identified}%')";
		$sql	.=" AND (IFNULL(event.modified_fields, '') LIKE '%{modified}%')";
		$sql 	.=" AND (event.event_code<>99 OR '$userId'='6acd9683761b153750db382c1c3694f6')";

		$sql	.= " ORDER BY event.created_on desc, event.row_id desc"
				;
		return $sql;

	}

	public function search()
	{
		$defaultEnd	= App::getSetting("defaultEnd");
		$userId = userID();
		$empName= Employee::getEmployeeFullnameByUserID($userId);
		if (empty($empName)) $empName="root";


		$monthOptions = array();
		$year = date('Y');
		$month = date('m');
        $day = date('d');
		//$actualMonth = $year.'-'.$month;

		$i = 0;
		for($y=$year-1; $y<=$year+1; $y++) {
			for($m=1; $m<=12; $m++) {
				if(strlen($m) == 1) {
					$m = '0'.$m;
				}
				$monthOptions[$i]['id'] = $y.'-'.$m;
				$monthOptions[$i]['value'] = $y.'-'.$m;
				$i++;
			}
		}

		$userSQL="select user_id as id, username as value from user where status=2";
		$userSQL="select '0' as id, 'Összes' as value union ".$userSQL;

		$employeeSQL="select * from (select employee_id as id, concat(last_name, ' ', first_name, ' ', employee_id) as value from employee "
				. "where status=2 and left(valid_from,7)<='{yearMonth}' and left(IFNULL(valid_to, '$defaultEnd'),7)>='{yearMonth}'"
				. " order by value) e";
		$employeeSQL="select 0 as id, 'Összes' as value union ".$employeeSQL;

		//$eventSQL  ="SELECT *, row_id as id, CONCAT(created_on,':',username,':',event_name,':',table_name,':',identified) AS value from (".$this->getGridSQL().") e";

		$activityArray=array();

		$activityArray[]=array('id'=> '1', 'value' => 'Összes esemény'); //Dict::getValue("work_type_flexible")),
		$activityArray[]=array('id'=> '2', 'value' => 'Kiválasztott esemény'); //Dict::getValue("work_type_framework")),
        $datetime = (new DateTime)->setDate($year, $month, $day);
        $prevmonth = $datetime->modify("-1 months")->format('Y-m-d');

		return array(
			'dateFrom'		=> array(
							'label_text' 	=> Dict::getModuleValue("ttwa-base",'date_from'),
							'col_type' 		=> 'ed',
							'default_value' => $prevmonth,
							'dPicker' 		=> true,
							'onchange' => array("event"),
								),
			'dateTo'		=> array(
							'label_text' 	=> Dict::getModuleValue("ttwa-base",'date_to'),
							'col_type' 		=> 'ed',
							'default_value' => date("Y-m-d"),
							'dPicker' 		=> true,
							'onchange' => array("event"),
								),
			'user'			=>	array(
									'label_text'=> 'Felhasználó', //Dict::getValue("work_type"),
									'col_type'	=> 'combo',
									'options'	=>	array(
													'mode'=>Grid2Controller::G2BC_QUERY_MODE_SQL,
													'sql'=> $userSQL,
												),
							'onchange' => array("event"),
								),
			'table_name'	=> array(
							'label_text' 	=> 'Tábla', //Dict::getModuleValue("ttwa-base",'table'),
							'col_type' 		=> 'ed',
							'default_value' => '',
							'onchange' => array("event"),
								),
			'identified'	=> array(
							'label_text' 	=> 'Érintett', //Dict::getModuleValue("ttwa-base",'identified'),
							'col_type' 		=> 'ed',
							'default_value' => '',
							'onchange' => array("event"),
								),
			'modified'		=> array(
							'label_text' 	=> 'Módosult', //Dict::getModuleValue("ttwa-base",'identified'),
							'col_type' 		=> 'ed',
							'default_value' => '',
							'onchange' => array("event"),
								),
			'submit'		=> array('col_type'=>'searchBarReinitGrid', 'width'=>'*', 'label_text'=>''),
		);
	}

	public function columns()
	{
		$cols=Array();
		if ($this->gridMode==2)
		{
			$cols['mezo']		= array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'200');
			$cols['modified']	= array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'50');
			$cols['uj']			= array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'600');
			$cols['regi']		= array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed');
			return $cols;
		}
		$cols['username']	= array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'200');
		$cols['created_on'] = array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'150');
		$cols['event_name'] = array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'100');
		$cols['table_name'] = array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'200');
		$cols['page_title'] = array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'200');
		$cols['identified'] = array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'400');
		$cols['modified']	= array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'400');
		return $cols;
	}

	public function attributeLabels()
	{
		$cols=Array();
		$cols['username']	= Dict::getValue('username');
		$cols['created_on']	= Dict::getValue('created_on');
		$cols['event_name']	= Dict::getValue('event_name');
		$cols['table_name']	= Dict::getValue('table_name');
		$cols['page_title']	= Dict::getValue('page_title');
		$cols['identified']	= Dict::getValue('identified');
		$cols['modified']	= Dict::getValue('modified');
		return $cols;
	}

}
?>
