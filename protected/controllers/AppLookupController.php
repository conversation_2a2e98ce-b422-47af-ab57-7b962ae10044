<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use app\components\Log;
	use app\models\AppLookup;
	use app\models\Dictionary;
	use Yang;

`/yii2-only';


#yii2: done

class AppLookupController extends Grid2Controller
{
    public $layout = '//layouts/main';
	private $usedLangs;
	
    public function __construct()
    {
            parent::__construct("appLookup");
			$this->usedLangs = explode(';', App::getSetting("used_languages"));
    }
	
    protected function G2BInit()
    {
		$this->LAGridDB->setModelName("AppLookup");

		parent::setControllerPageTitleId("page_title_appLookup");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("add",				true);
		$this->LAGridRights->overrideInitRights("delete",			true);
		$this->LAGridRights->overrideInitRights("inline_modify",	true);
		$this->LAGridRights->overrideInitRights("edit",				true);
		
		$this->LAGridDB->enableSQLMode();
		
		$selectLang = "";
		$join = "";
		foreach ($this->usedLangs as $lang)
		{
			$join .= "LEFT JOIN `dictionary` AS dict_{$lang} ON `app_lookup`.`dict_id` = dict_{$lang}.`dict_id`
							AND dict_{$lang}.`lang` = '{$lang}' \n";
			$selectLang .= "`dict_{$lang}`.`dict_value` AS dict_value_{$lang}, \n";
		}
		        
		$sql = "SELECT DISTINCT `app_lookup`.`row_id`,
								{$selectLang}
								COALESCE(d.`dict_value`, `app_lookup`.`lookup_id`) AS lookup_id,
								COALESCE(d.`dict_value`, `app_lookup`.`lookup_id`) AS lookup_name,
								`app_lookup`.`order`
				FROM `app_lookup`
				LEFT JOIN `dictionary` AS d ON `app_lookup`.`lookup_id` = d.`dict_id`
					AND d.`lang` = '" . Dict::getLang() . "'
				{$join}
				WHERE `app_lookup`.`valid` = 1
					AND (`app_lookup`.`lookup_id` = '{lookup_id}'
					OR '{lookup_id}' = 'ALL')
				ORDER BY `app_lookup`.`order`, `app_lookup`.`lookup_id`, `app_lookup`.`lookup_value`
				";
        		
        $this->LAGridDB->setSQLSelection($sql, 'row_id');
        parent::setGridProperty("splitColumnEnabled", false);
		parent::setGridProperty("splitColumn", 1);
        parent::G2BInit();
	}

	public function search()
	{
		return [
			'lookup_id'	=>	[
									'col_type'	=> 'combo', 
									'width'		=> '*', 
									'label_text'=> Dict::getValue('dropdown'),
									'options'	=>	[
													'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
													'sql'	=> "SELECT `app_lookup`.`lookup_id` AS `id`,
																		COALESCE(dict.`dict_value`, `app_lookup`.`lookup_id`) AS value
																FROM `app_lookup`
																LEFT JOIN `dictionary` AS dict
																	ON `app_lookup`.`lookup_id` = dict.`dict_id`
																	AND dict.`lang` = '" . Dict::getLang() . "'
																WHERE `app_lookup`.`valid` = 1
																GROUP BY `app_lookup`.`lookup_id`
																ORDER BY value",
													'array' => [["id"=>"ALL","value"=>Dict::getValue("all")]]
													],
								],
			
			'submit'	=> ['col_type'=>'searchBarReloadGrid', 'width'=>'*', 'label_text'=>'', 'gridID' => 'dhtmlxGrid',],
		];
	}

    public function columns()
    {		
        $return = [
			'row_id'				=> ['grid' => false, 'window' => false],
			'lookup_id'				=> ['col_type'		=> 'combo', 'window' => true, 'grid' => false,
										'options'		=>	[
															'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
															'sql'	=>
																"SELECT `app_lookup`.`lookup_id` AS `id`,
																		COALESCE(dict.`dict_value`, `app_lookup`.`lookup_id`) AS value
																FROM `app_lookup`
																LEFT JOIN `dictionary` AS dict
																	ON `app_lookup`.`lookup_id` = dict.`dict_id`
																	AND dict.`lang` = '" . Dict::getLang() . "'
																WHERE `app_lookup`.`valid` = 1
																GROUP BY `app_lookup`.`lookup_id`
																ORDER BY value",
															'array' => [["id"=>"","value"=>""]]
															],								
										],
			'lookup_name'			=> ['col_type' => 'ro', 'window' => false, 'grid' => true],
			'order'					=> ['col_type' => 'ed', 'window' => true, 'grid' => true],
        ];
		
		foreach ($this->usedLangs as $lang)
		{
			$return["dict_value_{$lang}"] = ['col_type' => 'ed', 'window' => true,];
		}
		return $return;
    }

    public function attributeLabels()
    {
        $return = [
                'lookup_name'	=> Dict::getValue('dropdown'),
				'order'			=> Dict::getValue('order'),
        ];
		
		foreach ($this->usedLangs as $lang)
		{
			$return["dict_value_{$lang}"] = Dict::getValue("lang_{$lang}");
		}
		
		return $return;
    }
	
	public function actionSave($data = [], $modelName = null, $pk = null, $vOnly = false, $ret = false, $contentId = null)
	{
		$this->layout = "//layouts/ajax";
	
		$form = requestParam('gridID');
		
		$data = requestParam("dialogInput_$form");
		
		$saved	= 1;
		$message = null;
		
		$lookupId = $data['lookup_id'];		
		
		$date	= date("YmdHis");
		$rand	= rand(100, 999);
		$dictId = str_replace(' ', '_', strtolower($lookupId)) . '_' . $date . $rand;
		$lookupValue		= '1';									
		$selectLookupValues ="
							SELECT `lookup_value`
							FROM `app_lookup`
							WHERE `lookup_id` = '{$lookupId}'";
		$lookupValues      = dbFetchColumn($selectLookupValues);
		
		for ($i = 0; $i < count($lookupValues); $i++)
		{
			if (in_array($lookupValue, $lookupValues)) {
				$lookupValue += 1;
			}
		}
		
		$lookup					= new AppLookup();
		$lookup->lookup_id		= $lookupId;
		$lookup->lookup_value	= $lookupValue;
		$lookup->dict_id		= $dictId;
		$lookup->save();
		
		if ($lookupId === '')
		{
			$message = Dict::getValue('error_field_required', ['attribute' => 'lookup_id']);
			$saved = 0;
		}
				
		$changesArray = [
							'addedLookupId' => $lookupId,
							'addedLookupValue' => $lookupValue,
							'addedDictId'	=> $dictId,
						];
		
		foreach ($this->usedLangs as $lang)
		{
			${'dictValue'.$lang} = $data["dict_value_{$lang}"];
			$changesArray["addedDict{$lang}"] = ${'dictValue'.$lang};
			$this->addDict($lang, $dictId, ${'dictValue'.$lang});
			
			if (${'dictValue'.$lang} === '')
			{
				$message = Dict::getValue('error_field_required', ['attribute' => ${'dictValue'.$lang}]);
				$saved = 0;
			}			
		} 
				
		Log::create($this, "APP_LOOKUP_SAVE", ["data" => $changesArray]);

		
		if ($saved) {
			$status = [
				'status'	=> 1,
			];
		} else {
			$status = [
				'status'	=> 0,
				'error'	=> $message,
			];
		}
		
		echo json_encode($status);
	}

	public function actionDelete($modelName = 'AppLookup', $hasRight = false) {
		$this->layout = "//layouts/ajax";
		if(isYii2()){
			$this->layout = '@app/views/layouts/ajax';
			$modelName = 'app\\models\\'.$modelName;
		}
	
		$this->G2BInit();

		$gridID = requestParam('gridID');
		$gridID = empty($gridID) ? "dhtmlxGrid" : $gridID;
		$hasRight = $hasRight||$this->hasRight("delete", $gridID);
		$ids = requestParam('ids');			
		$idsArray = explode(";", $ids);
		$idsToDelete = [];
		$mainGridID = requestParam('mainGridID');
		$generateFrom = requestParam('generateFrom');
		$idsToDelete[$this->LAGridDB->getModelName($gridID)] = $idsArray;
		$deleteSuccess = true;			
		$message = null;
		
		if ($hasRight && isset($idsToDelete[$modelName]) && count($idsToDelete[$modelName])) {
			if (!empty($modelName)) {
				foreach ($idsToDelete[$modelName] as $id) {					
					if(isYii1()){
						$model = $modelName::model()->findByPk($id);
					}else if (isYii2()){
						$model = $modelName::findOne(['row_id' => $id]);
					}
					
					if ($this->hasRight("delete")) {
						Yang::modelUpdateByPk($modelName,$id,["valid"=>0]);
						
						$changesArray = [
							'deletedRowId' => $id,
						];
						Log::create($this, "APP_LOOKUP_DELETE", ["data" => $changesArray]);
					}
					else {
						$deleteSuccess = false;			
						$message = Dict::getValue("sync_check_deleted_rows_failed");
					}
				}
			} else {
				$deleteSuccess = $deleteSuccess && false;
			}
		} else {
			$deleteSuccess = $deleteSuccess && false;
		}
		
		if ($deleteSuccess) {
			$status = array(
				'status'	=> 1,
				'pkDeleted' => $ids,
			);
		} else {
			$status = array(
				'status'	=> 0,
				'message'	=> $message,
			);
		}

		echo json_encode($status);
	}
	
	public function actionGridEdit()
	{	
		$ids           = requestParam('ids');
		if(isYii1()){
			$lookup			= AppLookup::model()->findByPk($ids);
		}else if (isYii2()){
			$lookup	= AppLookup::findOne(['row_id' => $ids]);
		}

		if ($lookup->order != requestParam($ids . '_c1')) {
			$lookup->order = requestParam($ids . '_c1');
			$lookup->save();
		}

		$dictId			= $lookup->dict_id;
		$i = 2;
		foreach ($this->usedLangs as $lang)
		{
            $inputTextToSave = requestParam($ids . '_c' . $i);
			${'newDictValue'.$lang} = preg_match('/&[a-zA-Z0-9#]+;/', $inputTextToSave) ? html_entity_decode($inputTextToSave) : $inputTextToSave;
			$i++;
		}

		$editorStatus  = requestParam($ids.'_'."!nativeeditor_status");
		$action			= 'update';
		
		if(isYii1()){
			$dict = Dictionary::model()->findAllByAttributes(['dict_id' => $dictId]);
		}else if (isYii2()){
			$dict = Dictionary::findAll(['dict_id' => $dictId]);
		}

		foreach ($dict as $d)
		{
			if (method_exists($d, "getOldAttributes"))
			{
				$oldData = $d->getOldAttributes();
				$changesArray = [];
				$ignoreCols = ["valid", "module", "row_id", "dict_id", "lang"];

				foreach ($oldData as $key => $oldDataRow)
				{
					if (!in_array($key, $ignoreCols) && $oldDataRow != ${'newDictValue'.$oldData["lang"]})
					{
						$changesArray[] = 
						[
							"dict_id"				=> $dictId,
							"prevValue"				=> $oldData[$key],
							"newValue"				=> ${'newDictValue'.$oldData["lang"]},
							"lang"					=> $oldData["lang"]
						];

					}

					if(isYii1()){
						$newD = Dictionary::model()->findByAttributes(['dict_id' => $dictId, 'lang' => $oldData["lang"]]);
					}else if (isYii2()){
						$newD = Dictionary::findOne(['dict_id' => $dictId, 'lang' => $oldData["lang"]]);
					}

					$newD->dict_value = ${'newDictValue'.$oldData["lang"]};
					$newD->save();
					}
					if (count($changesArray) > 0) {
						Log::create($this, "APP_LOOKUP_DATA_CHANGE", ["data" => $changesArray]);
					}
				}
			}
			

		if (stripos($_SERVER["HTTP_ACCEPT"], "application/xhtml+xml") !== false) {
			header("Content-type: application/xhtml+xml");
		} else {
			header("Content-type: text/xml");
		}
		echo('<?xml version="1.0" encoding="utf-8" ?>'."\n");
	    ?>
		<data>
            <action type='//<?=$action?>'
                    sid='<?=$ids?>'
                    tid='<?=$editorStatus?>'/>
		</data>
		<?php
        exit();
	}
	
	private function addDict($lang, $dictId, $dictValue)
	{
		$dict				= new Dictionary();
		$dict->lang			= $lang;
		$dict->dict_id		= $dictId;
		$dict->dict_value	= $dictValue;
		$dict->save();
	}
}
?>