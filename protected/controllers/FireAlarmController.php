<?php

'yii2-only`;

	namespace app\controllers;
	use app\models\AlarmLog;
	use Yang;

`/yii2-only';


#yii2: done

class FireAlarmController extends Controller
{
	public function actionStatus() {
//		$al = new AlarmLog;
//		$crit = new CDbCriteria();
//		$crit->condition = "";
//		$crit->order = "`row_id` DESC";
//		$res = $al->find($crit);

		$axdoorAlarmSourceTerminalID = "TTWA";

		$response = [];
		$response["alarmInitUrl"] = baseURL() . "/axDoor/axDoor/keepAlive?id=$axdoorAlarmSourceTerminalID&gateStatus=G";
		$response["alarmStopUrl"] = baseURL() . "/axDoor/axDoor/keepAlive?id=$axdoorAlarmSourceTerminalID&gateStatus=H";

//		if ($res) {
//			if ((int)$res->alarm_level) {
//
//			}
//		} else {
//
//		}

		echo json_encode($response);
	}
}