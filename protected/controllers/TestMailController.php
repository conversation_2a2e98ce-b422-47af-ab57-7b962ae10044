<?php

class TestMailController extends Controller
{
	public function actionIndex()
	{
		$es = new EmailSender();
		$message = 'Ha ezt eltudod olvasni akkor sikeres volt az email küldési teszt!';

		$es->sendMail(
			/* $addresses */
			[
				'addr' => [
					[
						'email'    => requestParam('email'),
					],
				],
			],
			/* $subject */
			'Test',
			/* $message */
			$message,
			/* $view */
			[],//[$emailAttrs['emailLayout']['folder'], $emailAttrs['emailLayout']['view']],
			/* $vars */
			'',//$emailAttrs['emailParams'],
			/* $images */
			[],//['logo' => Yang::getBasePath().'/../webroot/images/module_logos/logo_email.png'],
			/* $iCal */
			false,
			/* $iCalString */
			"",
			/* $notSkipAppSettings */
			\TRUE
		);
	}

	public function filters()
	{
		return [
			'accessControl', // perform access control for CRUD operations
		];
	}

	public function accessRules()
	{
		return [
			['allow', // allow authenticated users to access all actions
				'users' => ['@'],
			],
			['deny',  // deny all users
				'users' => ['*'],
			],
		];
	}
}
