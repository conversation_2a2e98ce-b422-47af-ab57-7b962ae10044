<?php
$module_name = 'ttwa_software_name-ttwa-wfm';

$module = ['ttwa-ahp-core', 'ttwa-ahp', 'ttwa-wfm', 'ttwa-wwm', 'ttwa-csm-core', 'ttwa-csm'];

$host = 'sqlserver.login.hu';
$user = 'dbauser';
$pass = 'V1aifSphFeSr';
$db = 'mbh_ease';

$customerDbPatchName = "magna";

$nodeReportDir = 'c:/www/ttwa/webroot/reports/';

$testDesign = false;

$isHttpsLinks = false;

$reportHost = "http://localhost:8080/ttwa-jasper/";
$nodeHost = "http://localhost:8000/";

$reportDbName = 'ttwa_jasper';
$reportDbUser = 'root';
$reportDbPass = 'root';


$email_settings =
	[
		'host'				=> 'smtp.gmail.com',
		'port'				=> '465',
		'is_smtp'			=> true,
		'smtp_auth'			=> true,
		'smtp_secure'		=> 'ssl',
		'username'			=> '<EMAIL>',
		'password'			=> 'pvirgbjwykxizgdg',
		'is_html'			=> true,
		'sender'			=> '<EMAIL>',
		'from_email'		=> '<EMAIL>',
		'from_name'			=> 'Login Autonom Teszt',
		'reply_to'			=> '<EMAIL>',
		'confirm_reading'	=> false,
		'charset'			=> 'UTF-8',
	];

$greenDesign = true;