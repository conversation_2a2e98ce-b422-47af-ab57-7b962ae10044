<?php

declare(strict_types=1);

namespace Components\BaberExport\ETL\Transformer;

use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;

final class HashAssocRowEntryTransformer implements Transformer
{
    public function __construct(
        private readonly string $hashEntryName,
        private readonly string $entryName
    )
    {
    }

    public function __serialize(): array
    {
        throw new \Exception('Not implemented!');
    }

    public function __unserialize(array $data): void
    {
        throw new \Exception('Not implemented!');
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $transformer = function (Row $row): Row {
            if (!$row->entries()->has($this->entryName)) {
                throw new \RuntimeException("\"{$this->entryName}\" not found");
            }

            $arrayEntry = $row->get($this->entryName);
            $data = $arrayEntry->value();
            $hashId = md5(json_encode($data));

            return $row->set(new Row\Entry\StringEntry(
                $this->hashEntryName,
                $hashId
            ));
        };

        return $rows->map($transformer);
    }
}