<?php

declare(strict_types=1);

namespace Components\BaberExport\ETL\Transformer;

use Components\BaberExport\Enum\BaberExportEnum;
use Components\BaberExport\Enum\BaberSourceFieldsEnum;
use Components\BaberExport\Enum\BaberTargetFieldsTorzsEnum;
use Components\BaberExport\Enum\DefaultFieldEnum;
use Components\Employee\Enum\EmployeeMainDataEnum;
use Components\ETL\RowFactory\ArrayRowFactory;
use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;

final class DefaultTargetFieldValuesTransformer implements Transformer
{
    public function __construct(
        private readonly ArrayRowFactory $rowFactory = new ArrayRowFactory()
    ) {
    }

    public function __serialize(): array
    {
        throw new \Exception('Not implemented!');
    }

    public function __unserialize(array $data): void
    {
        throw new \Exception('Not implemented!');
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $transformer = function (Row $row): Row {
            $targetFields = [
                BaberTargetFieldsTorzsEnum::ADOAZONOSITOJEL => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE)[EmployeeMainDataEnum::TAX_NUMBER] ?? NULL),
                BaberTargetFieldsTorzsEnum::ELONEV => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE)[EmployeeMainDataEnum::TITLE] ?? NULL),
                BaberTargetFieldsTorzsEnum::VEZETEKNEV => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE)[EmployeeMainDataEnum::LAST_NAME] ?? NULL),
                BaberTargetFieldsTorzsEnum::KERESZTNEV => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE)[EmployeeMainDataEnum::FIRST_NAME] ?? NULL),
                BaberTargetFieldsTorzsEnum::LEANYNEV => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE)[EmployeeMainDataEnum::NAME_OF_BIRTH] ?? NULL),
                BaberTargetFieldsTorzsEnum::ANYJANEVE => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_EXT)[BaberSourceFieldsEnum::EMPLOYEE_EXT_MOTHERS_NAME] ?? NULL),
                BaberTargetFieldsTorzsEnum::NEME => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE)[EmployeeMainDataEnum::GENDER] ?? NULL),
                BaberTargetFieldsTorzsEnum::ALLAMPOLG => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_EXT)[BaberSourceFieldsEnum::EMPLOYEE_EXT_OPTION4] ?? NULL),
                BaberTargetFieldsTorzsEnum::SZULETESIORSZAG_ALLAMPOLG => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::SZULHELY => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_EXT)[BaberSourceFieldsEnum::EMPLOYEE_EXT_PLACE_OF_BIRTH] ?? NULL),
                BaberTargetFieldsTorzsEnum::SZULDATUM => $this->convertDateFormat($this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_EXT)[BaberSourceFieldsEnum::EMPLOYEE_EXT_DATE_OF_BIRTH] ?? NULL)),
                BaberTargetFieldsTorzsEnum::TAJSZAM => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_EXT)[BaberSourceFieldsEnum::EMPLOYEE_EXT_SSN] ?? NULL),
                BaberTargetFieldsTorzsEnum::SZEMELYISZ => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::SZEMELYIGSZ => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_EXT)[BaberSourceFieldsEnum::EMPLOYEE_EXT_PERSONAL_ID_CARD_NUMBER] ?? NULL),
                BaberTargetFieldsTorzsEnum::NYUGDIJASSTATUSZKOD => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_EXT)[BaberSourceFieldsEnum::EMPLOYEE_EXT_OPTION9] ?? NULL),
                BaberTargetFieldsTorzsEnum::NYUGDIJASSTATUSZDATUM => $this->convertDateFormat($this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_EXT)[BaberSourceFieldsEnum::EMPLOYEE_EXT_OPTION10] ?? NULL), true),
                BaberTargetFieldsTorzsEnum::NYTORZSSZAM => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::ADOSZAM => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::LAKHELYTAVOLSAG => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::LAKHELYTAVOLSAG2 => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::UTIKOLTSEG1 => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::UTIKOLTSEG2 => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::IRANYITOSZAM => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_ADDRESS)[BaberSourceFieldsEnum::EMPLOYEE_ADDRESS_ZIP_CODE] ?? NULL),
                BaberTargetFieldsTorzsEnum::HELYSEG => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_ADDRESS)[BaberSourceFieldsEnum::EMPLOYEE_ADDRESS_CITY] ?? NULL),
                BaberTargetFieldsTorzsEnum::UTCA => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_ADDRESS)[BaberSourceFieldsEnum::EMPLOYEE_ADDRESS_PUBLIC_PLACE_NAME] ?? NULL),
                BaberTargetFieldsTorzsEnum::UTCAJELLEGE => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_ADDRESS)[BaberSourceFieldsEnum::EMPLOYEE_ADDRESS_PLACE_TYPE] ?? NULL),
                BaberTargetFieldsTorzsEnum::HAZSZAM => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_ADDRESS)[BaberSourceFieldsEnum::EMPLOYEE_ADDRESS_HOUSE_NUMBER] ?? NULL),
                BaberTargetFieldsTorzsEnum::EPULET => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::LEPCSOHAZ => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::EMELET => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_ADDRESS)[BaberSourceFieldsEnum::EMPLOYEE_ADDRESS_FLOOR] ?? NULL),
                BaberTargetFieldsTorzsEnum::AJTO => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_ADDRESS)[BaberSourceFieldsEnum::EMPLOYEE_ADDRESS_DOOR] ?? NULL),
                BaberTargetFieldsTorzsEnum::LEVIRSZ => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_ADDRESS)[BaberSourceFieldsEnum::EMPLOYEE_ADDRESS_RES_ZIP_CODE] ?? NULL),
                BaberTargetFieldsTorzsEnum::LEVHELYSEG => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_ADDRESS)[BaberSourceFieldsEnum::EMPLOYEE_ADDRESS_RES_CITY] ?? NULL),
                BaberTargetFieldsTorzsEnum::LEVUTCA => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_ADDRESS)[BaberSourceFieldsEnum::EMPLOYEE_ADDRESS_RES_PUBLIC_PLACE_NAME] ?? NULL),
                BaberTargetFieldsTorzsEnum::LEVUTCAJELLEGE => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_ADDRESS)[BaberSourceFieldsEnum::EMPLOYEE_ADDRESS_RES_PUBLIC_PLACE_TYPE] ?? NULL),
                BaberTargetFieldsTorzsEnum::LEVHAZSZAM => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_ADDRESS)[BaberSourceFieldsEnum::EMPLOYEE_ADDRESS_RES_HOUSE_NUMBER] ?? NULL),
                BaberTargetFieldsTorzsEnum::LEVEPULET => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::LEVLEPCSOHAZ => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::LEVEMELET => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_ADDRESS)[BaberSourceFieldsEnum::EMPLOYEE_ADDRESS_RES_FLOOR] ?? NULL),
                BaberTargetFieldsTorzsEnum::LEVAJTO => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_ADDRESS)[BaberSourceFieldsEnum::EMPLOYEE_ADDRESS_RES_DOOR] ?? NULL),
                BaberTargetFieldsTorzsEnum::TELEFON => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_EXT6)[BaberSourceFieldsEnum::EMPLOYEE_EXT6_OPTION1] ?? NULL),
                BaberTargetFieldsTorzsEnum::EMAILCIM => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_EXT6)[BaberSourceFieldsEnum::EMPLOYEE_EXT6_OPTION2] ?? NULL),
                BaberTargetFieldsTorzsEnum::JOGVISZONYAZONOSITO => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::BELEPES => $this->convertDateFormat($this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_CONTRACT)[EmployeeMainDataEnum::EC_VALID_FROM] ?? NULL), true),
                BaberTargetFieldsTorzsEnum::KILEPES => $this->convertDateFormat($this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_CONTRACT)[EmployeeMainDataEnum::EC_VALID_TO] ?? NULL), true),
                BaberTargetFieldsTorzsEnum::KILEPES_MODJA => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_CONTRACT)[EmployeeMainDataEnum::EC_END_TYPE] ?? NULL),
                BaberTargetFieldsTorzsEnum::PROBAIDOK => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::PROBAIDOV => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::JOGVISZONYOKKOD => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_CONTRACT)[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE] ?? NULL),
                BaberTargetFieldsTorzsEnum::JOGVISZONYKOD => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_CONTRACT)[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE] ?? NULL),
                BaberTargetFieldsTorzsEnum::JOGVISZONYDATUM => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::MUNKASZERZODESTIPUSA => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_EXT3)[BaberSourceFieldsEnum::EMPLOYEE_EXT3_OPTION2] ?? NULL),
                BaberTargetFieldsTorzsEnum::MUNKASZERZODESTOLDATUM => $this->convertDateFormat($this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_CONTRACT)[EmployeeMainDataEnum::EC_VALID_FROM] ?? NULL), true),
                BaberTargetFieldsTorzsEnum::MUNKASZERZODESIGDATUM => $this->convertDateFormat($this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_CONTRACT)[EmployeeMainDataEnum::EC_VALID_TO] ?? NULL), true),
                BaberTargetFieldsTorzsEnum::MUNKAKORKOD => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_EXT2)[BaberSourceFieldsEnum::EMPLOYEE_EXT2_OPTION1] ?? NULL),
                BaberTargetFieldsTorzsEnum::MUNKAKORMEGNEVEZES => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_EXT2)[BaberSourceFieldsEnum::EMPLOYEE_EXT2_OPTION2] ?? NULL),
                BaberTargetFieldsTorzsEnum::MUNKAKORDATUM => $this->convertDateFormat($this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_GROUP)[BaberSourceFieldsEnum::EMPLOYEE_GROUP_VALID_FROM] ?? NULL), true),
                BaberTargetFieldsTorzsEnum::FEORKOD => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_EXT2)[BaberSourceFieldsEnum::EMPLOYEE_EXT2_OPTION6] ?? NULL),
                BaberTargetFieldsTorzsEnum::FEORDATUM => $this->convertDateFormat($this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_GROUP)[BaberSourceFieldsEnum::EMPLOYEE_GROUP_VALID_FROM] ?? NULL), true),
                BaberTargetFieldsTorzsEnum::BEOSZTASKOD => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::BEOSZTASDATUM => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::PROFITCENTRUMKOD => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_COST)[BaberSourceFieldsEnum::EMPLOYEE_COST_COST_CENTER_ID] ?? NULL),
                BaberTargetFieldsTorzsEnum::PROFITCENTRUMDATUM => $this->convertDateFormat($this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_COST)[BaberSourceFieldsEnum::EMPLOYEE_COST_VALID_FROM] ?? NULL), true),
                BaberTargetFieldsTorzsEnum::ABKOD => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::ABDATUM => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::SZERVEZETKOD => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_COST)[BaberSourceFieldsEnum::EMPLOYEE_COST_COST_ID] ?? NULL),
                BaberTargetFieldsTorzsEnum::SZERVEZETMEGNEVEZES => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_COST)[BaberSourceFieldsEnum::EMPLOYEE_COST_COST_ID] ?? NULL),
                BaberTargetFieldsTorzsEnum::SZERVEZETDATUM => $this->convertDateFormat($this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_COST)[BaberSourceFieldsEnum::EMPLOYEE_COST_VALID_FROM] ?? NULL), true),
                BaberTargetFieldsTorzsEnum::FOKSZAMOKKOD => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_COST)[BaberSourceFieldsEnum::EMPLOYEE_COST_COST_ID] ?? NULL),
                BaberTargetFieldsTorzsEnum::FOKSZAMOKMEGNEVEZES => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_COST)[BaberSourceFieldsEnum::EMPLOYEE_COST_COST_ID] ?? NULL),
                BaberTargetFieldsTorzsEnum::FOKSZAMOKDATUM => $this->convertDateFormat($this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_COST)[BaberSourceFieldsEnum::EMPLOYEE_COST_VALID_FROM] ?? NULL), true),
                BaberTargetFieldsTorzsEnum::BERTIPKOD => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_CONTRACT)[EmployeeMainDataEnum::WAGE_TYPE] ?? NULL),
                BaberTargetFieldsTorzsEnum::ALAPBER => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::ARFOLYAMOKKOD => DefaultFieldEnum::ARFOLYAMOKKOD,
                BaberTargetFieldsTorzsEnum::ALAPBERVALTOZASDATUM => $this->convertDateFormat($this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_SALARY)[BaberSourceFieldsEnum::EMPLOYEE_SALARY_VALID_FROM] ?? NULL), true),
                BaberTargetFieldsTorzsEnum::UJSZAMLASZAM => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_SALARY)[BaberSourceFieldsEnum::EMPLOYEE_SALARY_ES_OPTION2] ?? NULL),
                BaberTargetFieldsTorzsEnum::BANKKOD => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::PENZTARKOD => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_COST)[BaberSourceFieldsEnum::EMPLOYEE_COST_COST_CENTER_ID] ?? NULL),
                BaberTargetFieldsTorzsEnum::PENZTARDATUM => $this->convertDateFormat($this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_COST)[BaberSourceFieldsEnum::EMPLOYEE_COST_VALID_FROM] ?? NULL), true),
                BaberTargetFieldsTorzsEnum::MUNKIRANYITOSZAM => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::MUNKHELYSEG => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_EXT3)[BaberSourceFieldsEnum::EMPLOYEE_EXT3_OPTION6] ?? NULL),
                BaberTargetFieldsTorzsEnum::MUNKUTCA => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::MUNKUTCAJELLEGE => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::MUNKHAZSZAM => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::MUNKEPULET => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::MUNKLEPCSOHAZ => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::MUNKEMELET => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::MUNKAJTO => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::HETIMUNKAORA => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_CONTRACT)[EmployeeMainDataEnum::DAILY_WORKTIME] ?? NULL),
                BaberTargetFieldsTorzsEnum::HETIMUNKAORAVALTOZASDATUM => $this->convertDateFormat($this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_CONTRACT)[EmployeeMainDataEnum::EC_VALID_FROM] ?? NULL), true),
                BaberTargetFieldsTorzsEnum::MUSZAKOKKOD => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_CONTRACT)[EmployeeMainDataEnum::DAILY_WORKTIME] ?? NULL),
                BaberTargetFieldsTorzsEnum::MUSZAKOKDATUM => $this->convertDateFormat($this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_CONTRACT)[EmployeeMainDataEnum::EC_VALID_FROM] ?? NULL), true),
                BaberTargetFieldsTorzsEnum::OKKEZDET_EV => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::OKKEZDET => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::OKHONAP => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::OKTORAKERET => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::NYELVAZONOSITO => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::KULSOAZONOSITOKOD => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::AZONOSITO => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::TORZSSZAM => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::UTOLSOMUNKANAP => $this->convertDateFormat($this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_CONTRACT)[EmployeeMainDataEnum::EC_VALID_TO] ?? NULL), true),
                BaberTargetFieldsTorzsEnum::VEGZETTSEG => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::MEGSZERZESDATUMA => DefaultFieldEnum::VALUE_NULL,
                BaberTargetFieldsTorzsEnum::TAGOZAT => DefaultFieldEnum::VALUE_NULL
            ];

            return $row->set(
                new Row\Entry\ArrayEntry(
                    BaberExportEnum::BABER_TARGET_TORZS_FIELDS,
                    $targetFields
                )
            );
        };

        return $rows->map($transformer);
    }

    private function getRowValueOrNull(mixed $value): string
    {
        return !empty($value) && !is_null($value) ? (string) $value : 'NULL';
    }

    private function convertDateFormat(string $date, bool $checkLowerLimit = false): string
    {
        if (preg_match("/^[0-9]{4}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/", $date)) {
            if ($checkLowerLimit && strtotime($date) < strtotime(DefaultFieldEnum::LEAST_DATE)) {
                $date = DefaultFieldEnum::LEAST_DATE;
            }

            $date = str_replace('-', '.', $date);
            return $date;
        }

        return $date;
    }

}
