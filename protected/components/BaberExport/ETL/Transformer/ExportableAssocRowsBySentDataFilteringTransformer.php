<?php

declare(strict_types=1);

namespace Components\BaberExport\ETL\Transformer;

use Components\BaberExport\Enum\BaberExportEnum;
use Components\ETL\Enum\EtlEnum;
use Components\ETL\RowFactory\ArrayRowFactory;
use Flow\ETL\Exception\InvalidArgumentException;
use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;

final readonly class ExportableAssocRowsBySentDataFilteringTransformer implements Transformer
{
    public function __construct(
        private string $entryName,
    )
    {
    }

    public function __serialize(): array
    {
        throw new \Exception('Not implemented!');
    }

    public function __unserialize(array $data): void
    {
        throw new \Exception('Not implemented!');
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $etlRowHash = iterator_to_array($context->cache()->read('etlRowHashes'));
        $etlRowHashArray = $this->rowsToArray($etlRowHash);
        $etlRowHashes = array_column($etlRowHashArray, EtlEnum::ETL_ROW_HASH_TABLE_HASH);
        $hashes = [];

        /**
         * @throws InvalidArgumentException
         */
        $transformer = function (Row $row) use ($etlRowHashes): Row {
            if (!$row->entries()->has($this->entryName)) {
                throw new \RuntimeException("\"{$this->entryName}\" not found");
            }

            if (!$row->entries()->get($this->entryName) instanceof Row\Entry\ArrayEntry) {
                throw new \RuntimeException("\"{$this->entryName}\" is not ArrayEntry");
            }

            $arrayEntry = $row->get($this->entryName);
            $data = $arrayEntry->value();
            $hashId = md5(json_encode($data));

            if (in_array($hashId, $etlRowHashes)) {
                return $row->set(new Row\Entry\BooleanEntry(
                    BaberExportEnum::ENTRY_TORZS_EXPORTABLE,
                    FALSE
                ));
            }

            return $row;
        };

        return $rows->map($transformer);
    }

    private function rowsToArray($rows): array
    {
        $result = [];
        foreach ($rows as $row) {
            $result = $row->toArray();
        }
        return $result[0];
    }
}