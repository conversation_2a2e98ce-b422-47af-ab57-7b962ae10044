<?php

declare(strict_types=1);

namespace Components\BaberExport\ETL\Transformer;

use Components\BaberExport\Descriptor\BaberRawDataDescriptor;
use Components\BaberExport\Enum\BaberExportEnum;
use Components\BaberExport\Enum\BaberSourceFieldsEnum;
use Components\BaberExport\Enum\BaberTargetFieldsCsEltartottEnum;
use Components\BaberExport\Enum\BaberTargetFieldsTorzsEnum;
use Components\BaberExport\Enum\DefaultFieldEnum;
use Components\Core\Descriptor\ValidityRowDescriptor;
use Components\Core\Transformer\ValidityRowDescriptorSortingToArrayTransformer;
use Components\Employee\Enum\EmployeeMainDataEnum;
use Components\ETL\Provider\EtlWorkflowConfigProvider;
use Components\ETL\RowFactory\ArrayRowFactory;
use Flow\ETL\Exception\InvalidArgumentException;
use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;

final class FillTargetFieldValuesByRulesTransformer implements Transformer
{
    public function __construct(
        private readonly BaberRawDataDescriptor    $baberRawDataDescriptor,
        private readonly ArrayRowFactory           $rowFactory = new ArrayRowFactory(),
        private readonly EtlWorkflowConfigProvider $etlWorkflowConfigProvider = new EtlWorkflowConfigProvider()
    )
    {
    }

    public function __serialize(): array
    {
        throw new \Exception('Not implemented!');
    }

    public function __unserialize(array $data): void
    {
        throw new \Exception('Not implemented!');
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $config = json_decode($this->baberRawDataDescriptor->getEtlConfig()->config, true);
        /**
         * @throws InvalidArgumentException
         */
        $transformer = function (Row $row) use ($config, $context): Row {
            [$targetTorzsFields, $targetEltartottFields] = $this->fillTargetFields($row, $config, $context);
            return $row->set(
                new Row\Entry\ArrayEntry(
                    BaberExportEnum::BABER_TARGET_TORZS_FIELDS,
                    $targetTorzsFields
                ),
                new Row\Entry\ArrayEntry(
                    BaberExportEnum::BABER_TARGET_ELTARTOTT_FIELDS,
                    $targetEltartottFields
                )
            );
        };

        return $rows->map($transformer);
    }

    /**
     * @throws InvalidArgumentException
     */
    private function fillTargetFields(Row $row, array $config, FlowContext $context): array
    {
        $costs = iterator_to_array($context->cache()->read('costs'));
        $costsArray = $this->rowsToArray($costs);
        $costCenters = iterator_to_array($context->cache()->read('costCenters'));
        $costCentersArray = $this->rowsToArray($costCenters);
        $appLookupDicts = iterator_to_array($context->cache()->read('appLookupsDict'));
        $appLookupDictsArray = $this->rowsToArray($appLookupDicts);
        $employeeRow = $row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE);
        $employeeContractRow = $row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_CONTRACT);
        $employeeContractByHistory = $row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_CONTRACT_HISTORY);
        $primaryEmployeeContractRow = $row->valueOf(BaberExportEnum::ENTRY_PRIMARY_EMPLOYEE_CONTRACT);
        $employeeAbsencesByHistory = $row->valueOf(BaberExportEnum::ENTRY_LAST_EMPLOYEE_ABSENCE_HISTORY);
        $employeeSalaryRow = $row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_SALARY);
        $employeeExtRow = $row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_EXT);
        $employeeExt3Row = $row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_EXT3);
        $employeeExt5Row = $row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE_EXT5);
        $defaultEnd = $this->convertDateFormat(\App::getSetting('defaultEnd'));
        $torzsFields = $row->valueOf(BaberExportEnum::BABER_TARGET_TORZS_FIELDS);

        $firstValidFromHistory = $this->convertDateFormat($this->getRowValueOrNull($primaryEmployeeContractRow[EmployeeMainDataEnum::VALID_FROM] ?? NULL), true);
        if ($this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_BELEPES_EXCEPTION)) {
            $torzsFields[BaberTargetFieldsTorzsEnum::BELEPES] = DefaultFieldEnum::VALUE_NULL;
        } elseif ($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE] == '31') {
            $torzsFields[BaberTargetFieldsTorzsEnum::BELEPES] = $firstValidFromHistory;
        }

        $torzsFields[BaberTargetFieldsTorzsEnum::ALLAMPOLG] = $torzsFields[BaberTargetFieldsTorzsEnum::ALLAMPOLG] == DefaultFieldEnum::VALUE_NULL ? DefaultFieldEnum::SZULETESIORSZAG_ALLAMPOLG : $torzsFields[BaberTargetFieldsTorzsEnum::ALLAMPOLG];
        $torzsFields[BaberTargetFieldsTorzsEnum::SZULETESIORSZAG_ALLAMPOLG] = DefaultFieldEnum::SZULETESIORSZAG_ALLAMPOLG;
        $torzsFields[BaberTargetFieldsTorzsEnum::NEME] = $config[BaberTargetFieldsTorzsEnum::NEME][$torzsFields[BaberTargetFieldsTorzsEnum::NEME]] ?? $torzsFields[BaberTargetFieldsTorzsEnum::NEME];
        $torzsFields[BaberTargetFieldsTorzsEnum::NYUGDIJASSTATUSZKOD] = $config[BaberTargetFieldsTorzsEnum::NYUGDIJASSTATUSZKOD][$torzsFields[BaberTargetFieldsTorzsEnum::NYUGDIJASSTATUSZKOD]] ?? $torzsFields[BaberTargetFieldsTorzsEnum::NYUGDIJASSTATUSZKOD];
        $torzsFields[BaberTargetFieldsTorzsEnum::NYUGDIJASSTATUSZKOD] = $torzsFields[BaberTargetFieldsTorzsEnum::NYUGDIJASSTATUSZKOD] == 'NULL' ? DefaultFieldEnum::VALUE_ZERO : $torzsFields[BaberTargetFieldsTorzsEnum::NYUGDIJASSTATUSZKOD];

        if ($this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_NYUGDIJAS_STATUSZ_DATUM_EXCEPTION)) {
            $torzsFields[BaberTargetFieldsTorzsEnum::NYUGDIJASSTATUSZDATUM] = DefaultFieldEnum::VALUE_NULL;
        } elseif ($torzsFields[BaberTargetFieldsTorzsEnum::NYUGDIJASSTATUSZKOD] == DefaultFieldEnum::VALUE_ZERO) {
            $torzsFields[BaberTargetFieldsTorzsEnum::NYUGDIJASSTATUSZDATUM] = $torzsFields[BaberTargetFieldsTorzsEnum::BELEPES];
        }

        $torzsFields[BaberTargetFieldsTorzsEnum::EMAILCIM] = $torzsFields[BaberTargetFieldsTorzsEnum::EMAILCIM] != DefaultFieldEnum::VALUE_NULL ? $torzsFields[BaberTargetFieldsTorzsEnum::EMAILCIM] : DefaultFieldEnum::EMAIL_CIM;

        $torzsFields[BaberTargetFieldsTorzsEnum::JOGVISZONYAZONOSITO] = match ((string)$employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE]) {
            '31' => '5' . $torzsFields[BaberTargetFieldsTorzsEnum::ADOAZONOSITOJEL],
            '32' => '15' . $torzsFields[BaberTargetFieldsTorzsEnum::ADOAZONOSITOJEL],
            '33' => '16' . $torzsFields[BaberTargetFieldsTorzsEnum::ADOAZONOSITOJEL],
            default => $torzsFields[BaberTargetFieldsTorzsEnum::ADOAZONOSITOJEL] . $employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_NUMBER],
        };

        if ($this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_KILEPES_EXCEPTION)
            || empty($employeeContractRow[EmployeeMainDataEnum::EC_END_TYPE])) {
            $torzsFields[BaberTargetFieldsTorzsEnum::KILEPES] = DefaultFieldEnum::VALUE_NULL;
        } elseif ($torzsFields[BaberTargetFieldsTorzsEnum::KILEPES] == $defaultEnd) {
            $torzsFields[BaberTargetFieldsTorzsEnum::KILEPES] = DefaultFieldEnum::VALUE_NULL;
        }

        if ($this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_KILEPES_MODJA_EXCEPTION)) {
            $torzsFields[BaberTargetFieldsTorzsEnum::KILEPES_MODJA] = DefaultFieldEnum::VALUE_NULL;
        } elseif (isset($config[BaberTargetFieldsTorzsEnum::KILEPES_MODJA][$torzsFields[BaberTargetFieldsTorzsEnum::KILEPES_MODJA]])) {
            $torzsFields[BaberTargetFieldsTorzsEnum::KILEPES_MODJA] = $config[BaberTargetFieldsTorzsEnum::KILEPES_MODJA][$torzsFields[BaberTargetFieldsTorzsEnum::KILEPES_MODJA]];
        }

        $torzsFields[BaberTargetFieldsTorzsEnum::JOGVISZONYOKKOD] = $config[BaberTargetFieldsTorzsEnum::JOGVISZONYOKKOD][$torzsFields[BaberTargetFieldsTorzsEnum::JOGVISZONYOKKOD]] ?? $torzsFields[BaberTargetFieldsTorzsEnum::JOGVISZONYOKKOD];
        $torzsFields[BaberTargetFieldsTorzsEnum::JOGVISZONYKOD] = $config[BaberTargetFieldsTorzsEnum::JOGVISZONYKOD][$torzsFields[BaberTargetFieldsTorzsEnum::JOGVISZONYKOD]] ?? $torzsFields[BaberTargetFieldsTorzsEnum::JOGVISZONYKOD];
        $torzsFields[BaberTargetFieldsTorzsEnum::JOGVISZONYDATUM] = $this->convertDateFormat(
            $this->getRowValueOrNull(
                $this->getJogviszonyDatumByChanges($employeeContractByHistory)
            ),
            true
        );

        $torzsFields[BaberTargetFieldsTorzsEnum::MUNKASZERZODESTIPUSA] = (
        $this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_MUNKASZERZODES_TIPUSA_EXCEPTION)
        )
            ? DefaultFieldEnum::VALUE_NULL
            : ($config[BaberTargetFieldsTorzsEnum::MUNKASZERZODESTIPUSA][$torzsFields[BaberTargetFieldsTorzsEnum::MUNKASZERZODESTIPUSA]] ?? DefaultFieldEnum::VALUE_NULL);

        $torzsFields[BaberTargetFieldsTorzsEnum::MUNKASZERZODESTOLDATUM] =
            ($this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_MUNKASZERZODES_TOL_DATUM_EXCEPTION))
                ? DefaultFieldEnum::VALUE_NULL
                : $torzsFields[BaberTargetFieldsTorzsEnum::MUNKASZERZODESTOLDATUM];

        if (
            $this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_MUNKASZERZODES_IG_DATUM_EXCEPTION)
            || (
                isset($employeeExt3Row[BaberSourceFieldsEnum::EMPLOYEE_EXT3_OPTION2])
                && $employeeExt3Row[BaberSourceFieldsEnum::EMPLOYEE_EXT3_OPTION2] == 2)
        ) {
            $torzsFields[BaberTargetFieldsTorzsEnum::MUNKASZERZODESIGDATUM] = DefaultFieldEnum::VALUE_NULL;
        } elseif ($torzsFields[BaberTargetFieldsTorzsEnum::MUNKASZERZODESIGDATUM] == $defaultEnd) {
            $torzsFields[BaberTargetFieldsTorzsEnum::MUNKASZERZODESIGDATUM] = DefaultFieldEnum::VALUE_NULL;
        }

        $torzsFields[BaberTargetFieldsTorzsEnum::MUNKAKORKOD] =
            ($this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_MUNKAKOR_KOD_EXCEPTION))
                ? DefaultFieldEnum::VALUE_NULL
                : $torzsFields[BaberTargetFieldsTorzsEnum::MUNKAKORKOD];

        $torzsFields[BaberTargetFieldsTorzsEnum::MUNKAKORMEGNEVEZES] =
            ($this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_MUNKAKOR_MEGNEVEZES_EXCEPTION))
                ? DefaultFieldEnum::VALUE_NULL
                : $torzsFields[BaberTargetFieldsTorzsEnum::MUNKAKORMEGNEVEZES];

        $torzsFields[BaberTargetFieldsTorzsEnum::MUNKAKORDATUM] =
            ($this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_MUNKAKOR_DATUM_EXCEPTION))
                ? DefaultFieldEnum::VALUE_NULL
                : $torzsFields[BaberTargetFieldsTorzsEnum::MUNKAKORDATUM];

        $torzsFields[BaberTargetFieldsTorzsEnum::FEORKOD] =
            ($this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_FEORKOD_EXCEPTION))
                ? DefaultFieldEnum::VALUE_NULL
                : $torzsFields[BaberTargetFieldsTorzsEnum::FEORKOD];

        $torzsFields[BaberTargetFieldsTorzsEnum::FEORDATUM] =
            ($this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_FEORDATUM_EXCEPTION))
                ? DefaultFieldEnum::VALUE_NULL
                : $torzsFields[BaberTargetFieldsTorzsEnum::FEORDATUM];

        $torzsFields[BaberTargetFieldsTorzsEnum::BEOSZTASKOD] =
            ($this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_BEOSZTASKOD_EXCEPTION))
                ? DefaultFieldEnum::VALUE_NULL
                : $torzsFields[BaberTargetFieldsTorzsEnum::BEOSZTASKOD];

        $torzsFields[BaberTargetFieldsTorzsEnum::BEOSZTASDATUM] =
            ($this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_BEOSZTAS_DATUM_EXCEPTION))
                ? DefaultFieldEnum::VALUE_NULL
                : $torzsFields[BaberTargetFieldsTorzsEnum::BEOSZTASDATUM];

        $torzsFields[BaberTargetFieldsTorzsEnum::ABKOD] =
            ($this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_AB_KOD_EXCEPTION))
                ? DefaultFieldEnum::VALUE_NULL
                : $torzsFields[BaberTargetFieldsTorzsEnum::ABKOD];

        $torzsFields[BaberTargetFieldsTorzsEnum::ABDATUM] =
            ($this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_AB_DATUM_EXCEPTION))
                ? DefaultFieldEnum::VALUE_NULL
                : $torzsFields[BaberTargetFieldsTorzsEnum::ABDATUM];

        $torzsFields[BaberTargetFieldsTorzsEnum::PROFITCENTRUMKOD] = str_replace(' ', '', (explode(' - ', $costCentersArray[$torzsFields[BaberTargetFieldsTorzsEnum::PROFITCENTRUMKOD]][BaberSourceFieldsEnum::EMPLOYEE_COST_COST_CENTER_NAME] ?? $torzsFields[BaberTargetFieldsTorzsEnum::PROFITCENTRUMKOD])[0] ?? $torzsFields[BaberTargetFieldsTorzsEnum::PROFITCENTRUMKOD]));
        $torzsFields[BaberTargetFieldsTorzsEnum::SZERVEZETKOD] = str_replace(' ', '', (explode(' - ', $costsArray[$torzsFields[BaberTargetFieldsTorzsEnum::SZERVEZETKOD]][BaberSourceFieldsEnum::EMPLOYEE_COST_COST_NAME] ?? $torzsFields[BaberTargetFieldsTorzsEnum::SZERVEZETKOD])[0] ?? $torzsFields[BaberTargetFieldsTorzsEnum::SZERVEZETKOD]));
        $torzsFields[BaberTargetFieldsTorzsEnum::SZERVEZETMEGNEVEZES] = str_replace(' ', '', (explode(' - ', $costsArray[$torzsFields[BaberTargetFieldsTorzsEnum::SZERVEZETMEGNEVEZES]][BaberSourceFieldsEnum::EMPLOYEE_COST_COST_NAME] ?? $torzsFields[BaberTargetFieldsTorzsEnum::SZERVEZETMEGNEVEZES])[1] ?? $torzsFields[BaberTargetFieldsTorzsEnum::SZERVEZETMEGNEVEZES]));
        $torzsFields[BaberTargetFieldsTorzsEnum::FOKSZAMOKKOD] = str_replace(' ', '', (explode(' - ', $costsArray[$torzsFields[BaberTargetFieldsTorzsEnum::FOKSZAMOKKOD]][BaberSourceFieldsEnum::EMPLOYEE_COST_COST_NAME] ?? $torzsFields[BaberTargetFieldsTorzsEnum::FOKSZAMOKKOD])[0] ?? $torzsFields[BaberTargetFieldsTorzsEnum::FOKSZAMOKKOD]));
        $torzsFields[BaberTargetFieldsTorzsEnum::FOKSZAMOKMEGNEVEZES] = str_replace(' ', '', (explode(' - ', $costsArray[$torzsFields[BaberTargetFieldsTorzsEnum::FOKSZAMOKMEGNEVEZES]][BaberSourceFieldsEnum::EMPLOYEE_COST_COST_NAME] ?? $torzsFields[BaberTargetFieldsTorzsEnum::FOKSZAMOKMEGNEVEZES])[1] ?? $torzsFields[BaberTargetFieldsTorzsEnum::FOKSZAMOKMEGNEVEZES]));

        if ($this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_BERTIPKOD_EXCEPTION)) {
            $torzsFields[BaberTargetFieldsTorzsEnum::BERTIPKOD] = '2';
        } elseif (isset($config[BaberTargetFieldsTorzsEnum::BERTIPKOD][$torzsFields[BaberTargetFieldsTorzsEnum::BERTIPKOD]])) {
            $torzsFields[BaberTargetFieldsTorzsEnum::BERTIPKOD] = $config[BaberTargetFieldsTorzsEnum::BERTIPKOD][$torzsFields[BaberTargetFieldsTorzsEnum::BERTIPKOD]];
        }

        if (!$this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_ALAPBER_EXCEPTION)) {
            $torzsFields[BaberTargetFieldsTorzsEnum::ALAPBER] = ($employeeContractRow[EmployeeMainDataEnum::WAGE_TYPE] == 'MoLo') ? $this->getRowValueOrNull($employeeSalaryRow[BaberSourceFieldsEnum::EMPLOYEE_SALARY_PERSONAL_MONTH_SALARY] ?? NULL) : (($employeeContractRow[EmployeeMainDataEnum::WAGE_TYPE] == 'OB') ? $this->getRowValueOrNull($employeeSalaryRow[BaberSourceFieldsEnum::EMPLOYEE_SALARY_PERSONAL_HOUR_SALARY] ?? NULL) : DefaultFieldEnum::VALUE_NULL);
        }

        $torzsFields[BaberTargetFieldsTorzsEnum::ALAPBERVALTOZASDATUM] =
            $this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_ALAPBER_VALTOZAS_DATUM_EXCEPTION)
                ? DefaultFieldEnum::VALUE_NULL
                : $torzsFields[BaberTargetFieldsTorzsEnum::ALAPBERVALTOZASDATUM];

        $torzsFields[BaberTargetFieldsTorzsEnum::BANKKOD] = DefaultFieldEnum::BankKod;
        $torzsFields[BaberTargetFieldsTorzsEnum::PENZTARKOD] = str_replace(' ', '', (explode(' - ', $costCentersArray[$torzsFields[BaberTargetFieldsTorzsEnum::PENZTARKOD]][BaberSourceFieldsEnum::EMPLOYEE_COST_COST_CENTER_NAME] ?? $torzsFields[BaberTargetFieldsTorzsEnum::PENZTARKOD])[0] ?? $torzsFields[BaberTargetFieldsTorzsEnum::PENZTARKOD]));

        $torzsFields[BaberTargetFieldsTorzsEnum::MUNKHELYSEG] = $appLookupDictsArray[BaberSourceFieldsEnum::EMPLOYEE_EXT3_OPTION6][$torzsFields[BaberTargetFieldsTorzsEnum::MUNKHELYSEG]] ?? DefaultFieldEnum::VALUE_NULL;

        $torzsFields[BaberTargetFieldsTorzsEnum::HETIMUNKAORA] =
            $this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_HETI_MUNKAORA_EXCEPTION)
                ? DefaultFieldEnum::VALUE_NULL
                : ($torzsFields[BaberTargetFieldsTorzsEnum::HETIMUNKAORA] * 5);

        $torzsFields[BaberTargetFieldsTorzsEnum::HETIMUNKAORAVALTOZASDATUM] =
            $this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_HETI_MUNKAORA_VALTOZAS_DATUM_EXCEPTION)
                ? DefaultFieldEnum::VALUE_NULL
                : $torzsFields[BaberTargetFieldsTorzsEnum::HETIMUNKAORAVALTOZASDATUM];

        if ($this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_MUSZAKOK_KOD_EXCEPTION)) {
            $torzsFields[BaberTargetFieldsTorzsEnum::MUSZAKOKKOD] = DefaultFieldEnum::VALUE_NULL;
        } elseif (isset($config[BaberTargetFieldsTorzsEnum::MUSZAKOKKOD][$torzsFields[BaberTargetFieldsTorzsEnum::MUSZAKOKKOD]])) {
            $torzsFields[BaberTargetFieldsTorzsEnum::MUSZAKOKKOD] = $config[BaberTargetFieldsTorzsEnum::MUSZAKOKKOD][$torzsFields[BaberTargetFieldsTorzsEnum::MUSZAKOKKOD]];
        }

        $torzsFields[BaberTargetFieldsTorzsEnum::MUSZAKOKDATUM] =
            $this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_MUSZAKOK_DATUM_EXCEPTION)
                ? DefaultFieldEnum::VALUE_NULL
                : $torzsFields[BaberTargetFieldsTorzsEnum::MUSZAKOKDATUM];

        $torzsFields[BaberTargetFieldsTorzsEnum::TORZSSZAM] = match ((string)$employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE]) {
            '31' => $employeeRow[EmployeeMainDataEnum::EMP_ID] . '5',
            '32' => $employeeRow[EmployeeMainDataEnum::EMP_ID] . '15',
            '33' => $employeeRow[EmployeeMainDataEnum::EMP_ID] . '16',
            '10' => $employeeRow[EmployeeMainDataEnum::EMP_ID] . '12',
            '18' => $employeeRow[EmployeeMainDataEnum::EMP_ID] . '11',
            default => $employeeRow[EmployeeMainDataEnum::EMP_ID],
        };

        $torzsFields[BaberTargetFieldsTorzsEnum::UTOLSOMUNKANAP] =
            $this->isInArrayExceptionConfig($employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE], $config, BaberExportEnum::CONFIG_UTOLSO_MUNKANAP_EXCEPTION)
                ? DefaultFieldEnum::VALUE_NULL
                : $this->getLastDayWorkday(
                $employeeAbsencesByHistory,
                $employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_ID],
                $torzsFields[BaberTargetFieldsTorzsEnum::UTOLSOMUNKANAP],
                $defaultEnd
            );

        $eltartFieldsRequiredOptions = [
            BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION31,
            BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION32,
            BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION33,
            BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION34,
            BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION35,
        ];

        $i = 0;
        $eltartFields = [];
        foreach ($eltartFieldsRequiredOptions as $option) {
            if (!empty($employeeExt5Row[$option])) {
                $i++;
                $eltartFields[] = $this->generateEltartFields(
                    $option,
                    $row,
                    $config,
                    $employeeExt5Row,
                    $employeeContractRow,
                    $employeeRow,
                    $torzsFields,
                    $i
                );
            }
        }

        return [$torzsFields, $eltartFields];
    }

    /**
     * @throws InvalidArgumentException
     */
    private function generateEltartFields(
        string $option,
        Row    $row,
        array  $config,
        array  $employeeExt5Row,
        array  $employeeContractRow,
        array  $employeeRow,
        array  $torzsFields,
        int    $id
    ): array
    {
        $formattedCurrentDate = clone $this->baberRawDataDescriptor->getCurrentDate();
        $formattedCurrentDate->format('Y.m.d');

        $specifiedFields = [
            BaberTargetFieldsCsEltartottEnum::DOLGOZOAZONOSITO => $this->getRowValueOrNull($row->valueOf(BaberExportEnum::ENTRY_EMPLOYEE)[EmployeeMainDataEnum::TAX_NUMBER]),
            BaberTargetFieldsCsEltartottEnum::ADOAZONOSITOJEL => DefaultFieldEnum::VALUE_NULL,
            BaberTargetFieldsCsEltartottEnum::TAJSZAM => DefaultFieldEnum::VALUE_NULL,
            BaberTargetFieldsCsEltartottEnum::NEVE => DefaultFieldEnum::VALUE_NULL,
            BaberTargetFieldsCsEltartottEnum::NEME => DefaultFieldEnum::VALUE_NULL,
            BaberTargetFieldsCsEltartottEnum::ALLAMPOLG => DefaultFieldEnum::SZULETESIORSZAG_ALLAMPOLG,
            BaberTargetFieldsCsEltartottEnum::SZULIDO => DefaultFieldEnum::VALUE_NULL,
            BaberTargetFieldsCsEltartottEnum::SZULHELYSEG => DefaultFieldEnum::VALUE_NULL,
            BaberTargetFieldsCsEltartottEnum::ANYJANEVE => DefaultFieldEnum::VALUE_NULL,
            BaberTargetFieldsCsEltartottEnum::APJANEVE => DefaultFieldEnum::VALUE_NULL,
            BaberTargetFieldsCsEltartottEnum::GYEREKID => DefaultFieldEnum::VALUE_NULL,
            BaberTargetFieldsCsEltartottEnum::POTSZABADSAG => DefaultFieldEnum::VALUE_NULL,
            BaberTargetFieldsCsEltartottEnum::FOGYATEKOSSAG => DefaultFieldEnum::VALUE_NULL,
            BaberTargetFieldsCsEltartottEnum::JOGVISZONYAZONOSITO => $torzsFields[BaberTargetFieldsTorzsEnum::JOGVISZONYAZONOSITO],
        ];

        switch ($option) {
            case BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION31:
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::ADOAZONOSITOJEL] = $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION31]);
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::TAJSZAM] = $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION26]);
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::NEVE] =
                    $employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION1] . ' ' . $employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION6];
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::SZULIDO] = $this->convertDateFormat($this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION16]));
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::SZULHELYSEG] = $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION11]);
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::ANYJANEVE] = $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION21]);
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::GYEREKID] = $employeeRow[EmployeeMainDataEnum::EMP_ID] . $employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_NUMBER] . $id;

                $specifiedFields[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG] =
                    isset($config[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG][$employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION41]])
                        ? $this->getRowValueOrNull($config[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG][$employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION41]])
                        : $specifiedFields[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG];

                $specifiedFields[BaberTargetFieldsCsEltartottEnum::FOGYATEKOSSAG] = (
                    ($this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION46]) == '1' && $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION51] == '1'))
                    || (
                        $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION46]) == '1'
                        && $this->convertDateFormat($this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION56])) > $formattedCurrentDate
                        && $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION51]) == '2'
                    )
                ) ? $config[BaberTargetFieldsCsEltartottEnum::FOGYATEKOSSAG][1] : $config[BaberTargetFieldsCsEltartottEnum::FOGYATEKOSSAG][2];
                break;
            case BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION32:
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::ADOAZONOSITOJEL] = $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION32]);
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::TAJSZAM] = $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION27]);
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::NEVE] =
                    $employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION2] . ' ' . $employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION7];
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::SZULIDO] = $this->convertDateFormat($this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION17]));
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::ANYJANEVE] = $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION22]);
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::GYEREKID] = $employeeRow[EmployeeMainDataEnum::EMP_ID] . $employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_NUMBER] . $id;

                $specifiedFields[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG] =
                    isset($config[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG][$employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION42]])
                        ? $this->getRowValueOrNull($config[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG][$employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION42]])
                        : $specifiedFields[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG];

                $specifiedFields[BaberTargetFieldsCsEltartottEnum::FOGYATEKOSSAG] = (
                    ($this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION47]) == '1' && $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION52] == '1'))
                    || (
                        $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION47]) == '1'
                        && $this->convertDateFormat($this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION57])) > $formattedCurrentDate
                        && $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION52]) == '2'
                    )
                ) ? $config[BaberTargetFieldsCsEltartottEnum::FOGYATEKOSSAG][1] : $config[BaberTargetFieldsCsEltartottEnum::FOGYATEKOSSAG][2];
                break;
            case BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION33:
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::ADOAZONOSITOJEL] = $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION33]);
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::TAJSZAM] = $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION28]);
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::NEVE] =
                    $employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION3] . ' ' . $employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION8];
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::SZULIDO] = $this->convertDateFormat($this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION18]));
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::ANYJANEVE] = $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION23]);
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::GYEREKID] = $employeeRow[EmployeeMainDataEnum::EMP_ID] . $employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_NUMBER] . $id;

                $specifiedFields[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG] =
                    isset($config[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG][$employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION43]])
                        ? $this->getRowValueOrNull($config[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG][$employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION43]])
                        : $specifiedFields[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG];

                $specifiedFields[BaberTargetFieldsCsEltartottEnum::FOGYATEKOSSAG] = (
                    ($this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION48]) == '1' && $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION53] == '1'))
                    || (
                        $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION48]) == '1'
                        && $this->convertDateFormat($this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION58])) > $formattedCurrentDate
                        && $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION53]) == '2'
                    )
                ) ? $config[BaberTargetFieldsCsEltartottEnum::FOGYATEKOSSAG][1] : $config[BaberTargetFieldsCsEltartottEnum::FOGYATEKOSSAG][2];
                break;
            case BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION34:
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::ADOAZONOSITOJEL] = $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION34]);
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::TAJSZAM] = $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION29]);
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::NEVE] =
                    $employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION4] . ' ' . $employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION9];
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::SZULIDO] = $this->convertDateFormat($this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION19]));
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::ANYJANEVE] = $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION24]);
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::GYEREKID] = $employeeRow[EmployeeMainDataEnum::EMP_ID] . $employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_NUMBER] . $id;

                $specifiedFields[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG] =
                    isset($config[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG][$employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION44]])
                        ? $this->getRowValueOrNull($config[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG][$employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION44]])
                        : $specifiedFields[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG];

                $specifiedFields[BaberTargetFieldsCsEltartottEnum::FOGYATEKOSSAG] = (
                    ($this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION49]) == '1' && $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION54] == '1'))
                    || (
                        $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION49]) == '1'
                        && $this->convertDateFormat($this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION59])) > $formattedCurrentDate
                        && $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION54]) == '2'
                    )
                ) ? $config[BaberTargetFieldsCsEltartottEnum::FOGYATEKOSSAG][1] : $config[BaberTargetFieldsCsEltartottEnum::FOGYATEKOSSAG][2];
                break;
            case BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION35:
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::ADOAZONOSITOJEL] = $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION35]);
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::TAJSZAM] = $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION30]);
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::NEVE] =
                    $employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION5] . ' ' . $employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION10];
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::SZULIDO] = $this->convertDateFormat($this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION20]));
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::ANYJANEVE] = $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION25]);
                $specifiedFields[BaberTargetFieldsCsEltartottEnum::GYEREKID] = $employeeRow[EmployeeMainDataEnum::EMP_ID] . $employeeContractRow[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_NUMBER] . $id;

                $specifiedFields[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG] =
                    isset($config[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG][$employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION45]])
                        ? $this->getRowValueOrNull($config[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG][$employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION45]])
                        : $specifiedFields[BaberTargetFieldsCsEltartottEnum::POTSZABADSAG];

                $specifiedFields[BaberTargetFieldsCsEltartottEnum::FOGYATEKOSSAG] = (
                    ($this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION50]) == '1' && $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION55] == '1'))
                    || (
                        $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION50]) == '1'
                        && $this->convertDateFormat($this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION60])) > $formattedCurrentDate
                        && $this->getRowValueOrNull($employeeExt5Row[BaberSourceFieldsEnum::EMPLOYEE_EXT5_OPTION55]) == '2'
                    )
                ) ? $config[BaberTargetFieldsCsEltartottEnum::FOGYATEKOSSAG][1] : $config[BaberTargetFieldsCsEltartottEnum::FOGYATEKOSSAG][2];
                break;
            default:
                break;
        }

        return $this->generateHashId($specifiedFields);
    }

    private function generateHashId($specifiedFields): array
    {
        $hashId = md5(json_encode($specifiedFields));
        $specifiedFields[BaberExportEnum::HASH_ID] = $hashId;

        return $specifiedFields;
    }

    private function rowsToArray($rows): array
    {
        $result = [];
        foreach ($rows as $row) {
            $result = $row->toArray();
        }
        return $result[0];
    }

    private function getRowValueOrNull(mixed $value): string
    {
        return !empty($value) && !is_null($value) ? (string)$value : 'NULL';
    }

    private function convertDateFormat(string $date, bool $checkLowerLimit = false): string
    {
        if (preg_match('/^[0-9]{4}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/', $date)) {
            if ($checkLowerLimit && strtotime($date) < strtotime(DefaultFieldEnum::LEAST_DATE)) {
                $date = DefaultFieldEnum::LEAST_DATE;
            }

            return str_replace('-', '.', $date);
        }

        return $date;
    }

    private function getLastDayWorkday(array $employeeAbsencesByHistory, string $employeeContractId, string $torzsFieldsUtolsoMunkanap, string $defaultEnd): string
    {
        if ($torzsFieldsUtolsoMunkanap == $defaultEnd) {
            return DefaultFieldEnum::VALUE_NULL;
        }

        $lastDay = \DateTime::createFromFormat('Y.m.d', $torzsFieldsUtolsoMunkanap);

        while (
            isset($employeeAbsencesByHistory[$employeeContractId][$lastDay->format('Y.m.d')])
            || $lastDay->format('w') == 0
            || $lastDay->format('w') == 6
        ) {
            $lastDay->modify('-1 day');
        }

        return $lastDay->format('Y.m.d');
    }

    private function isInArrayExceptionConfig(string|int|null $needleValue, array $haystackConfigArray, string $configObjectName): bool
    {
        if (isset($haystackConfigArray[$configObjectName]) && in_array($needleValue, $haystackConfigArray[$configObjectName])) {
            return true;
        }

        return false;
    }

    private function getJogviszonyDatumByChanges(array $employeeContractModels): ?string
    {
        $validityRows = [];
        foreach ($employeeContractModels as $employeeContract) {
            $validityRow = new ValidityRowDescriptor();
            $validityRow->setValidFrom($employeeContract[EmployeeMainDataEnum::VALID_FROM]);
            $validityRow->setValidTo($employeeContract[EmployeeMainDataEnum::VALID_TO]);
            $tempData = [];
            $tempData[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE] = $employeeContract[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_TYPE];
            $tempData[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_NUMBER] = $employeeContract[EmployeeMainDataEnum::EMPLOYEE_CONTRACT_NUMBER];
            $validityRow->setValidityRowArray($tempData);
            $validityRows[] = $validityRow;
        }

        $validityTransformer = new ValidityRowDescriptorSortingToArrayTransformer();
        $result = $validityTransformer->transform($validityRows);
        $lastElement = end($result);

        return $lastElement[EmployeeMainDataEnum::VALID_FROM]->format('Y-m-d');
    }

}