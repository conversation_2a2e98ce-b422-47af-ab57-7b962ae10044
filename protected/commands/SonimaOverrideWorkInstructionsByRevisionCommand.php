<?php

declare(strict_types=1);

$composerAutoloder = include_once('../vendor/autoload.php');
Yang::registerAutoloader([$composerAutoloder, 'loadClass'], true);
Yang::loadComponentNamespaces('Core');

use Components\Core\Command\AbstractUserSessionCommand;
use phpseclib3\Net\SFTP;

final class SonimaOverrideWorkInstructionsByRevisionCommand extends AbstractUserSessionCommand
{
    private const FILE_GROUP_ID = 'sonimaSFTPDocuments';
    private const VALIDITY_INACTIVE = 'inactive';
    private const MODIFIED_BY = 'revisionOverride';
    private const CREATED_BY = 'newRevision';
    private const DEACTIVATED = 'deactivated';
    private string $currentDate;
    private int $publishedStatus;
    private array $sftpConfig;
    private array $indexedFileStorage;
    private array $deactivatedInstructions = [];
    private bool $postProcessingCleanup = false;

    private string $defaultCompanyId = 'd0e372703226031ddd7e066301534450';

    public function __construct()
    {
        $this->sftpConfig = Yang::getParam('additionalComponents')['SFTPFileDownloaderConfig'];
        $this->currentDate = date('Y-m-d');
        $this->publishedStatus = Status::PUBLISHED;
    }

	public function actionRun(bool $postProcessingCleanup = false): int
	{
		try {
			if ($postProcessingCleanup) {
				$this->postProcessingCleanup = true;
			}

			$this->runOverrideWorkInstructions();

			$transaction = Yang::app()->db->beginTransaction();
			try {
				$this->deactivateInstructions();
				$transaction->commit();
			} catch (Exception $ex) {
				$transaction->rollback();
				Yii::log("Deactivation error: " . $ex->getMessage(), 'error');
			}

			return 0;
		} catch (Exception $ex) {
			Yii::log("Fatal error: " . $ex->getMessage(), 'error');
			return 1;
		}
	}

    /**
     * @throws Exception
     */
    private function runOverrideWorkInstructions(): void
    {
        $fileStorage = $this->getFileStorage();
        $this->indexedFileStorage = $this->transformFileStorage($fileStorage, '__', '--');
        $competencies = $this->getCompetencies();
        $trainings = $this->getTrainings();
        $trainingLinks = $this->getTrainingLinks();
        $competencyRequirements = $this->getCompetencyRequirements();
        $this->transformFileStorageByDeactivated();

        $errors = [];
        $processedData = [];
        $prevInstruction = null;
        foreach ($this->indexedFileStorage as $fileName) {
            foreach ($fileName as $instructionId => $revision) {
                if ($instructionId == $prevInstruction) {
                    continue;
                }

                $prevInstruction = $instructionId;
                $filePath = $instructionId . '__' . $revision . '.pdf';
				
	            if (!isset($fileStorage[$filePath])) {
		            $errors[] = "The file was not found: $filePath";
		            continue;
	            }

	            $file = $fileStorage[$filePath];

	            if (pathinfo($file->file_name, PATHINFO_EXTENSION) === 'txt') {
		            continue;
	            }

	            $instructionName = $this->getInstructionNameFromMetaDataFile($instructionId . '__' . $revision);

                if (strlen($revision) != 2) {
                    $errors[] = "The revision is not a two-digit number: $revision in the case of instruction ID $instructionId.";
                    continue;
                }

                if (!isset($fileStorage[$filePath])) {
                    $errors[] = "The file was not found: $filePath";
                    continue;
                }

                $shortName = $this->extractShortName($file->file_name);
                if (empty($shortName)) {
                    $errors[] = "The short name is empty: $file->file_name";
                    continue;
                }

                $processedCompetencies = $this->handleCompetencies($instructionId, $revision, $shortName, $competencies, $fileStorage, $instructionName);
                $processedTrainings = $this->handleTrainings($instructionId, $revision, $shortName, $trainings, $fileStorage);
                $processedTrainingLinks = $this->handleTrainingLinks($instructionId, $revision, $shortName, $trainingLinks, $fileStorage);
                $processedCompetencyRequirements = $this->handleCompetencyRequirements($instructionId, $revision, $shortName, $competencyRequirements, $fileStorage);

                $processedData = array_unique(array_merge($processedData, $processedCompetencies, $processedTrainings, $processedTrainingLinks, $processedCompetencyRequirements));
            }
        }

        $processedData = array_values(array_unique($processedData));

        $this->handleResponse($errors, $processedData);
    }

    private function getFileStorage(): array
    {
        $criteria = new CDbCriteria();
        $criteria->condition = "`file_group_id` = '" . self::FILE_GROUP_ID . "' AND `file_name` NOT LIKE 'HU-errors%' AND `file_type` <> 'text/plain'";
        $criteria->order = '`file_name` DESC';

        $fileStorage = FileStorage::model()->findAll($criteria);
        if (!is_array($fileStorage)) {
            $fileStorage = [];
        }

        $indexedFileStorage = [];
        foreach ($fileStorage as $file) {
            [$indexedFileName,] = $this->extractFileName($file->file_name);
            $indexedFileStorage[$indexedFileName] = $file;
        }

        return $indexedFileStorage;
    }

	private function getInstructionNameFromMetaDataFile(string $filename): string
	{
		$instructionName = '';

		$criteria = new CDbCriteria();
		$criteria->condition = "`file_type` = 'text/plain' AND `file_name` LIKE '%{$filename}%'";

		$fileStorage = FileStorage::model()->find($criteria);
		if (!$fileStorage) {
			return $instructionName;
		}

		$metaFilePath = Yang::getBasePath() . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'webroot' . DIRECTORY_SEPARATOR . DIRECTORY_SEPARATOR . $fileStorage->file_url;
		$metaFile = $fileStorage->file_name;

		if (file_exists($metaFilePath)) {
			$content = file_get_contents($metaFilePath);
			$content = mb_convert_encoding($content, 'UTF-8', mb_detect_encoding($content, 'UTF-8, ISO-8859-2, ISO-8859-1'));

			if (preg_match('/documentname=([^\r\n]+)/u', $content, $matches)) {
				return str_replace('_', ' ', html_entity_decode(trim($matches[1]), ENT_QUOTES, 'UTF-8'));
			}
		}

		return $instructionName;
	}

    private function getCompetencies(): array
    {
        $criteria = new CDbCriteria();
        $criteria->condition = '`status` = ' . Status::PUBLISHED .
            ' AND `validity` IS NULL';
        $competencies = Competency::model()->findAll($criteria);
        if (!is_array($competencies)) {
            $competencies = [];
        }

        $indexedCompetencies = [];
        foreach ($competencies as $competency) {
            [$instructionId, $revision] = $this->extractIdAndRevision($competency->competency_id, '--');
            $indexedCompetencies[$instructionId][$revision] = $competency;
        }

        return $indexedCompetencies;
    }

    private function getTrainings(): array
    {
        $criteria = new CDbCriteria();
        $criteria->condition = '`status` = ' . Status::PUBLISHED .
            " AND '$this->currentDate' BETWEEN `valid_from` AND `valid_to`";
        $trainings = Training::model()->findAll($criteria);
        if (!is_array($trainings)) {
            $trainings = [];
        }

        $indexedTrainings = [];
        foreach ($trainings as $training) {
            [$instructionId, $revision] = $this->extractIdAndRevision($training->training_id, '--');
            $indexedTrainings[$instructionId][$revision] = $training;
        }

        return $indexedTrainings;
    }

    private function extractIdAndRevision(string $fileName, string $seperator): array
    {
        $id = $fileName;
        $revision = '0';

        $lastDoubleDashPosition = strrpos($fileName, $seperator);
        if ($lastDoubleDashPosition !== false) {
            $id = substr($fileName, 0, $lastDoubleDashPosition);
            $revision = substr($fileName, $lastDoubleDashPosition + 2);

            $lastDotPosition = strrpos($revision, '.');
            if ($lastDotPosition !== false) {
                $revision = substr($revision, 0, $lastDotPosition);
            }
        }

        return [$id, $revision];
    }

    private function getIdsWithRevision(array $fileNames): array
    {
        $transformedData = [];
        foreach ($fileNames as $fileName) {
            list($id, $revision) = $this->extractIdAndRevision($fileName, '--');
            $transformedData[] = [$id => $revision];
        }

        return $transformedData;
    }

    private function transformFileStorage(array $fileStorage, string $search, string $replace): array
    {
        $transformedFileStorage = array_map(function ($key) use ($fileStorage, $search, $replace) {
            $newKey = str_replace($search, $replace, $key);
            return [$newKey => $fileStorage[$key]];
        }, array_keys($fileStorage));

        $transformedFileIds = array_keys(array_merge(...$transformedFileStorage));

        return $this->getIdsWithRevision($transformedFileIds);
    }

    private function handleCompetencies(string $instructionId, string $revision, string $shortName, array $competencies, array $fileStorage,  string $instructionName): array
    {
        if (!isset($competencies[$instructionId][$revision])) {
            if (isset($competencies[$instructionId])) {
                $this->overrideCompetency($competencies[$instructionId]);
            }
            $this->insertNonExistentCompetencies($instructionId, $revision, $shortName, $fileStorage, $instructionName);

            return [$instructionId . '__' . $revision . '_' . $shortName . '.pdf'];
        }

        return [];
    }

    private function overrideCompetency(array $currentCompetency): void
    {
        $currentCompetency = reset($currentCompetency);
        $currentCompetency->validity = self::VALIDITY_INACTIVE;
        $currentCompetency->modified_by = self::MODIFIED_BY;
        $currentCompetency->modified_on = date('Y-m-d H:i:s');

        if ($currentCompetency->validate()) {
            $currentCompetency->save();
        }
    }

    private function handleTrainings(string $instructionId, string $revision, string $shortName, array $trainings, array $fileStorage): array
    {
        if (!isset($trainings[$instructionId][$revision])) {
            if (isset ($trainings[$instructionId])) {
                $this->overrideTraining($trainings[$instructionId], $fileStorage, $revision);
            }
            $this->insertNonExistentTrainings($instructionId, $revision, $shortName, $fileStorage);

            return [$instructionId . '__' . $revision . '_' . $shortName . '.pdf'];
        }

        return [];
    }

    private function insertNonExistentCompetencies(string $instructionId, string $revision, string $shortName, array $fileStorage, string $instructionName): void
    {
        $nonExistentCompetency = $fileStorage[$instructionId . '__' . $revision . '.pdf'];
        [$fileName, $filenameWithRevision] = $this->extractFileName($nonExistentCompetency->file_name);
        $competencyGroupId = $shortName ? $this->getCompetencyGroupDataByShortName($shortName, 'competency_group_id') : null;

        [$newId, $newRevision] = $this->extractIdAndRevision($fileName, '__');
        $competencyId = $newId . '--' . $newRevision;

        $newCompetency = new Competency();
        $newCompetency->setIsNewRecord(true);
        $newCompetency->competency_id = $competencyId;
        $newCompetency->competency_name = $competencyId . '_' . $shortName . ' | ' . $instructionName;
        $newCompetency->competency_group_id = $competencyGroupId;
        $newCompetency->created_by = self::CREATED_BY;
        $newCompetency->created_on = $nonExistentCompetency->file_upload_date;

        if ($newCompetency->validate()) {
            $newCompetency->save();
        }
    }

    private function overrideTraining(array $training, array $fileStorage, string $newRevision): void
    {
        $currentTraining = reset($training);
        [$id, $revision] = $this->extractIdAndRevision($currentTraining->training_id, '--');

        $closeDate = $fileStorage[$id . '__' . $newRevision . '.pdf']->file_upload_date;
        $closeDate = date('Y-m-d', strtotime($closeDate . ' -1 day'));
        $currentTraining->valid_to = $closeDate;
        $currentTraining->modified_by = self::MODIFIED_BY;
        $currentTraining->modified_on = date('Y-m-d H:i:s');

        if ($currentTraining->validate()) {
            $currentTraining->save();
        }
    }

    private function insertNonExistentTrainings(string $instructionId, string $revision, string $shortName, array $fileStorage): void
    {
        $realFileName = $instructionId . '__' . $revision . '.pdf';
        $nonExistentTraining = $fileStorage[$realFileName];
        [$fileName,] = $this->extractFileName($nonExistentTraining->file_name);
        [$newId, $newRevision] = $this->extractIdAndRevision($fileName, '__');
        $trainingId = $newId . '--' . $newRevision;
        $validFrom = date('Y-m-d', strtotime($nonExistentTraining->file_upload_date));

        $newTraining = new Training();
        $newTraining->setIsNewRecord(true);
        $newTraining->training_id = $trainingId;
        $newTraining->training_name = $trainingId . '_' . $shortName;
        $newTraining->status = Status::PUBLISHED;
        $newTraining->valid_from = $validFrom;
        $newTraining->valid_to = App::getSetting('defaultEnd');
        $newTraining->created_by = self::CREATED_BY;
        $newTraining->created_on = $nonExistentTraining->file_upload_date;

        if ($newTraining->validate()) {
            $newTraining->save();
        }
    }

    private function getTrainingLinks(): array
    {
        $criteria = new CDbCriteria();
        $criteria->condition = '`status` = ' . Status::PUBLISHED;
        $trainingLinks = TrainingLink::model()->findAll($criteria);
        if (!is_array($trainingLinks)) {
            $trainingLinks = [];
        }

        $indexedTrainings = [];
        foreach ($trainingLinks as $trainingLink) {
            [$instructionId, $revision] = $this->extractIdAndRevision($trainingLink->competency_id, '--');
            $indexedTrainings[$instructionId][$revision] = $trainingLink;
        }

        return $indexedTrainings;
    }

    private function handleTrainingLinks(string $instructionId, string $revision, string $shortName, array $trainingLinks, array $fileStorage): array
    {
        if (!isset($trainingLinks[$instructionId][$revision])) {
            if (isset ($trainingLinks[$instructionId])) {
                $this->overrideTrainingLinks($trainingLinks[$instructionId]);
            }
            $this->insertNonExistentTrainingLinks($instructionId, $revision, $shortName, $fileStorage);

            return [$instructionId . '__' . $revision . '_' . $shortName . '.pdf'];
        }

        return [];
    }

    private function insertNonExistentTrainingLinks(string $instructionId, string $revision, string $shortName, array $fileStorage): void
    {
        $nonExistentTrainingLink = $fileStorage[$instructionId . '__' . $revision . '.pdf'];
        [$fileName,] = $this->extractFileName($nonExistentTrainingLink->file_name);
        [$newId, $newRevision] = $this->extractIdAndRevision($fileName, '__');
        $trainingLinkId = $newId . '--' . $newRevision;

        $newTrainingLink = new TrainingLink();
        $newTrainingLink->setIsNewRecord(true);
        $newTrainingLink->competency_id = $trainingLinkId;
        $newTrainingLink->training_id = $trainingLinkId;
        $newTrainingLink->created_by = self::CREATED_BY;
        $newTrainingLink->created_on = $nonExistentTrainingLink->file_upload_date;
        $newTrainingLink->status = Status::PUBLISHED;

        if ($newTrainingLink->validate()) {
            $newTrainingLink->save();
        }
    }

    private function overrideTrainingLinks(array $trainingLinks): void
    {
        $currentTrainingLink = reset($trainingLinks);
        $currentTrainingLink->status = Status::DELETED;
        $currentTrainingLink->modified_by = self::MODIFIED_BY;
        $currentTrainingLink->modified_on = date('Y-m-d H:i:s');

        if ($currentTrainingLink->validate()) {
            $currentTrainingLink->save();
        }
    }

    private function getCompetencyRequirements(): array
    {
        $criteria = new CDbCriteria();
        $criteria->condition = '`status` = ' . Status::PUBLISHED;
        $competencyRequirements = CompetencyRequirement::model()->findAll($criteria);
        if (!is_array($competencyRequirements)) {
            $competencyRequirements = [];
        }

        $indexedRequirements = [];
        foreach ($competencyRequirements as $competencyRequirement) {
            [$instructionId, $revision] = $this->extractIdAndRevision($competencyRequirement->competency_id, '--');
            $indexedRequirements[$instructionId][$revision] = $competencyRequirement;
        }

        return $indexedRequirements;
    }

    private function handleCompetencyRequirements(string $instructionId, string $revision, string $shortName, array $competencyRequirements, array $fileStorage): array
    {
        if (!isset($competencyRequirements[$instructionId][$revision])) {
            if (isset ($competencyRequirements[$instructionId])) {
                $this->overrideCompetencyRequirements($competencyRequirements[$instructionId]);
            }
            $this->insertNonExistentCompetencyRequirements($instructionId, $revision, $shortName, $fileStorage);

            return [$instructionId . '__' . $revision . '_' . $shortName . '.pdf'];
        }

        return [];
    }

    private function insertNonExistentCompetencyRequirements(string $instructionId, string $revision, string $shortName, array $fileStorage): void
    {
        $nonExistentCompetencyRequirement = $fileStorage[$instructionId . '__' . $revision . '.pdf'];

        [$fileName, $filenameWithRevision] = $this->extractFileName($nonExistentCompetencyRequirement->file_name);
        $competencyGroupName = $shortName ? $this->getCompetencyGroupDataByShortName($shortName, 'competency_group_name') : null;
        $unitId = $competencyGroupName ? $this->getUnitIdByCompetencyGroupName($competencyGroupName) : null;
        $employeeContractIds = $this->getAllEmployeeContractsInUnit($unitId);

        [$newId, $newRevision] = $this->extractIdAndRevision($fileName, '__');
        $competencyId = $newId . '--' . $newRevision;

        $treeLevel = '{"unit": "' . $unitId . '","company": "' . $this->defaultCompanyId . '"}';

        $newCompetencyRequirement = new CompetencyRequirement();
        $newCompetencyRequirement->setIsNewRecord(true);
        $newCompetencyRequirement->tree_level = $treeLevel;
        $newCompetencyRequirement->competency_id = $competencyId;
        $newCompetencyRequirement->level = 2;
        $newCompetencyRequirement->status = $this->publishedStatus;
        $newCompetencyRequirement->created_by = self::CREATED_BY;
        $newCompetencyRequirement->created_on = $nonExistentCompetencyRequirement->file_upload_date;

        if ($newCompetencyRequirement->validate()) {
            $newCompetencyRequirement->save();
            $trainingRequests = $this->getTrainingRequests();
            $draftEmployeeCompetencies = $this->getDraftEmployeeCompetencies();

            $this->handleTrainingRequests($trainingRequests, $employeeContractIds, $fileStorage, $newId);
            $this->handleDraftEmployeeCompetencies($draftEmployeeCompetencies, $employeeContractIds, $fileStorage, $newId);
        }
    }

    private function overrideCompetencyRequirements(array $competencyRequirements): void
    {
        $currentCompetencyRequirement = reset($competencyRequirements);
        $currentCompetencyRequirement->status = Status::DELETED;
        $currentCompetencyRequirement->modified_by = self::MODIFIED_BY;
        $currentCompetencyRequirement->modified_on = date('Y-m-d H:i:s');

        if ($currentCompetencyRequirement->validate()) {
            $currentCompetencyRequirement->save();
        }
    }

    private function getTrainingRequests(): array
    {
        $criteria = new CDbCriteria();
        $criteria->select = 'DISTINCT competency_id';
        $criteria->condition = '`status` = ' . Status::DRAFT;
        $trainingRequests = TrainingRequest::model()->findAll($criteria);
        if (!is_array($trainingRequests)) {
            $trainingRequests = [];
        }

        $indexedRequests = [];
        foreach ($trainingRequests as $trainingRequest) {
            [$instructionId, $revision] = $this->extractIdAndRevision($trainingRequest->competency_id, '--');
            $indexedRequests[$instructionId][$revision] = $trainingRequest;
        }

        return $indexedRequests;
    }

    private function handleTrainingRequests(array $trainingRequests, array $employeeContractIds, array $fileStorage, string $instrId): void
    {
        $currentIndex = false;
        foreach ($this->indexedFileStorage as $key => $fileStorageElement) {
            if (key($fileStorageElement) === $instrId) {
                $currentIndex = $key;
            }
        }

        if ($currentIndex !== false) {
            if (!isset($trainingRequests[$instrId][$this->indexedFileStorage[$currentIndex][$instrId]])) {
                if (isset($trainingRequests[$instrId])) {
                    $this->overrideTrainingRequests($trainingRequests[$instrId]);
                }

                foreach ($employeeContractIds as $employeeContractId) {
                    $this->insertNonExistentTrainingRequests($instrId, $employeeContractId, $this->indexedFileStorage[$currentIndex][$instrId], $fileStorage);
                }
            }
        }
    }

    private function overrideTrainingRequests(array $trainingRequests): void
    {
        $currentTrainingRequest = reset($trainingRequests);
        $currentTrainingRequest->status = Status::DELETED;
        $currentTrainingRequest->modified_by = self::MODIFIED_BY;
        $currentTrainingRequest->modified_on = date('Y-m-d H:i:s');

        if ($currentTrainingRequest->validate()) {
            $currentTrainingRequest->save();
        }
    }

    private function insertNonExistentTrainingRequests(string $instructionId, string $employeeContractId, string $revision, array $fileStorage): void
    {
        $nonExistentTrainingRequest = $fileStorage[$instructionId . '__' . $revision . '.pdf'];
        [$fileName,] = $this->extractFileName($nonExistentTrainingRequest->file_name);
        [$newId, $newRevision] = $this->extractIdAndRevision($fileName, '__');
        $competencyId = $newId . '--' . $newRevision;

        $newTrainingRequest = new TrainingRequest();
        $newTrainingRequest->setIsNewRecord(true);
        $newTrainingRequest->training_request_id = md5($employeeContractId . $competencyId . $competencyId . $newRevision);
        $newTrainingRequest->employee_contract_id = $employeeContractId;
        $newTrainingRequest->competency_id = $competencyId;
        $newTrainingRequest->training_id = $competencyId;
        $newTrainingRequest->created_by = self::CREATED_BY;
        $newTrainingRequest->created_on = $nonExistentTrainingRequest->file_upload_date;
        $newTrainingRequest->status = Status::DRAFT;

        if ($newTrainingRequest->validate()) {
            $newTrainingRequest->save();
        }
    }

    private function getDraftEmployeeCompetencies(): array
    {
        $criteria = new CDbCriteria();
        $criteria->select = 'DISTINCT competency_id';
        $criteria->condition = '`status` = ' . Status::DRAFT;
        $draftEmployeeCompetencies = EmployeeCompetency::model()->findAll($criteria);
        if (!is_array($draftEmployeeCompetencies)) {
            $draftEmployeeCompetencies = [];
        }

        $indexedDraftEmployeeCompetencies = [];
        foreach ($draftEmployeeCompetencies as $draftEmployeeCompetency) {
            [$instructionId, $revision] = $this->extractIdAndRevision($draftEmployeeCompetency->competency_id, '--');
            $indexedDraftEmployeeCompetencies[$instructionId][$revision] = $draftEmployeeCompetency;
        }

        return $indexedDraftEmployeeCompetencies;
    }

    private function handleDraftEmployeeCompetencies(array $draftEmployeeCompetencies, array $employeeContractIds, array $fileStorage, string $instrId): void
    {
        $currentIndex = false;
        foreach ($this->indexedFileStorage as $key => $fileStorageElement) {
            if (key($fileStorageElement) === $instrId) {
                $currentIndex = $key;
            }
        }

        if ($currentIndex !== false) {
            if (!isset($draftEmployeeCompetencies[$instrId][$this->indexedFileStorage[$currentIndex][$instrId]])) {
                if (isset($draftEmployeeCompetencies[$instrId])) {
                    $this->overrideDraftEmployeeCompetencies($draftEmployeeCompetencies[$instrId]);
                }

                foreach ($employeeContractIds as $employeeContractId) {
                    $this->insertNonExistentDraftEmployeeCompetencies($instrId, $employeeContractId, $this->indexedFileStorage[$currentIndex][$instrId], $fileStorage);
                }
            }
        }
    }

    private function overrideDraftEmployeeCompetencies(array $draftEmployeeCompetencies): void
    {
        $currentDraftEmployeeCompetency = reset($draftEmployeeCompetencies);
        $currentDraftEmployeeCompetency->status = Status::DELETED;
        $currentDraftEmployeeCompetency->modified_by = self::MODIFIED_BY;
        $currentDraftEmployeeCompetency->modified_on = date('Y-m-d H:i:s');

        if ($currentDraftEmployeeCompetency->validate()) {
            $currentDraftEmployeeCompetency->save();
        }
    }

    private function insertNonExistentDraftEmployeeCompetencies(string $instructionId, string $employeeContractId, string $revision, array $fileStorage): void
    {
        $nonExistentDraftEmployeeCompetency = $fileStorage[$instructionId . '__' . $revision . '.pdf'];
        [$fileName,] = $this->extractFileName($nonExistentDraftEmployeeCompetency->file_name);
        [$newId, $newRevision] = $this->extractIdAndRevision($fileName, '__');
        $competencyId = $newId . '--' . $newRevision;

        $newDraftEmployeeCompetency = new EmployeeCompetency();
        $newDraftEmployeeCompetency->setIsNewRecord(true);
        $newDraftEmployeeCompetency->employee_contract_id = $employeeContractId;
        $newDraftEmployeeCompetency->competency_id = $competencyId;
        $newDraftEmployeeCompetency->level_id = 2;
        $newDraftEmployeeCompetency->valid_from = date('Y-m-d H:i:s');
        $newDraftEmployeeCompetency->valid_to = App::getSetting('defaultEnd');
        $newDraftEmployeeCompetency->created_by = self::CREATED_BY;
        $newDraftEmployeeCompetency->created_on = $nonExistentDraftEmployeeCompetency->file_upload_date;
        $newDraftEmployeeCompetency->status = Status::DRAFT;

        if ($newDraftEmployeeCompetency->validate()) {
            $newDraftEmployeeCompetency->save();
        }
    }

    private function deactivateInstructions(): void
    {
        $models = [
            new Training(),
            new TrainingLink(),
            new Competency(),
        ];

        foreach ($models as $model) {
            $criteria = new CDbCriteria();
            $criteria->condition = '`status` = ' . Status::PUBLISHED;
            if ($model instanceof Training) {
                $criteria->addInCondition('training_id', $this->deactivatedInstructions);
            } elseif ($model instanceof TrainingLink) {
                $criteria->addInCondition('competency_id', $this->deactivatedInstructions);
            } elseif ($model instanceof Competency) {
                $criteria->addInCondition('competency_id', $this->deactivatedInstructions);
            }

            $model->updateAll(
                [
                    'status' => Status::DELETED,
                    'modified_by' => self::DEACTIVATED,
                    'modified_on' => date('Y-m-d H:i:s'),
                ],
                $criteria
            );
        }
    }

    private function transformFileStorageByDeactivated(): void
    {
        $tempDeactivatedInstructions = [];
        foreach ($this->indexedFileStorage as $fileKey => $fileName) {
            foreach ($fileName as $instructionId => $revision) {
                if ($revision == self::DEACTIVATED) {
                    if (!in_array($instructionId, $tempDeactivatedInstructions)) {
                        $tempDeactivatedInstructions[] = $instructionId;
                        unset($this->indexedFileStorage[$fileKey]);
                    }
                }

                if (in_array($instructionId, $tempDeactivatedInstructions) && $revision != self::DEACTIVATED) {
                    $this->deactivatedInstructions[] = $instructionId . '--' . $revision;
                    unset($this->indexedFileStorage[$fileKey]);
                }
            }
        }

        $this->indexedFileStorage = array_values($this->indexedFileStorage);
    }

    private function extractFileName(string $file_name): array
    {
        if (preg_match('/__\d+_[^_.]*\.pdf$/', $file_name)) {
            $lastUnderscorePosition = strrpos($file_name, '_');
            $filenameWithRevision = substr($file_name, 0, $lastUnderscorePosition);
            return [$filenameWithRevision . '.pdf', $filenameWithRevision];
        } else {
            return [$file_name, null];
        }
    }

    private function extractShortName(string $fileName): ?string
    {
        $extension = pathinfo($fileName, PATHINFO_EXTENSION);
        if ($extension !== 'pdf') {
            return null;
        }

        $fileNameWithoutExtension = pathinfo($fileName, PATHINFO_FILENAME);
        $lastUnderscorePos = strrpos($fileNameWithoutExtension, '_');

        if ($lastUnderscorePos === false) {
            return null;
        }

        $shortName = substr($fileNameWithoutExtension, $lastUnderscorePos + 1);

        return ctype_alpha($shortName) ? $shortName : null;
    }

    private function getCompetencyGroupDataByShortName(string $shortName, string $column): string
    {
        $competencyGroupData = CompetencyGroup::model()->findByAttributes([
            'competency_group_short_name' => $shortName,
            'status' => $this->publishedStatus
        ]);

        if (!empty($competencyGroupData)) {
            return
                $competencyGroupData->{$column};
        } else {
            return '';
        }
    }

    private function getUnitIdByCompetencyGroupName(string $competencyGroupName): string
    {
        $criteria = new CDbCriteria([
            'condition' => 'unit_name=:competencyGroupName AND status=:status AND CURDATE() BETWEEN valid_from AND IFNULL(valid_to, :defaultEnd)',
            'params' => [
                ':competencyGroupName' => $competencyGroupName,
                ':status' => $this->publishedStatus,
                ':defaultEnd' => App::getSetting('defaultEnd')
            ]
        ]);

        $unitData = Unit::model()->find($criteria);

        if (!empty($unitData)) {
            return $unitData->unit_id;
        } else {
            return '';
        }
    }

    private function getAllEmployeeContractsInUnit(string $unitId): array
    {
        $employeeContracts = [];

        $criteria = new CDbCriteria([
            'condition' => 'group_id="unit_id" AND group_value=:unitId AND status=:status AND CURDATE() BETWEEN valid_from AND IFNULL(valid_to, :defaultEnd)',
            'params' => [
                ':unitId' => $unitId,
                ':status' => $this->publishedStatus,
                ':defaultEnd' => App::getSetting('defaultEnd')
            ]
        ]);

        $employeeContractData = EmployeeGroup::model()->findAll($criteria);

        if (!empty($employeeContractData)) {
            foreach ($employeeContractData as $employeeContract) {
                $employeeContracts[] = $employeeContract->employee_contract_id;
            }
        }

        return $employeeContracts;
    }

    /**
     * @throws Exception
     */
    public function handleResponse(array $errors, array $processedData): void
    {
        $sftp = new SFTP($this->sftpConfig['host'], $this->sftpConfig['port']);
        if (!$sftp->login($this->sftpConfig['user'], $this->sftpConfig['pass'])) {
            throw new Exception('SFTP login failed');
        }

        if (!$sftp->chdir($this->sftpConfig['downloadFromDir'])) {
            throw new Exception('Failed to change directory on SFTP server');
        }

        $this->handleErrors($sftp, $errors);
        $this->deleteProcessedFiles($sftp, $processedData);

        $sftp->disconnect();
    }

    /**
     * @throws Exception
     */
    private function handleErrors(SFTP $sftp, array $errors): void
    {
        if (!empty($errors)) {
            $filename = 'HU-errors_' . date('Y-m-d_H-i-s') . '.txt';
            $memoryStream = fopen('php://memory', 'r+');
            if ($memoryStream === false) {
                throw new Exception('Failed to create memory stream');
            }

            fwrite($memoryStream, implode(PHP_EOL, $errors));
            rewind($memoryStream);

            if (!$sftp->put($filename, $memoryStream)) {
                throw new Exception('Failed to upload file to SFTP server');
            }

            fclose($memoryStream);
        }
    }

    /**
     * @throws Exception
     */
    private function deleteProcessedFiles(SFTP $sftp, array $processedData): void
    {
        if ($this->postProcessingCleanup !== true) {
            return;
        }

        array_walk($this->deactivatedInstructions, function ($instruction) use ($sftp) {
            $filename = $instruction . '__deactivated.txt';
            if (!$sftp->delete($filename)) {
                throw new Exception('Failed to delete file ' . $filename . ' from SFTP server');
            }
        });

        array_walk($processedData, function ($file) use ($sftp) {
            if (!$sftp->delete($file)) {
                throw new Exception('Failed to delete file ' . $file . ' from SFTP server');
            }
        });
    }

}