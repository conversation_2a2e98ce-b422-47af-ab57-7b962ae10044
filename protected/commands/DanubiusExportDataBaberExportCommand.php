<?php

declare(strict_types=1);

include_once('../vendor/flow-php/etl/src/Flow/ETL/DSL/functions.php');

use Components\BaberExport\Descriptor\BaberRawDataDescriptor;
use Components\BaberExport\Descriptor\UploadedFileDescriptor;
use Components\BaberExport\Enum\BaberExportEnum;
use Components\BaberExport\ETL\Extractor\BaberExtractor;
use Components\BaberExport\ETL\Loader\ArrayToExcelLoader;
use Components\BaberExport\ETL\Loader\AssocRowHashesToDBLoader;
use Components\BaberExport\ETL\Loader\DinamicRowHashesToDBLoader;
use Components\BaberExport\ETL\Transformer\DefaultTargetFieldValuesTransformer;
use Components\BaberExport\ETL\Transformer\FillTargetFieldValuesByRulesTransformer;
use Components\BaberExport\ETL\Transformer\ExportableAssocRowsBySentDataFilteringTransformer;
use Components\BaberExport\ETL\Transformer\ExportableDinamicRowsBySentDataFilteringTransformer;
use Components\BaberExport\ETL\Transformer\HashAssocRowEntryTransformer;
use Components\BaberExport\ETL\Transformer\HashDinamicRowEntryTransformer;
use Components\BaberExport\Handler\UploadExportFileHandler;
use Components\Core\Command\AbstractUserSessionCommand;
use Components\ETL\Enum\EtlEnum;
use Components\ETL\Provider\EtlWorkflowConfigProvider;
use Flow\ETL\Cache\InMemoryCache;
use Flow\ETL\Config;
use Flow\ETL\Flow;

$composerAutoloder = include_once("../vendor/autoload.php");
Yang::registerAutoloader([$composerAutoloder, 'loadClass'], true);
Yang::loadComponentNamespaces('BaberExport');
Yang::loadComponentNamespaces('Employee');
Yang::loadComponentNamespaces('Core');
Yang::loadComponentNamespaces('ETL');

final class DanubiusExportDataBaberExportCommand extends AbstractUserSessionCommand
{
    private BaberRawDataDescriptor $baberRawdataDescriptor;
    private EtlWorkflowConfigProvider $etlWorkflowConfigProvider;
    private UploadedFileDescriptor $uploadedFileDescriptor;
    private UploadExportFileHandler $uploadExportFileHandler;

    public function __construct()
    {
        $this->baberRawdataDescriptor = new BaberRawDataDescriptor();
        $this->etlWorkflowConfigProvider = new EtlWorkflowConfigProvider();
        $this->uploadedFileDescriptor = new UploadedFileDescriptor();
    }

    public function actionRun(): int
    {
        $workflow = $this->baberRawdataDescriptor->getWorkFlow();
        $configModel = ($this->etlWorkflowConfigProvider)($workflow, $this->baberRawdataDescriptor->getCurrentDate());
        $config = json_decode($configModel[$workflow]['config'] ?? '{}', true);

        if (
            !isset($configModel[$workflow])
            && !isset($config['SFTP'])
        ) {
            return 0;
        }

        $this->baberRawdataDescriptor->setEtlConfig($configModel[$workflow]);

        $transaction = \Yang::app()->db->beginTransaction();
        try {
            $etlFlowSuccess = $this->runEtlFlow();
            $transaction->commit();

            if ($etlFlowSuccess) {
                $this->uploadToSFTP();
            }
        } catch (\Exception $e) {
            $transaction->rollback();
            return 1;
        }

        return 0;
    }

    private function uploadToSFTP(): void
    {
        try {
            $this->uploadExportFileHandler = new UploadExportFileHandler(
                $this->baberRawdataDescriptor,
                $this->uploadedFileDescriptor
            );
            
            $this->uploadExportFileHandler->handle();
        } catch (\Exception $e) {
        }
    }

    private function runEtlFlow(): bool
    {
        $success = true;
        $config = Config::builder()
            ->cache(new InMemoryCache())
            ->build();

        $processId = \App::getIncreasedGlobalIdentifier(EtlEnum::ETL_PROCESS_ID);
        $this->setProcessStart($processId);

        try {
            Flow::setUp($config)
                ->extract(
                    new BaberExtractor(
                        $this->baberRawdataDescriptor
                    )
                )
                ->transform(new DefaultTargetFieldValuesTransformer())
                ->transform(
                    new FillTargetFieldValuesByRulesTransformer(
                        $this->baberRawdataDescriptor
                    )
                )
                ->transform(
                    new ExportableAssocRowsBySentDataFilteringTransformer(
                        BaberExportEnum::BABER_TARGET_TORZS_FIELDS,
                    )
                )
                ->transform(
                    new HashAssocRowEntryTransformer(
                        BaberExportEnum::ENTRY_TORZS_HASH,
                        BaberExportEnum::BABER_TARGET_TORZS_FIELDS,
                    )
                )
                ->transform(
                    new ExportableDinamicRowsBySentDataFilteringTransformer(
                        BaberExportEnum::BABER_TARGET_ELTARTOTT_FIELDS,
                        BaberExportEnum::ENTRY_ELTARTOTT_EXPORTABLE,

                    )
                )
                ->transform(
                    new HashDinamicRowEntryTransformer(
                        BaberExportEnum::ENTRY_ELTARTOTTAK_HASH,
                        BaberExportEnum::BABER_TARGET_ELTARTOTT_FIELDS,
                    )
                )
                ->write(
                    new AssocRowHashesToDBLoader()
                )
                ->write(
                    new DinamicRowHashesToDBLoader()
                )
                ->write(
                    new ArrayToExcelLoader(
                        $this->baberRawdataDescriptor,
                        $this->uploadedFileDescriptor
                    )
                )
                ->run();

            $this->setProcessStop($processId, EtlEnum::ETL_PROCESS_STATUS_SUCCES);
        } catch (\Exception $e) {
            $this->setProcessStop($processId, EtlEnum::ETL_PROCESS_STATUS_FAILED);
            $success = false;
        }

        return $success;
    }


    private function setProcessStart(int $processId, array $metaData = []): void
    {
        $model = new \EtlProcess();
        $model->process_id = $processId;
        $model->workflow = BaberExportEnum::WORKFLOW_DATA_EXPORT;
        $model->process_status = EtlEnum::ETL_PROCESS_STATUS_IN_PROGRESS;
        $model->process_start_datetime = (new \Datetime(EtlEnum::ETL_PROCESS_DATETIME))->format(EtlEnum::ETL_PROCESS_TABLE_DATETIME_FORMAT);
        $model->status = \Status::PUBLISHED;
        $model->created_by = EtlEnum::CREATED_BY;
        $model->created_on = (new \Datetime(EtlEnum::CREATED_ON))->format(EtlEnum::ETL_PROCESS_TABLE_DATETIME_FORMAT);
        $model->save();
    }
    private function setProcessStop(int $processId, string $processStatus): void
    {
        \EtlProcess::model()->updateAll(
            [
                EtlEnum::ETL_PROCESS_TABLE_PROCESS_STATUS => $processStatus,
                EtlENum::ETL_PROCESS_TABLE_PROCESS_STOP_DATETIME => (new \Datetime(EtlEnum::ETL_PROCESS_DATETIME))->format(EtlEnum::ETL_PROCESS_TABLE_DATETIME_FORMAT)
            ],
            "`" . EtlEnum::ETL_PROCESS_TABLE_PROCESS_ID . "`=$processId 
            AND  `" . EtlEnum::ETL_PROCESS_TABLE_PROCESS_WORKFLOW . "`='" . BaberExportEnum::WORKFLOW_DATA_EXPORT . "'
            AND `" . EtlEnum::ETL_PROCESS_TABLE_STATUS . "`=" . \Status::PUBLISHED
        );
    }
}
