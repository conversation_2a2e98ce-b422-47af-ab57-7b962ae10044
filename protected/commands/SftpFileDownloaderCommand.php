<?php

declare(strict_types=1);

use Components\Core\Command\AbstractUserSessionCommand;
use phpseclib3\Net\SFTP;

$composerAutoloder = include_once('../vendor/autoload.php');
Yang::registerAutoloader([$composerAutoloder, 'loadClass'], true);

Yang::loadComponentNamespaces('Core');

final class SftpFileDownloaderCommand extends AbstractUserSessionCommand
{
    private array $fileStorage;
    private array $sftpConfig;
    private array $allowedExtensions = [];
    private array $fileNameContains = [];

    public function __construct()
    {
        $this->sftpConfig = Yang::getParam('additionalComponents')['SFTPFileDownloaderConfig'];
    }

    public function actionRun(string $allowedExtensions = null, string $fileNameContains = null): int
    {
        if (!is_null($allowedExtensions)) {
            $this->allowedExtensions = explode(',', $allowedExtensions);
        }

        if (!is_null($fileNameContains)) {
            $this->fileNameContains = explode(',', $fileNameContains);
        }

        $transaction = Yang::app()->db->beginTransaction();
        try {
            $this->fetchFileStorage();
            $this->downloadFiles();
            $transaction->commit();

            return 0;
        } catch (Exception $e) {
            $transaction->rollback();
            Yang::log($e->getMessage());
            return 0;
        }
    }

    /**
     * @throws Exception
     */
    private function downloadFiles(): void
    {
        $sftp = new SFTP($this->sftpConfig['host'], $this->sftpConfig['port']);
        if (!$sftp->login($this->sftpConfig['user'], $this->sftpConfig['pass'])) {
            throw new Exception('SFTP login failed');
        }

        if (!$sftp->chdir($this->sftpConfig['downloadFromDir'])) {
            throw new Exception('Failed to change directory on SFTP server');
        }

        $localDownloadPath = Yang::getBasePath() . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'webroot' . DIRECTORY_SEPARATOR . $this->sftpConfig['localDownloadPath'];

        if (!is_dir($localDownloadPath)) {
            mkdir($localDownloadPath, 0755, true);
        }

        $files = $sftp->nlist();
        foreach ($files as $file) {
            if (is_dir($file) || in_array($file, array_keys($this->fileStorage))) {
                continue;
            }

            $extension = pathinfo($file, PATHINFO_EXTENSION);
            if (!empty($this->allowedExtensions) && !in_array($extension, $this->allowedExtensions)) {
                continue;
            }

            if (!empty($this->fileNameContains) && empty(array_filter($this->fileNameContains, fn($keyword) => strpos($file, $keyword) !== false))) {
                continue;
            }

            $localFilePath = $localDownloadPath . DIRECTORY_SEPARATOR . basename($file);
            $sftp->get($file, $localFilePath);

            $fs = new FS();
            $fs->setCustomFsDir($localDownloadPath);
            $fs->setCustomFsUrl($this->sftpConfig['localDownloadPath']);
            $fs->disableMySQLStore();
            $fs->setFileGroupID($this->sftpConfig['fileGroupId']);
            $fs->uploadFile($localFilePath);
        }

        $sftp->disconnect();
    }

    private function fetchFileStorage(): void
    {
        $this->fileStorage = $this->fetchFileStorageFromDatabase();
    }

    private function fetchFileStorageFromDatabase(): array
    {
        $criteria = new CDbCriteria();
        $fileGroupId = $this->sftpConfig['fileGroupId'];
        $criteria->condition = "`file_group_id` = '$fileGroupId'";
        $fileStorage = FileStorage::model()->findAll($criteria);
        if (!is_array($fileStorage)) {
            $fileStorage = [];
        }

        $indexedFileStorage = [];
        foreach ($fileStorage as $file) {
            $indexedFileStorage[$file->file_name] = $file;
        }

        return $indexedFileStorage;
    }

}

