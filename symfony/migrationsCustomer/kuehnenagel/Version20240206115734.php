<?php

declare(strict_types=1);

namespace KuehneNagel;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240206115734 extends AbstractMigration
{
    public const REQUEST_MAP = [
        '_api_/company-org-group1_post' => ['company-org-group1-create', 'companyMainData'],
        '_api_/company-org-group1/{id}_delete' => ['company-org-group1-delete', 'companyMainData'],
        '_api_/company-org-group1_get_collection' => ['company-org-group1-list', 'companyMainData'],
        '_api_/company-org-group1-autocomplete_get_collection' => ['company-org-group1-autocomplete', 'companyMainData'],
        '_api_/job-task-autocomplete_get_collection' => ['job-task-autocomplete', 'competency'],
        '_api_/employee-autocomplete_get_collection' => ['employee-autocomplete', 'competency'],
        '_api_/employee/{id}_get' => ['employee-item', 'competency'],
        '_api_/competency_get_collection' => ['competency-list', 'competency'],
        '_api_/competency/{id}_get' => ['competency-item', 'competency'],
        '_api_/competency-autocomplete_get_collection' => ['competency-autocomplete', 'competency'],
        '_api_/training-event_get_collection' => ['training-event-list', 'competency'],
        '_api_/training-event/{id}_get' => ['training-event-item', 'competency'],
        '_api_/training-event-lock_post' => ['training-event-lock', 'competency'],
        '_api_/training-event-autocomplete_get_collection' => ['training-event-autocomplete', 'competency'],
        '_api_/training-event_post' => ['training-event-create', 'competency'],
        '_api_/training-event/{id}_put' => ['training-event-update', 'competency'],
        '_api_/simplified-training-wizard_post' => ['simplified-training-wizard-create', 'competency'],
        '_api_/simplified-training-wizard_put' => ['simplified-training-wizard-update', 'competency'],
        '_api_/employee-training_get_collection' => ['employee-training-list', 'competency'],
        '_api_/employee-training/{id}_get' => ['employee-training-item', 'competency'],
        '_api_/employee-training/{id}_patch' => ['employee-training-set-status', 'competency'],
        '_api_/group-hierarchy_get_collection' => ['group-hierarchy', 'competency'],
        'group-hierarchy-list' => ['group-hierarchy', 'competency'],
        'competency-hierarchy-autocomplete' => ['competency-hierarchy-autocomplete', 'competency'],
        'employee-hierarchy-autocomplete' => ['employee-hierarchy-autocomplete', 'competency'],
    ];

    public function getDescription(): string
    {
        return 'Default request permissions for Simplified training wizard';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(
            "INSERT INTO auth_rolegroup_to_operation_group (rolegroup_id, auth_operation_group_id, pre_row_id, status, created_by, created_on, modified_by, modified_on) VALUES ('a5b6bd79e008725744118c7c46e10cda', 1, null, 2, 'system', '2024-01-06 17:00:00', null, null);");
        $this->addSql(
            "INSERT INTO auth_rolegroup_to_operation_group (rolegroup_id, auth_operation_group_id, pre_row_id, status, created_by, created_on, modified_by, modified_on) VALUES ('41401acf25cb82055b99b65a7901a5f2', 1, null, 2, 'system', '2024-01-06 17:00:00', null, null);");

        foreach (self::REQUEST_MAP as $operationId => [$requestName, $processIds]) {
            $this->addSql(
                "INSERT INTO request_to_operation 
                        (request_name, operation_id, process_ids, status, created_by, created_on) 
                      VALUES (
                              '{$requestName}',
                              '{$operationId}',
                              '{$processIds}',
                              2,
                              'system',
                              '2024-02-06 17:00:00'
                              );"
            );
            $this->addSql("INSERT INTO 
                    auth_operation_group_to_operation
                    (auth_operation_group_id, operation_id, status, created_by, created_on) 
                    VALUES (
                            1,
                            '{$operationId}',
                            2,
                            'system',
                            '2024-02-06 17:00:00'
                    );"
            );
        }
    }

    public function down(Schema $schema): void
    {
        $this->addSql("DELETE FROM auth_rolegroup_to_operation_group WHERE rolegroup_id='a5b6bd79e008725744118c7c46e10cda' AND auth_operation_group_id=1");
        $this->addSql("DELETE FROM auth_rolegroup_to_operation_group WHERE rolegroup_id='41401acf25cb82055b99b65a7901a5f2' AND auth_operation_group_id=1");
        foreach (self::REQUEST_MAP as $operationId => [$requestName, $processIds]) {
            $this->addSql("DELETE FROM request_to_operation 
                    WHERE request_name='{$requestName}' AND operation_id='{$operationId}'");
            $this->addSql("DELETE FROM auth_operation_group_to_operation
                    WHERE auth_operation_group_id='1' AND operation_id='{$operationId}'");
        }
    }
}
