<?php

declare(strict_types=1);

namespace LoginAutonom\EmployeeBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use LoginAutonom\CompanyBundle\Entity\Workgroup;
use LoginAutonom\CompanyBundle\Enum\WorkgroupEntityFieldEnum;
use LoginAutonom\DatabaseBundle\Attribute\EntityAssociation;
use LoginAutonom\DatabaseBundle\Attribute\EntityIdentifier;
use LoginAutonom\DatabaseBundle\Attribute\StatusColumn;
use LoginAutonom\DatabaseBundle\Attribute\ValidityColumns;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\HistoryFieldsEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\NoteEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\RowIdEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\StatusEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\ValidityEmbeddable;
use LoginAutonom\EmployeeBundle\Enum\EmployeeAbsenceEntityFieldEnum;
use LoginAutonom\EmployeeBundle\Enum\EmployeeCardEntityFieldEnum;
use LoginAutonom\EmployeeBundle\Enum\EmployeeCompetencyEntityFieldEnum;
use LoginAutonom\EmployeeBundle\Enum\EmployeeContractEntityFieldEnum;
use LoginAutonom\EmployeeBundle\Enum\EmployeeEntityFieldEnum;
use LoginAutonom\EmployeeBundle\Enum\EmployeeExtraHoursEntityFieldEnum;
use LoginAutonom\EmployeeBundle\Enum\EmployeePositionEntityFieldEnum;

#[ORM\Entity]
#[StatusColumn]
#[ValidityColumns]
#[ORM\Index(columns: [
    'employee_contract_id',
    'valid_from',
    'valid_to',
    'status',
], name: 'IDX_contract_id')]
#[ORM\Index(columns: [
    'employee_contract_id',
    'valid_from',
    'valid_to',
    'ec_valid_from',
    'ec_valid_to',
    'status',
], name: 'IDX_contract_id_valid_ec_valid_status')]
#[ORM\Index(columns: [
    'workgroup_id',
    'status',
    'valid_from',
    'valid_to',
    'ec_valid_from',
    'ec_valid_to',
], name: 'IDX_workgroup_valid_state')]
#[ORM\Index(columns: [
    'valid_from',
    'valid_to',
    'ec_valid_from',
    'ec_valid_to',
    'status',
], name: 'IDX_valid_ec_valid_status')]
#[ORM\Index(columns: [
    'employee_id',
    'valid_from',
    'valid_to',
    'ec_valid_from',
    'ec_valid_to',
    'status',
], name: 'IDX_employee_id_vf_vt_ec_vf_ec_vt_status')]
#[ORM\Index(columns: [
    'employee_id',
    'employee_contract_number',
    'valid_from',
    'valid_to',
    'status',
], name: 'IDX_contract_number')]
#[ORM\Index(columns: [
    'employee_id',
    'employee_contract_id',
    'valid_from',
    'valid_to',
    'ec_valid_from',
    'ec_valid_to',
    'status',
], name: 'IDX_employee_id_employee_contract_id_vf_vt_ec_vf_ec_vt_status')]
#[ORM\Index(columns: [
    'employee_contract_id',
], name: 'IDX_employee_contract_id')]
#[EntityIdentifier(EmployeeContractEntityFieldEnum::EMPLOYEE_CONTRACT_ID)]
final class EmployeeContract
{
    #[ORM\Embedded(class: RowIdEmbeddable::class, columnPrefix: false)]
    private RowIdEmbeddable $row;

    #[ORM\Embedded(class: NoteEmbeddable::class, columnPrefix: false)]
    private NoteEmbeddable $note;

    #[ORM\Embedded(class: StatusEmbeddable::class, columnPrefix: false)]
    private StatusEmbeddable $status;

    #[ORM\Embedded(class: HistoryFieldsEmbeddable::class, columnPrefix: false)]
    private HistoryFieldsEmbeddable $history;

    #[ORM\Embedded(class: ValidityEmbeddable::class, columnPrefix: false)]
    private ValidityEmbeddable $validity;

    #[ORM\Column(type: 'string', length: 32)]
    #[EntityAssociation(
        targetEntity: EmployeeCompetency::class,
        targetField: EmployeeCompetencyEntityFieldEnum::EMPLOYEE_CONTRACT_ID
    )]
    #[EntityAssociation(
        targetEntity: EmployeeAbsence::class,
        targetField: EmployeeAbsenceEntityFieldEnum::EMPLOYEE_CONTRACT_ID
    )]
    #[EntityAssociation(
        targetEntity: EmployeeExtraHours::class,
        targetField: EmployeeExtraHoursEntityFieldEnum::EMPLOYEE_CONTRACT_ID
    )]
    #[EntityAssociation(
        targetEntity: EmployeeCard::class,
        targetField: EmployeeCardEntityFieldEnum::EMPLOYEE_CONTRACT_ID
    )]
    private string $employeeContractId;

    #[ORM\Column(type: 'string', length: 32)]
    #[EntityAssociation(targetEntity: Employee::class, targetField: EmployeeEntityFieldEnum::EMPLOYEE_ID)]
    private string $employeeId;

    #[ORM\Column(type: 'string', length: 32)]
    private string $employeeContractNumber;

    #[ORM\Column(type: 'date')]
    private \DateTime $ecValidFrom;

    #[ORM\Column(type: 'date', nullable: true)]
    private ?\DateTime $ecValidTo;

    #[ORM\Column(type: 'integer', nullable: true)]
    private ?int $employeeContractType;

    #[ORM\Column(type: 'string', length: 64, options: ['default' => 'MoLo'])]
    private string $wageType = 'MoLo';

    #[ORM\Column(type: 'string', length: 32, nullable: true)]
    #[EntityAssociation(
        targetEntity: Workgroup::class,
        targetField: WorkgroupEntityFieldEnum::WORKGROUP_ID
    )]
    private ?string $workgroupId;

    #[ORM\Column(type: 'string', length: 32, nullable: true)]
    #[EntityAssociation(
        targetEntity: EmployeePosition::class,
        targetField: EmployeePositionEntityFieldEnum::EMPLOYEE_POSITION_ID
    )]
    private ?string $employeePositionId;

    #[ORM\Column(type: 'float', options: ['default' => '8'])]
    private float $dailyWorktime = 0;

    #[ORM\Column(type: 'string', length: 250, nullable: true)]
    private ?string $ecEndReason;

    #[ORM\Column(type: 'string', length: 250, nullable: true)]
    private ?string $ecEndType;

    public function __construct()
    {
        $this->row = new RowIdEmbeddable();
        $this->note = new NoteEmbeddable();
        $this->status = new StatusEmbeddable();
        $this->history = new HistoryFieldsEmbeddable();
        $this->validity = new ValidityEmbeddable();
    }

    public function getRow(): RowIdEmbeddable
    {
        return $this->row;
    }

    public function setRow(RowIdEmbeddable $row): void
    {
        $this->row = $row;
    }

    public function getNote(): NoteEmbeddable
    {
        return $this->note;
    }

    public function setNote(NoteEmbeddable $note): void
    {
        $this->note = $note;
    }

    public function getStatus(): StatusEmbeddable
    {
        return $this->status;
    }

    public function setStatus(StatusEmbeddable $status): void
    {
        $this->status = $status;
    }

    public function getHistory(): HistoryFieldsEmbeddable
    {
        return $this->history;
    }

    public function setHistory(HistoryFieldsEmbeddable $history): void
    {
        $this->history = $history;
    }

    public function getValidity(): ValidityEmbeddable
    {
        return $this->validity;
    }

    public function setValidity(ValidityEmbeddable $validity): void
    {
        $this->validity = $validity;
    }

    public function getEmployeeContractId(): string
    {
        return $this->employeeContractId;
    }

    public function setEmployeeContractId(string $employeeContractId): void
    {
        $this->employeeContractId = $employeeContractId;
    }

    public function hasEmployeeContractId(): bool
    {
        return isset($this->employeeContractId);
    }

    public function getEmployeeId(): string
    {
        return $this->employeeId;
    }

    public function setEmployeeId(string $employeeId): void
    {
        $this->employeeId = $employeeId;
    }

    public function hasEmployeeId(): bool
    {
        return isset($this->employeeId);
    }

    public function getEmployeeContractNumber(): string
    {
        return $this->employeeContractNumber;
    }

    public function setEmployeeContractNumber(string $employeeContractNumber): void
    {
        $this->employeeContractNumber = $employeeContractNumber;
    }

    public function hasEmployeeContractNumber(): bool
    {
        return isset($this->employeeContractNumber);
    }

    public function getEcValidFrom(): \DateTime
    {
        return $this->ecValidFrom;
    }

    public function setEcValidFrom(\DateTime $ecValidFrom): void
    {
        $this->ecValidFrom = $ecValidFrom;
    }

    public function hasEcValidFrom(): bool
    {
        return isset($this->ecValidFrom);
    }

    public function getEcValidTo(): ?\DateTime
    {
        return $this->ecValidTo;
    }

    public function setEcValidTo(?\DateTime $ecValidTo): void
    {
        $this->ecValidTo = $ecValidTo;
    }

    public function hasEcValidTo(): bool
    {
        return isset($this->ecValidTo);
    }

    public function getEmployeeContractType(): ?int
    {
        return $this->employeeContractType;
    }

    public function setEmployeeContractType(?int $employeeContractType): void
    {
        $this->employeeContractType = $employeeContractType;
    }

    public function hasEmployeeContractType(): bool
    {
        return isset($this->employeeContractType);
    }

    public function getWageType(): string
    {
        return $this->wageType;
    }

    public function setWageType(string $wageType): void
    {
        $this->wageType = $wageType;
    }

    public function hasWageType(): bool
    {
        return isset($this->wageType);
    }

    public function getWorkgroupId(): ?string
    {
        return $this->workgroupId;
    }

    public function setWorkgroupId(?string $workgroupId): void
    {
        $this->workgroupId = $workgroupId;
    }

    public function hasWorkgroupId(): bool
    {
        return isset($this->workgroupId);
    }

    public function getEmployeePositionId(): ?string
    {
        return $this->employeePositionId;
    }

    public function setEmployeePositionId(?string $employeePositionId): void
    {
        $this->employeePositionId = $employeePositionId;
    }

    public function hasEmployeePositionId(): bool
    {
        return isset($this->employeePositionId);
    }

    public function getDailyWorktime(): float
    {
        return $this->dailyWorktime;
    }

    public function setDailyWorktime(float $dailyWorktime): void
    {
        $this->dailyWorktime = $dailyWorktime;
    }

    public function hasDailyWorktime(): bool
    {
        return isset($this->dailyWorktime);
    }

    public function getEcEndReason(): ?string
    {
        return $this->ecEndReason;
    }

    public function setEcEndReason(?string $ecEndReason): void
    {
        $this->ecEndReason = $ecEndReason;
    }

    public function hasEcEndReason(): bool
    {
        return isset($this->ecEndReason);
    }

    public function getEcEndType(): ?string
    {
        return $this->ecEndType;
    }

    public function setEcEndType(?string $ecEndType): void
    {
        $this->ecEndType = $ecEndType;
    }

    public function hasEcEndType(): bool
    {
        return isset($this->ecEndType);
    }
}
