<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Wrapper;

use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use LoginAutonom\EtlBundle\DTO\RowsValidationDTO;
use LoginAutonom\EtlBundle\Enum\EtlCommonFieldNamesEnum;
use LoginAutonom\EtlBundle\Interfaces\FlowOneRowTransformerInterface;
use LoginAutonom\EtlBundle\Interfaces\FlowPreProcessTransformerInterface;
use LoginAutonom\EtlBundle\Interfaces\FlowTransformerInterface;
use LoginAutonom\EtlBundle\Interfaces\ValidityCheckTransformerInterface;
use LoginAutonom\EtlBundle\Trait\EtlCommonFunctionsTrait;
use LoginAutonom\EtlBundle\Trait\EtlSerializableTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class TransformerWrapper implements
    Transformer,
    LoggerAwareInterface
{
    use EtlSerializableTrait;
    use EtlCommonFunctionsTrait;
    use LoggerAwareTrait;


    public function __construct(
        private FlowTransformerInterface $transformer,
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $this->logger->info(
            sprintf(
                'Executing transformer: %s (Step: %d). Rows number: %d',
                get_class($this->transformer),
                $this->transformer->stepNumber(),
                count($rows)
            )
        );
        if ($this->transformer instanceof FlowOneRowTransformerInterface) {
            $rows = $this->handleOneRowTransformer($rows, $context);
        } elseif ($this->transformer instanceof FlowPreProcessTransformerInterface) {
            $rows = $this->handlePreProcessTransformer($rows, $context);
        } elseif ($this->transformer instanceof ValidityCheckTransformerInterface) {
            $rows = $this->transformer->validate(new RowsValidationDTO($rows));
        }
        $this->logger->info('Finished transformer: ' . get_class($this->transformer));

        return $rows;
    }

    private function handleOneRowTransformer(Rows $rows, FlowContext $context): Rows
    {
        return $rows->map(function (Row $row) use ($context) {
            $identifier = $this->getIdentifierByRow($row);
            if ($this->isRowFinal($row)) {
                $this->logger->debug("Skipping {$identifier} row, because is final");
                return $row;
            }
            $this->logger->debug("Transforming row: {$identifier}");
            try {
                $newRow = $this->transformer->transform($row, $context);
            } catch (\Exception $e) {
                $this->logger->error("Failed to transform row: {$e->getMessage()}", [
                    'row' => $row->toArray(),
                    EtlCommonFieldNamesEnum::LINE_IDENTIFIER => $identifier,
                    EtlCommonFieldNamesEnum::CLASSNAME => get_class($this->transformer)
                ]);
                $newRow = $row;
            }
            $this->logger->debug("Transforming finished: {$identifier}");

            return $newRow;
        });
    }

    private function handlePreProcessTransformer(Rows $rows, FlowContext $context): Rows
    {
        /** @var FlowPreProcessTransformerInterface $ransformer */
        $transformer = $this->transformer;
        $info = new \ArrayObject();
        $this->logger->info("Executing information collecting stage.");
        $rows->each(function (Row $row) use ($transformer, $context, $info) {
            $identifier = $this->getIdentifierByRow($row);
            if ($this->isRowFinal($row)) {
                $this->logger->debug("Skipping {$identifier}. row, because is final");
                return;
            }
            $this->logger->debug("Collect information from row: {$identifier}");
            try {
                $transformer->collectInformation($row, $context, $info);
            } catch (\Exception $e) {
                $this->logger->error("Failed to collect information from row! Message: {$e->getMessage()}", [
                    'row' => $row->toArray(),
                    EtlCommonFieldNamesEnum::LINE_IDENTIFIER => $identifier,
                ]);
            }
            $this->logger->debug("Information collected: {$identifier}");
        });

        $this->logger->info("Executing action stage.");
        $transformer->executeAction($info, $context);

        $this->logger->info("Executing transforming stage.");
        return $rows->map(function (Row $row) use ($transformer, $context, $info) {
            $identifier = $this->getIdentifierByRow($row);
            if ($this->isRowFinal($row)) {
                $this->logger->debug("Skipping {$identifier}. row, because is final");
                return $row;
            }
            $this->logger->debug("Transforming row: {$identifier}");
            try {
                $newRow = $transformer->buildRow($row, $context, $info);
            } catch (\Exception $e) {
                $this->logger->error("Failed to build row! Message {$e->getMessage()}", [
                    'row' => $row->toArray(),
                    EtlCommonFieldNamesEnum::LINE_IDENTIFIER => $identifier,
                ]);
                $newRow = $row;
            }
            $this->logger->debug("Transformation finished: {$identifier}");
            return $newRow;
        });
    }
}
