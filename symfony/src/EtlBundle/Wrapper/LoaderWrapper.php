<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Wrapper;

use Flow\ETL\FlowContext;
use Flow\ETL\Loader;
use Flow\ETL\Rows;
use LoginAutonom\CoreBundle\Handler\InMemoryCacheClearingHandler;
use LoginAutonom\CoreBundle\Interfaces\CommandBusInterface;
use LoginAutonom\CoreBundle\Util\DebugUtils;
use LoginAutonom\DatabaseBundle\Handler\EntityPersistRequestHandler;
use LoginAutonom\DatabaseBundle\Handler\TransactionHandler;
use LoginAutonom\EtlBundle\Exception\LoaderException;
use LoginAutonom\EtlBundle\Handler\EtlLogPersistingHandler;
use LoginAutonom\EtlBundle\Interfaces\EtlCacheAwareInterface;
use LoginAutonom\EtlBundle\Interfaces\EtlProcessAwareInterface;
use LoginAutonom\EtlBundle\Trait\EtlCacheAwareTrait;
use LoginAutonom\EtlBundle\Trait\EtlProcessAwareTrait;
use LoginAutonom\EtlBundle\Trait\EtlSerializableTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class LoaderWrapper implements
    Loader,
    LoggerAwareInterface,
    EtlCacheAwareInterface,
    EtlProcessAwareInterface
{
    use EtlSerializableTrait;
    use LoggerAwareTrait;
    use EtlCacheAwareTrait;
    use EtlProcessAwareTrait;

    private int $loopNumber = 0;

    public function __construct(
        private Loader $inner,
        private EntityPersistRequestHandler $entityPersistRequestHandler,
        private CommandBusInterface $commandBus,
        private InMemoryCacheClearingHandler $cacheClearingHandler,
        private EtlLogPersistingHandler $etlLogPersistingHandler,
        private TransactionHandler $transactionHandler,
    ) {
    }

    public function load(Rows $rows, FlowContext $context): void
    {
        $this->loopNumber++;
        $this->logger->info("Execute loader: " . get_class($this->inner) . " with rows: " . $rows->count());
        DebugUtils::writeProfile('loader_before_load_' . $this->loopNumber);
        try {
            $this->inner->load($rows, $context);
        } catch (\Throwable $e) {
            $this->logger->error(
                $e->getMessage(),
                [
                    'loader' => get_class($this->inner),
                ]
            );
            $this->persistLogs();
            throw new LoaderException(
                previous: $e
            );
        }
        $this->logger->info("Finished loader: " . get_class($this->inner));
        $this->logger->info("Persist logs: " . get_class($this->inner));
        $this->persistLogs();
        $this->logger->info("Removing cache...");
        DebugUtils::writeProfile('loader_before_cache_clear_' . $this->loopNumber);
        $this->cacheClearingHandler->handle();
        $this->cache()->reset();
        gc_collect_cycles();
        DebugUtils::writeProfile('loader_after_cache_clear_' . $this->loopNumber);
        $this->logger->info("Cache removed successfully.");
    }

    private function persistLogs(): void
    {
        $this->etlLogPersistingHandler->handle($this->logger, $this->process);
        $this->transactionHandler->commitTransaction();
        $this->transactionHandler->startTransaction();
    }
}
