<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use LoginAutonom\DatabaseBundle\Attribute\NoVisibilityPermission;
use LoginAutonom\DatabaseBundle\Attribute\StatusColumn;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\HistoryFieldsEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\RowIdEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\StatusEmbeddable;

#[ORM\Entity]
#[StatusColumn]
#[NoVisibilityPermission]
#[ORM\Index(columns: [
    'workflow',
    'status',
], name: 'IDX_workflow_status')]
final class EtlMapping
{
    #[ORM\Embedded(class: RowIdEmbeddable::class, columnPrefix: false)]
    private RowIdEmbeddable $row;

    #[ORM\Embedded(class: StatusEmbeddable::class, columnPrefix: false)]
    private StatusEmbeddable $status;

    #[ORM\Embedded(class: HistoryFieldsEmbeddable::class, columnPrefix: false)]
    private HistoryFieldsEmbeddable $history;

    #[ORM\Column(type: 'string', length: 255)]
    private string $workflow;

    #[ORM\Column(type: 'string', length: 255)]
    private string $mappingType;

    #[ORM\Column(type: 'json')]
    private array $sourceIdentifier;

    #[ORM\Column(type: 'json')]
    private array $targetIdentifier;

    public function __construct()
    {
        $this->row = new RowIdEmbeddable();
        $this->status = new StatusEmbeddable();
        $this->history = new HistoryFieldsEmbeddable();
    }

    public function getRow(): RowIdEmbeddable
    {
        return $this->row;
    }

    public function hasRow(): bool
    {
        return isset($this->row);
    }

    public function setRow(RowIdEmbeddable $row): void
    {
        $this->row = $row;
    }

    public function getStatus(): StatusEmbeddable
    {
        return $this->status;
    }

    public function hasStatus(): bool
    {
        return isset($this->status);
    }

    public function setStatus(StatusEmbeddable $status): void
    {
        $this->status = $status;
    }

    public function getHistory(): HistoryFieldsEmbeddable
    {
        return $this->history;
    }

    public function hasHistory(): bool
    {
        return isset($this->history);
    }

    public function setHistory(HistoryFieldsEmbeddable $history): void
    {
        $this->history = $history;
    }

    public function getWorkflow(): string
    {
        return $this->workflow;
    }

    public function hasWorkflow(): bool
    {
        return isset($this->workflow);
    }

    public function setWorkflow(string $workflow): void
    {
        $this->workflow = $workflow;
    }

    public function getSourceIdentifier(): array
    {
        return $this->sourceIdentifier;
    }

    public function hasSourceIdentifier(): bool
    {
        return isset($this->sourceIdentifier);
    }

    public function setSourceIdentifier(array $sourceIdentifier): void
    {
        $this->sourceIdentifier = $sourceIdentifier;
    }

    public function getTargetIdentifier(): array
    {
        return $this->targetIdentifier;
    }

    public function hasTargetIdentifier(): bool
    {
        return isset($this->targetIdentifier);
    }

    public function setTargetIdentifier(array $targetIdentifier): void
    {
        $this->targetIdentifier = $targetIdentifier;
    }

    public function getMappingType(): string
    {
        return $this->mappingType;
    }

    public function hasMappingType(): bool
    {
        return isset($this->mappingType);
    }

    public function setMappingType(string $mappingType): void
    {
        $this->mappingType = $mappingType;
    }
}
