<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\DTO;

use Flow\ETL\Row;
use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\DatabaseBundle\DTO\ObjectsChanges;
use LoginAutonom\DatabaseBundle\DTO\ValidityIntervalBasedEntity;
use LoginAutonom\DatabaseBundle\Generator\EntityValueArrayCacheKeyGenerator;
use LoginAutonom\DatabaseBundle\Guesser\EntityIdentifiersGuesser;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityBasedObjectsInterface;
use LoginAutonom\EtlBundle\Entity\EtlMapping;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;

final class EtlValidityBasedEntityCacheWithMapping
{
    private EtlMappingIdentifiersCache $mappingCache;
    /**
     * @var ValidityBasedObjectsInterface[]
     */
    private array $validityBasedObjects = [];
    private array $sourceMappings = [];
    private array $targetMappings = [];
    private array $mappingTypes = [];
    private array $createEtlMappingEntity = [];
    private array $sourceMappingsByHash = [];
    private array $targetMappingsByHash = [];
    private array $entityIdentifierByHash = [];
    private array $identifiersByHash = [];

    public function __construct(
        private readonly EntityValueArrayCacheKeyGenerator $entityValueArrayCacheKeyGenerator,
        private readonly PropertyAccessorInterface $propertyAccessor,
        private readonly EntityIdentifiersGuesser $entityIdentifiersGuesser,
    ) {
        $this->mappingCache = new EtlMappingIdentifiersCache();
    }

    public function addValidityBasedObject(string $entityClass, ValidityBasedObjectsInterface $objects): void
    {
        $this->validityBasedObjects[$entityClass] = $objects;
    }

    public function getValidityBasedObject(string $entityClass): ValidityBasedObjectsInterface
    {
        if (!$this->hasValidityBasedObject($entityClass)) {
            throw new NotFoundException($this->validityBasedObjects, $entityClass);
        }

        return $this->validityBasedObjects[$entityClass];
    }

    public function hasValidityBasedObject(string $entityClass): bool
    {
        return array_key_exists($entityClass, $this->validityBasedObjects);
    }

    public function addSourceMapping(string $entityClass, array $sourceMapping): void
    {
        $this->sourceMappings[$entityClass] = $sourceMapping;
    }

    public function getSourceMapping(string $mappingType): array
    {
        $entityClass = array_search($mappingType, $this->mappingTypes, true);
        return $this->sourceMappings[$entityClass];
    }

    public function addTargetMapping(string $entityClass, array $targetMapping): void
    {
        $this->targetMappings[$entityClass] = $targetMapping;
    }

    public function addCreateEtlMappingEntity(string $entityClass, bool $create): void
    {
        $this->createEtlMappingEntity[$entityClass] = $create;
    }

    public function isCreateEtlMappingEntity(string $entityClass): bool
    {
        return array_key_exists($entityClass, $this->createEtlMappingEntity) &&
            $this->createEtlMappingEntity[$entityClass] === true;
    }

    public function getTargetMapping(string $mappingType): array
    {
        $entityClass = array_search($mappingType, $this->mappingTypes, true);
        return $this->targetMappings[$entityClass];
    }

    public function addMappingType(string $entityClass, string $mappingType): void
    {
        $this->mappingTypes[$entityClass] = $mappingType;
    }

    public function getMappingType(string $entityClass): string
    {
        return $this->mappingTypes[$entityClass];
    }

    public function hasMappingType(string $mappingType): bool
    {
        return in_array($mappingType, $this->mappingTypes, true);
    }

    public function findMapping(array $mapping): EtlMapping
    {
        return $this->mappingCache->find($mapping);
    }

    public function clear(): void
    {
        $this->mappingCache->clear();
        foreach ($this->validityBasedObjects as $validityBasedObject) {
            $validityBasedObject->clear();
        }
    }

    public function generateMapping(object $entity, array $identifiers = []): void
    {
        $entityClass = get_class($entity);
        $hash = $this->createHash($entity);
        if ($identifiers !== []) {
            $this->identifiersByHash[$hash] = $identifiers;
        }
        $this->entityIdentifierByHash[$hash] = $this->entityIdentifiersGuesser->guess($entity);
        if (
            !array_key_exists($entityClass, $this->sourceMappings) ||
            !array_key_exists($entityClass, $this->targetMappings) ||
            !array_key_exists($entityClass, $this->mappingTypes)
        ) {
            return;
        }
        $sourceMapping = $this->generateSourceMapping($entity);
        $targetMapping = $this->generateTargetMapping($entity);
        if ($sourceMapping !== []) {
            $this->sourceMappingsByHash[$entityClass][$hash] = $sourceMapping;
        }
        if ($targetMapping !== []) {
            $this->targetMappingsByHash[$entityClass][$hash] = $targetMapping;
        }
    }

    private function createHash(object $entity): string
    {
        return spl_object_hash($entity);
    }

    private function generateSourceMapping(object $entity): array
    {
        $entityClass = get_class($entity);
        $sourceMappingConfig = $this->sourceMappings[$entityClass];
        $sourceMapping = [];
        foreach ($sourceMappingConfig as $entityField => $rowField) {
            if (!$this->propertyAccessor->isReadable($entity, $entityField)) {
                $sourceMapping[$entityField] = null;
            } else {
                $sourceMapping[$entityField] = $this->propertyAccessor->getValue($entity, $entityField);
            }
        }

        return $sourceMapping;
    }

    private function generateTargetMapping(object $entity): array
    {
        $entityClass = get_class($entity);
        $targetMappingConfig = $this->targetMappings[$entityClass];
        $targetMapping = [];
        foreach ($targetMappingConfig as $entityField => $rowField) {
            if (!$this->propertyAccessor->isReadable($entity, $entityField)) {
                $targetMapping[$entityField] = null;
            } else {
                $targetMapping[$entityField] = $this->propertyAccessor->getValue($entity, $entityField);
            }
        }

        return $targetMapping;
    }

    public function collectIdentifiers(Row $row, array $sourceMapping): array
    {
        $sourceIdentifier = [];
        foreach ($sourceMapping as $mappingField => $sourceField) {
            if (!$row->has($sourceField)) {
                break;
            }
            $sourceIdentifier[$mappingField] = $row->get($sourceField)->value();
        }
        return $sourceIdentifier;
    }

    public function get(
        mixed $needle,
        ?string $entityClass = null,
        ?string $mappingType = null
    ): ValidityIntervalBasedEntity {
        if (!isset($entityClass) && isset($mappingType)) {
            $entityClass = $this->getEntityClassByMappingType($mappingType);
        }
        if ($needle instanceof Row) {
            $identifier = $this->generateIdentifierByRow($needle, $this->sourceMappings[$entityClass]);
            try {
                return $this->findEntityByIdentifiers($identifier, $entityClass);
            } catch (NotFoundException) {
            }
            $mapping = $this->mappingCache->find($identifier);
            $identifier = $this->entityValueArrayCacheKeyGenerator->generate(
                $entityClass,
                $mapping->getTargetIdentifier()
            );
            return $this->validityBasedObjects[$entityClass]->get($identifier);
        } elseif (is_object($needle)) {
            $identifiers = $this->entityIdentifiersGuesser->guess($needle);
            return $this->validityBasedObjects[$entityClass]->get($identifiers);
        } elseif (is_array($needle)) {
            return $this->validityBasedObjects[$entityClass]->get($needle);
        } elseif (
            array_key_exists($entityClass, $this->validityBasedObjects) &&
            array_key_exists($entityClass, $this->sourceMappings) &&
            array_key_exists($entityClass, $this->targetMappings) &&
            count($this->sourceMappings[$entityClass]) === 1
        ) {
            $sourceIdentifiers = [];
            foreach ($this->sourceMappings[$entityClass] as $sourceField => $targetField) {
                $sourceIdentifiers[$sourceField] = $needle;
            }
            $targetIdentifiers = [];
            foreach ($this->targetMappings[$entityClass] as $sourceField => $targetField) {
                $targetIdentifiers[$sourceField] = $needle;
            }
            if ($this->validityBasedObjects[$entityClass]->has($sourceIdentifiers)) {
                return $this->validityBasedObjects[$entityClass]->get($sourceIdentifiers);
            } elseif ($this->validityBasedObjects[$entityClass]->has($targetIdentifiers)) {
                return $this->validityBasedObjects[$entityClass]->get($targetIdentifiers);
            }
            throw new NotFoundException('validityBasedObjects', $entityClass);
        } else {
            throw new NotFoundException('validityBasedObjects', $entityClass);
        }
    }

    public function getEntityClassByMappingType(string $mappingType): string
    {
        $entityClass = array_search($mappingType, $this->mappingTypes, true);
        if ($entityClass === false) {
            throw new NotFoundException(
                $this->mappingTypes,
                $mappingType
            );
        }

        return $entityClass;
    }

    private function generateIdentifierByRow(Row $row, array $mapping): array
    {
        $identifier = [];
        foreach ($mapping as $targetField => $sourceField) {
            if (!$row->has($sourceField)) {
                throw new NotFoundException('Row', $sourceField);
            }
            $identifier[$targetField] = $row->get($sourceField)->value();
        }

        return $identifier;
    }

    public function findEntityByIdentifiers(array $identifiers, string $entityClass): ValidityIntervalBasedEntity
    {
        if (!array_key_exists($entityClass, $this->sourceMappingsByHash)) {
            throw new NotFoundException($this->sourceMappingsByHash, $entityClass);
        }

        foreach ($this->sourceMappingsByHash[$entityClass] as $hash => $identifiersBySourceMapping) {
            $entityIdentifier = $this->entityIdentifierByHash[$hash];
            if ($identifiersBySourceMapping === $identifiers) {
                return $this->get($entityIdentifier, $entityClass);
            }
        }
        foreach ($this->targetMappingsByHash[$entityClass] as $hash => $identifiersByTargetMapping) {
            $entityIdentifier = $this->entityIdentifierByHash[$hash];
            if ($identifiersByTargetMapping === $identifiers) {
                return $this->get($entityIdentifier, $entityClass);
            }
        }
        throw new NotFoundException('Identifiers', $identifiers);
    }

    public function mergeObjectChanges(ObjectsChanges $objectsChanges): void
    {
        foreach ($objectsChanges->getNew() as $item) {
            $this->add($item);
        }
        foreach ($objectsChanges->getChanged() as $item) {
            $this->add($item);
        }
        foreach ($objectsChanges->getDeleted() as $item) {
            $this->add($item);
        }
    }

    public function add(object $entity): void
    {
        if ($entity instanceof EtlMapping) {
            $this->mappingCache->add($entity);
        } elseif (array_key_exists(get_class($entity), $this->validityBasedObjects)) {
            $identifiers = $this->entityIdentifiersGuesser->guess($entity);
            $validityBasedObject = $this->validityBasedObjects[get_class($entity)];
            $validityBasedObject->add($identifiers, $entity);
        }
    }
}
