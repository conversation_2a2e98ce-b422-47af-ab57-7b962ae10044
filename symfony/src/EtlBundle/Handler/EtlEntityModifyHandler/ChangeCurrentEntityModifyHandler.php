<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Handler\EtlEntityModifyHandler;

use LoginAutonom\DatabaseBundle\DTO\ObjectsChanges;
use LoginAutonom\DatabaseBundle\Handler\EntityChangePlanHandler;
use LoginAutonom\EtlBundle\DTO\EtlEntityModified;
use LoginAutonom\EtlBundle\DTO\EtlEntityModifyInfo;
use LoginAutonom\EtlBundle\Interfaces\EtlEntityModifyHandlerInterface;

final readonly class ChangeCurrentEntityModifyHandler implements EtlEntityModifyHandlerInterface
{
    public function __construct(
        private EntityChangePlanHandler $changePlanHandler,
    ) {
    }

    public function modify(EtlEntityModifyInfo $info): EtlEntityModified
    {
        $entity = $info->getEntity();
        $changePlan = $info->getPlan();
        $entity = $this->changePlanHandler->handle($entity, $changePlan);
        $objectChanges = new ObjectsChanges();
        $objectChanges->addChanged($entity);

        return new EtlEntityModified(
            $entity,
            $objectChanges
        );
    }

    public static function getName(): string
    {
        return 'change-current-entity-modify-handler';
    }
}
