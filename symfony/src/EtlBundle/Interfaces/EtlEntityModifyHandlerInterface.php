<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Interfaces;

use LoginAutonom\EtlBundle\DTO\EtlEntityModified;
use LoginAutonom\EtlBundle\DTO\EtlEntityModifyInfo;
use Symfony\Component\DependencyInjection\Attribute\AutoconfigureTag;

#[AutoconfigureTag(EtlEntityModifyHandlerInterface::TAG)]
interface EtlEntityModifyHandlerInterface
{
    public const TAG = 'etl.entity-modify.handler';
    public function modify(EtlEntityModifyInfo $info): EtlEntityModified;

    public static function getName(): string;
}
