<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Transformer;

use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use LoginAutonom\EtlBundle\Interfaces\FlowOneRowTransformerInterface;
use LoginAutonom\EtlBundle\Trait\EtlCommonFunctionsTrait;
use LoginAutonom\EtlBundle\Trait\FlowTransformerTrait;

final class ConcatFieldsToEntryTransformer implements FlowOneRowTransformerInterface
{
    use EtlCommonFunctionsTrait;
    use FlowTransformerTrait;

    public function __construct(
        private readonly array $orderedFieldMap,
        private readonly string $targetField,
        private readonly string $separator
    ) {
    }

    public function transform(Row $row, FlowContext $context): Row
    {
        $values = array_filter(
            array_map(fn($fieldName) => $row->has($fieldName) ?
                (string)$row->get($fieldName) : null, $this->orderedFieldMap)
        );

        $identifier = implode($this->separator, $values);

        return $row->add($context->entryFactory()->create($this->targetField, $identifier));
    }
}
