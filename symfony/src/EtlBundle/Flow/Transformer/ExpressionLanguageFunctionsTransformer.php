<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Transformer;

use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use LoginAutonom\EtlBundle\Interfaces\FlowOneRowTransformerInterface;
use LoginAutonom\EtlBundle\Trait\EtlSerializableTrait;
use LoginAutonom\EtlBundle\Trait\FlowTransformerTrait;
use Symfony\Component\ExpressionLanguage\ExpressionFunction;
use Symfony\Component\ExpressionLanguage\ExpressionLanguage;

final class ExpressionLanguageFunctionsTransformer implements FlowOneRowTransformerInterface
{
    use EtlSerializableTrait;
    use FlowTransformerTrait;

    public const FIELD = 'field';

    public function __construct(
        private readonly string $expression,
        private readonly string $targetField,
    ) {
    }

    public function transform(Row $row, FlowContext $context): Row
    {
        $expressionLanguage = new ExpressionLanguage();
        $expressionLanguage->addFunction(
            new ExpressionFunction(
                self::FIELD,
                static function () {
                },
                static function ($params, $fieldName) use ($row) {
                    return $row->get($fieldName)->value();
                },
            )
        );
        $evaluated = $expressionLanguage->evaluate($this->expression);

        return $row->set(
            $context->entryFactory()->create(
                $this->targetField,
                $evaluated
            )
        );
    }
}
