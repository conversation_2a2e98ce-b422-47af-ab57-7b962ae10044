<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Transformer;

use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoAutoJoinToEmployee;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoAutoJoinToEmployeeContract;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoVisibilityPermissionStamp;
use LoginAutonom\EtlBundle\Interfaces\EtlCacheAwareInterface;
use LoginAutonom\EtlBundle\Interfaces\EtlIdentifiersBasedProviderInterface;
use LoginAutonom\EtlBundle\Interfaces\FlowPreProcessTransformerInterface;
use LoginAutonom\EtlBundle\Trait\EtlCacheAwareTrait;
use LoginAutonom\EtlBundle\Trait\EtlCommonFunctionsTrait;
use LoginAutonom\EtlBundle\Trait\EtlSerializableTrait;
use LoginAutonom\EtlBundle\Trait\FlowTransformerTrait;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;

final class FindEntityByIdentifierFieldsTransformer implements
    FlowPreProcessTransformerInterface,
    EtlCacheAwareInterface
{
    use EtlSerializableTrait;
    use EtlCacheAwareTrait;
    use EtlCommonFunctionsTrait;
    use FlowTransformerTrait;

    public const IDENTIFIERS = 'identifiers';
    public const ENTITIES = 'entities';

    public function __construct(
        private readonly string $targetField,
        private readonly array $identifierFieldsMap,
        private readonly EtlIdentifiersBasedProviderInterface $provider,
        private readonly PropertyAccessorInterface $propertyAccessor,
    ) {
    }

    public function collectInformation(Row $row, FlowContext $context, \ArrayObject $info): void
    {
        try {
            $info[self::IDENTIFIERS][] = $this->generateIdentifiersByRow($row);
        } catch (NotFoundException $e) {
        }
    }

    private function generateIdentifiersByRow(Row $row): array
    {
        $identifiers = [];
        foreach ($this->identifierFieldsMap as $sourceField => $identifierName) {
            if (!$row->has($sourceField)) {
                throw new NotFoundException($row, $sourceField);
            }
            $identifierValue = $row->valueOf($sourceField);
            $identifiers[$identifierName] = $identifierValue;
        }
        if (count($identifiers) !== count($this->identifierFieldsMap)) {
            throw new NotFoundException($row, $this->identifierFieldsMap);
        }

        return $identifiers;
    }

    public function executeAction(\ArrayObject $info, FlowContext $context): void
    {
        if (!$info->offsetExists(self::IDENTIFIERS)) {
            return;
        }
        $info[self::ENTITIES] = $this->provider->provide(
            $info[self::IDENTIFIERS],
            [
                new NoVisibilityPermissionStamp('ETL'),
                new NoAutoJoinToEmployee(),
                new NoAutoJoinToEmployeeContract()
            ]
        );
    }

    public function buildRow(Row $row, FlowContext $context, \ArrayObject $info): Row
    {
        try {
            $entity = $this->findEntityByRow($info[self::ENTITIES], $row);
        } catch (NotFoundException) {
            return $row;
        }

        return $row->set(
            $context->entryFactory()->create($this->targetField, $entity)
        );
    }

    private function findEntityByRow(array $entities, Row $row): object
    {
        $identifiers = $this->generateIdentifiersByRow($row);
        foreach ($entities as $entity) {
            $found = true;
            foreach ($identifiers as $identifierName => $identifierValue) {
                if (!$this->propertyAccessor->isReadable($entity, $identifierName)) {
                    $found = false;
                    break;
                }
                $entityValue = $this->propertyAccessor->getValue($entity, $identifierName);
                if ($identifierValue != $entityValue) {
                    $found = false;
                    break;
                }
            }
            if ($found) {
                return $entity;
            }
        }
        throw new NotFoundException($entities, $identifiers);
    }
}
