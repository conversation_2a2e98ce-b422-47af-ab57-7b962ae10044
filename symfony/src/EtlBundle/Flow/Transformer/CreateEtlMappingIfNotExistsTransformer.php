<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Transformer;

use Flow\ETL\FlowContext;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\DatabaseBundle\DTO\IdsWithValidityInterval;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityIntervalBasedEntitiesProviderInterface;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoAutoJoinToEmployee;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoAutoJoinToEmployeeContract;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoVisibilityPermissionStamp;
use LoginAutonom\EtlBundle\Builder\EtlMappingBuilder;
use LoginAutonom\EtlBundle\Interfaces\EtlCacheAwareInterface;
use LoginAutonom\EtlBundle\Trait\EtlCacheAwareTrait;
use LoginAutonom\EtlBundle\Trait\EtlCommonFunctionsTrait;
use LoginAutonom\EtlBundle\Trait\EtlSerializableTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class CreateEtlMappingIfNotExistsTransformer implements
    Transformer,
    LoggerAwareInterface,
    EtlCacheAwareInterface
{
    use EtlCacheAwareTrait;
    use LoggerAwareTrait;
    use EtlSerializableTrait;
    use EtlCommonFunctionsTrait;

    public function __construct(
        private string $mappingField,
        private string $mappingType,
        private string $identifierField,
        private string $entityClass,
        private EtlMappingBuilder $etlMappingBuilder,
        private ValidityIntervalBasedEntitiesProviderInterface $provider,
        private ?string $fromField = null,
        private ?string $toField = null,
        private ?string $workflow = null
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $this->logger->info('Starting transformer: ' . get_class($this), ['rows' => $rows->count()]);
        $sourceIdentifiers = [];

        foreach ($rows as $row) {
            if (!$row->has($this->identifierField)) {
                continue;
            }
            if (empty($row->valueOf($this->identifierField))) {
                continue;
            }
            if ($this->isRowFinal($row)) {
                continue;
            }
            if ($row->has($this->mappingField)) {
                continue;
            }

            $sourceIdentifiers[] = $row->valueOf($this->identifierField);
        }

        if (empty($sourceIdentifiers)) {
            return $rows;
        }

        $requiredIdentifiers = array_unique($sourceIdentifiers);
        $entityCache = $this->getEntityCache($context);

        if (empty($requiredIdentifiers)) {
            return $rows;
        }
        $ids = new IdsWithValidityInterval($requiredIdentifiers);
        $entities = $this->provider->provide(
            $ids,
            [
                new NoVisibilityPermissionStamp('ETL'),
                new NoAutoJoinToEmployee(),
                new NoAutoJoinToEmployeeContract()
            ],
            $entityCache->getValidityBasedObject($this->entityClass)
        );

        $newRows = new Rows();
        foreach ($rows as $row) {
            if (
                $this->isRowFinal($row) ||
                $row->has($this->mappingField) ||
                !$row->has($this->identifierField)
            ) {
                $newRows = $newRows->add($row);
                continue;
            }

            try {
                $validityIntervalBasedEntity = $entityCache->get($row, $this->entityClass);
            } catch (NotFoundException) {
                $newRows = $newRows->add($row);
                continue;
            }

            try {
                $sourceMapping = $entityCache->getSourceMapping($this->mappingType);
                $identifiers = $entityCache->collectIdentifiers($row, $sourceMapping);
                $entityCache->findMapping($identifiers);
                $newRows = $newRows->add($row);
            } catch (NotFoundException) {
                $entities = $validityIntervalBasedEntity->getAll();
                $entity = reset($entities);
                $etlMapping = $this->etlMappingBuilder->reset()
                    ->setEntity($entity)
                    ->setMappingType($this->mappingType)
                    ->setWorkflow($context->config->id())
                    ->setRow($row)
                    ->setSourceFields($entityCache->getSourceMapping($this->mappingType))
                    ->setTargetFields($entityCache->getTargetMapping($this->mappingType))
                    ->build();
                $changeStorage = $this->getChangesStorage($row);
                $changeStorage->addNew($etlMapping);
                $entityCache->add($etlMapping);
                $newRows = $newRows->add(
                    $row->set($context->entryFactory()->create($this->mappingField, $etlMapping))
                );
            }
        }

        $this->logger->info('Stopping transformer: ' . get_class($this), ['rows' => $newRows->count()]);
        return $newRows;
    }
}
