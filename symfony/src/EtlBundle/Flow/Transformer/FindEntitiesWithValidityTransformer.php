<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Transformer;

use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\DatabaseBundle\DTO\IdsWithValidityInterval;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityIntervalBasedEntitiesProviderInterface;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoAutoJoinToEmployee;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoAutoJoinToEmployeeContract;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoVisibilityPermissionStamp;
use LoginAutonom\EtlBundle\Interfaces\EtlCacheAwareInterface;
use LoginAutonom\EtlBundle\Trait\EtlCacheAwareTrait;
use LoginAutonom\EtlBundle\Trait\EtlCommonFunctionsTrait;
use LoginAutonom\EtlBundle\Trait\EtlSerializableTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class FindEntitiesWithValidityTransformer implements
    Transformer,
    EtlCacheAwareInterface,
    LoggerAwareInterface
{
    use EtlSerializableTrait;
    use EtlCacheAwareTrait;
    use EtlCommonFunctionsTrait;
    use LoggerAwareTrait;

    public function __construct(
        private readonly string $identifierField,
        private readonly string $targetField,
        private readonly string $entityClass,
        private ValidityIntervalBasedEntitiesProviderInterface $provider,
        private ?string $fromField = null,
        private ?string $toField = null,
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $this->logger->info('Starting transformer: ' . get_class($this), ['rows' => $rows->count()]);
        $targetIdentifiers = [];
        /** @var Row $row */
        foreach ($rows as $row) {
            if ($this->isRowFinal($row)) {
                continue;
            }
            if ($row->has($this->targetField)) {
                continue;
            }
            if (!$row->has($this->identifierField)) {
                continue;
            }
            $targetIdentifiers[] = $row->valueOf($this->identifierField);
        }
        if ($targetIdentifiers === []) {
            return $rows;
        }
        $entityCache = $this->getEntityCache($context);
        $required = [];
        foreach ($targetIdentifiers as $targetIdentifier) {
            try {
                $entityInCache = $entityCache->get($targetIdentifier, $this->entityClass);
            } catch (NotFoundException $e) {
                if (is_array($targetIdentifier)) {
                    array_push($required, ...array_values($targetIdentifier));
                } else {
                    $required[] = $targetIdentifier;
                }
            }
        }
        if ($required !== []) {
            $ids = new IdsWithValidityInterval($required);
            $entities = $this->provider->provide(
                $ids,
                [
                    new NoVisibilityPermissionStamp('ETL'),
                    new NoAutoJoinToEmployee(),
                    new NoAutoJoinToEmployeeContract()
                ],
                $entityCache->getValidityBasedObject($this->entityClass)
            );
        }

        $newRows = new Rows();
        /** @var Row $row */
        foreach ($rows as $row) {
            if ($this->isRowFinal($row)) {
                $newRows = $newRows->add($row);
                continue;
            }
            if ($row->has($this->targetField)) {
                $newRows = $newRows->add($row);
                continue;
            }
            if (!$row->has($this->identifierField)) {
                $newRows = $newRows->add($row);
                continue;
            }
            if (isset($this->fromField) && !$row->has($this->fromField)) {
                $newRows = $newRows->add($row);
                continue;
            }
            $identifier = $row->valueOf($this->identifierField);
            try {
                # @TODO: több identifier
                $validityIntervalBasedEntity = $entityCache->get($identifier, $this->entityClass);
            } catch (NotFoundException $e) {
                $newRows = $newRows->add($row);
                continue;
            }
            try {
                if (
                    isset($this->fromField) && $row->has($this->fromField) &&
                    isset($this->toField) && $row->has($this->toField)
                ) {
                    $entitiesByValidity = $validityIntervalBasedEntity->getAllByDateInterval(
                        $row->get($this->fromField)->value(),
                        $row->get($this->toField)->value(),
                    );
                    if ($entitiesByValidity === []) {
                        throw new NotFoundException(
                            $validityIntervalBasedEntity,
                            $row
                        );
                    }
                    $entityByValidity = reset($entitiesByValidity);
                } elseif (isset($this->fromField) && $row->has($this->fromField)) {
                    $entityByValidity = $validityIntervalBasedEntity->getByDay(
                        $row->get($this->fromField)->value(),
                    );
                } else {
                    $allEntityByValidity = $validityIntervalBasedEntity->getAll();
                    if ($allEntityByValidity === []) {
                        throw new NotFoundException(
                            $validityIntervalBasedEntity,
                            $row
                        );
                    }
                    $entityByValidity = reset($allEntityByValidity);
                }
                $newRows = $newRows->add(
                    $row->set($context->entryFactory()->create($this->targetField, $entityByValidity))
                );
            } catch (NotFoundException $e) {
                $newRows = $newRows->add($row);
                continue;
            }
        }
        $this->logger->info('Stopping transformer: ' . get_class($this), ['rows' => $newRows->count()]);

        return $newRows;
    }
}
