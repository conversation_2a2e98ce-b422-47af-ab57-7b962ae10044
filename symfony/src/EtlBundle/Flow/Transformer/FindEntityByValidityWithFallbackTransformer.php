<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Transformer;

use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use Flow\ETL\Transformer;
use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\DatabaseBundle\DTO\IdsWithValidityInterval;
use LoginAutonom\DatabaseBundle\DTO\ValidityIntervalBasedEntity;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityIntervalBasedEntitiesProviderInterface;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoAutoJoinToEmployee;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoAutoJoinToEmployeeContract;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoVisibilityPermissionStamp;
use LoginAutonom\EtlBundle\Interfaces\EtlCacheAwareInterface;
use LoginAutonom\EtlBundle\Trait\EtlCacheAwareTrait;
use LoginAutonom\EtlBundle\Trait\EtlCommonFunctionsTrait;
use LoginAutonom\EtlBundle\Trait\EtlSerializableTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class FindEntityByValidityWithFallbackTransformer implements
    Transformer,
    LoggerAwareInterface,
    EtlCacheAwareInterface
{
    use EtlCacheAwareTrait;
    use LoggerAwareTrait;
    use EtlSerializableTrait;
    use EtlCommonFunctionsTrait;

    public function __construct(
        private readonly string $identifierField,
        private readonly string $targetEntityField,
        private readonly string $entityClass,
        private readonly ValidityIntervalBasedEntitiesProviderInterface $provider,
        private readonly string $fromField,
        private readonly string $targetValidFromField,
        private readonly ?string $targetValidToField = null,
        private readonly ?string $toField = null,
        private readonly ?string $fallbackValidFromField = null,
        private readonly ?string $fallbackValidToField = null,
    ) {
    }

    public function transform(Rows $rows, FlowContext $context): Rows
    {
        $this->logStart($rows);

        $this->preloadEntities($rows, $context);
        $newRows = $this->processAllRows($rows, $context);

        $this->logEnd($newRows);

        return $newRows;
    }

    private function preloadEntities(Rows $rows, FlowContext $context): void
    {
        $targetIdentifiers = $this->collectTargetIdentifiers($rows);

        if (empty($targetIdentifiers)) {
            return;
        }

        $this->loadMissingEntities($targetIdentifiers, $context);
    }

    private function collectTargetIdentifiers(Rows $rows): array
    {
        $targetIdentifiers = [];

        foreach ($rows as $row) {
            if ($this->shouldSkipRowForCollection($row)) {
                continue;
            }
            $targetIdentifiers[] = $row->valueOf($this->identifierField);
        }

        return $targetIdentifiers;
    }

    private function shouldSkipRowForCollection(Row $row): bool
    {
        return $this->isRowFinal($row)
            || $row->has($this->targetEntityField)
            || !$row->has($this->identifierField);
    }

    private function loadMissingEntities(array $targetIdentifiers, FlowContext $context): void
    {
        $entityCache = $this->getEntityCache($context);
        $missingIdentifiers = $this->findMissingIdentifiers($targetIdentifiers, $entityCache);

        if (empty($missingIdentifiers)) {
            return;
        }

        $ids = new IdsWithValidityInterval($missingIdentifiers);
        $this->provider->provide(
            $ids,
            [
                new NoVisibilityPermissionStamp('ETL'),
                new NoAutoJoinToEmployee(),
                new NoAutoJoinToEmployeeContract()
            ],
            $entityCache->getValidityBasedObject($this->entityClass)
        );
    }

    private function findMissingIdentifiers(array $targetIdentifiers, $entityCache): array
    {
        $missing = [];

        foreach ($targetIdentifiers as $identifier) {
            try {
                $entityCache->get($identifier, $this->entityClass);
            } catch (NotFoundException) {
                $missing[] = $identifier;
            }
        }

        return $missing;
    }

    private function processAllRows(Rows $rows, FlowContext $context): Rows
    {
        $newRows = new Rows();

        foreach ($rows as $row) {
            $processedRow = $this->processRow($row, $context);
            $newRows = $newRows->add($processedRow);
        }

        return $newRows;
    }

    private function processRow(Row $row, FlowContext $context): Row
    {
        if ($this->shouldSkipRowProcessing($row)) {
            return $row;
        }

        if ($this->hasRequiredFields($row)) {
            return $this->processRowWithEntity($row, $context);
        }

        return $this->handleFallback($row, $context);
    }

    private function shouldSkipRowProcessing(Row $row): bool
    {
        return $this->isRowFinal($row) || $row->has($this->targetEntityField);
    }

    private function hasRequiredFields(Row $row): bool
    {
        return $row->has($this->identifierField) && $row->has($this->fromField);
    }

    private function processRowWithEntity(Row $row, FlowContext $context): Row
    {
        $identifier = $row->valueOf($this->identifierField);
        $entityCache = $this->getEntityCache($context);

        try {
            $validityEntity = $entityCache->get($identifier, $this->entityClass);
            return $this->findEntityByValidity($row, $validityEntity, $context);
        } catch (NotFoundException) {
            return $this->handleFallback($row, $context);
        }
    }

    private function findEntityByValidity(
        Row $row,
        ValidityIntervalBasedEntity $validityEntity,
        FlowContext $context
    ): Row {
        try {
            $entity = $this->getEntityByValidityPeriod($row, $validityEntity);
        } catch (NotFoundException) {
            return $this->handleFallback($row, $context);
        }

        return $this->addValidityFields($row, $entity, $context);
    }

    private function getEntityByValidityPeriod(Row $row, ValidityIntervalBasedEntity $validityEntity): object
    {
        if ($this->shouldUseInterval($row)) {
            return $this->getEntityByInterval($row, $validityEntity);
        }

        if (is_null($row->get($this->fromField)->value())) {
            throw new NotFoundException(
                $row,
                $this->fromField,
                'Field value is null'
            );
        }

        return $validityEntity->getByDay($row->get($this->fromField)->value());
    }

    private function shouldUseInterval(Row $row): bool
    {
        return isset($this->toField) && $row->has($this->toField);
    }

    private function getEntityByInterval(Row $row, ValidityIntervalBasedEntity $validityEntity): object
    {
        $entities = $validityEntity->getAllByDateInterval(
            $row->get($this->fromField)->value(),
            $row->get($this->toField)->value()
        );

        if (empty($entities)) {
            throw new NotFoundException($validityEntity, $row);
        }

        return reset($entities);
    }

    private function addValidityFields(Row $row, object $entity, FlowContext $context): Row
    {
        $this->validateEntityHasValidity($entity);

        $row = $this->setValidFromField($row, $entity, $context);
        $row = $this->setValidToField($row, $entity, $context);

        return $this->setTargetEntity($row, $entity, $context);
    }

    private function validateEntityHasValidity(object $entity): void
    {
        if (!method_exists($entity, 'getValidity')) {
            $message = sprintf(
                'Cannot get validity from entity %s, getValidity method does not exist',
                get_class($entity)
            );
            $this->logWarning($message);
            throw new \InvalidArgumentException('Entity does not have getValidity method');
        }
    }

    private function setValidFromField(Row $row, object $entity, FlowContext $context): Row
    {
        if (!isset($this->targetValidFromField)) {
            return $row;
        }

        $validFrom = $entity->getValidity()->getValidFrom();
        return $row->set(
            $context->entryFactory()->create($this->targetValidFromField, $validFrom)
        );
    }

    private function setValidToField(Row $row, object $entity, FlowContext $context): Row
    {
        if (!isset($this->targetValidToField)) {
            return $row;
        }

        $validTo = $entity->getValidity()->getValidTo();
        return $row->set(
            $context->entryFactory()->create($this->targetValidToField, $validTo)
        );
    }

    private function setTargetEntity(Row $row, object $entity, FlowContext $context): Row
    {
        return $row->set(
            $context->entryFactory()->create($this->targetEntityField, $entity)
        );
    }

    private function handleFallback(Row $row, FlowContext $context): Row
    {
        $row = $this->setFallbackValidFrom($row, $context);
        return $this->setFallbackValidTo($row, $context);
    }

    private function setFallbackValidFrom(Row $row, FlowContext $context): Row
    {
        if (!$this->canSetFallbackValidFrom($row)) {
            return $row;
        }

        return $row->set(
            $context->entryFactory()->create(
                $this->targetValidFromField,
                $row->get($this->fallbackValidFromField)->value()
            )
        );
    }

    private function canSetFallbackValidFrom(Row $row): bool
    {
        return isset($this->fallbackValidFromField)
            && $row->has($this->fallbackValidFromField);
    }

    private function setFallbackValidTo(Row $row, FlowContext $context): Row
    {
        if (!$this->canSetFallbackValidTo($row)) {
            return $row;
        }

        return $row->set(
            $context->entryFactory()->create(
                $this->targetValidToField,
                $row->get($this->fallbackValidToField)->value()
            )
        );
    }

    private function canSetFallbackValidTo(Row $row): bool
    {
        return isset($this->targetValidToField)
            && isset($this->fallbackValidToField)
            && $row->has($this->fallbackValidToField);
    }

    private function logStart(Rows $rows): void
    {
        $this->logger->info('Starting transformer: ' . get_class($this), ['rows' => $rows->count()]);
    }

    private function logEnd(Rows $rows): void
    {
        $this->logger->info('Stopping transformer: ' . get_class($this), ['rows' => $rows->count()]);
    }

    private function logWarning(string $message): void
    {
        $this->logger?->warning($message);
    }
}
