<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Processor;

use LoginAutonom\ExternalResourcesBundle\Nexon\Builder\DailyWorkSchedule2ResponseDescriptorBuilder;

final readonly class DailyWorkSchedule2ResponseProcessor
{
    public function __construct(
        private DailyWorkSchedule2ResponseDescriptorBuilder $employeeResponseDescriptorBuilder
    ) {
    }

    public function process(array $persons, array $workSchedules): array
    {
        return $this->employeeResponseDescriptorBuilder->reset()
            ->setPersons($persons)
            ->setWorkSchedules($workSchedules)
            ->build();
    }
}
