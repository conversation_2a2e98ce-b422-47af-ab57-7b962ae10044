<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Descriptor;

use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\CommonResponseFieldNameEnum;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;

#[Autoconfigure(autowire: false)]
final readonly class DailyWorkSchedule2Descriptor
{
    public function __construct(
        private int $personId,
        private string $employeeId,
        private array $personData,
        private array $workSchedules
    ) {
    }

    public function getPersonId(): int
    {
        return $this->personId;
    }

    public function getEmployeeId(): string
    {
        return $this->employeeId;
    }

    public function getPersonData(): array
    {
        return $this->personData;
    }

    public function getWorkSchedules(): array
    {
        return $this->workSchedules;
    }

    public function getWorkScheduleForDay(string $day): array
    {
        foreach ($this->workSchedules as $schedule) {
            if (
                isset($schedule[CommonResponseFieldNameEnum::DAY->value]) &&
                $schedule[CommonResponseFieldNameEnum::DAY->value] === $day
            ) {
                return $schedule;
            }
        }

        throw new \RuntimeException("Work schedule not found for day: $day");
    }

    public function getAllWorkDays(): array
    {
        return array_column($this->workSchedules, CommonResponseFieldNameEnum::DAY->value);
    }
}
