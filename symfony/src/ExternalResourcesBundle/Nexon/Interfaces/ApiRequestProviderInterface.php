<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Interfaces;

use LoginAutonom\ExternalResourcesBundle\DTO\ApiConfigurationDTO;
use Symfony\Component\DependencyInjection\Attribute\AutoconfigureTag;

#[AutoconfigureTag(ApiRequestProviderInterface::TAG)]
interface ApiRequestProviderInterface
{
    public const TAG = 'external-resources.api-request.handler';

    public function provideData(ApiConfigurationDTO $config): \Generator;

    public static function getName(): string;
}
