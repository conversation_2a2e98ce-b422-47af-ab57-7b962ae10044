<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Builder;

use LoginAutonom\ExternalResourcesBundle\Nexon\Descriptor\DailyWorkSchedule2Descriptor;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiEndpointEnum;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\CommonResponseFieldNameEnum;

final class DailyWorkSchedule2ResponseDescriptorBuilder
{
    private array $persons;
    private array $workSchedules;
    public function build(): array
    {
        $personsById = $this->indexPersonsByPersonId($this->persons);
        $workSchedulesByPersonId = $this->groupWorkSchedulesByPersonId($this->workSchedules);
        $descriptors = [];
        foreach ($workSchedulesByPersonId as $personId => $schedules) {
            if (!isset($personsById[$personId])) {
                continue;
            }
            $personData = $personsById[$personId];
            $employeeId = $this->getEmployeeIdFromPersonData($personData);
            $descriptors[][ApiEndpointEnum::GET_PERSONS_2->value] = new DailyWorkSchedule2Descriptor(
                $personId,
                $employeeId,
                $personData,
                $schedules
            );
        }

        return $descriptors;
    }

    public function reset(): self
    {
        unset(
            $this->persons,
            $this->workSchedules
        );

        return $this;
    }

    public function setPersons(array $persons): self
    {
        $this->persons = $persons;
        return $this;
    }

    public function setWorkSchedules(array $workSchedules): self
    {
        $this->workSchedules = $workSchedules;
        return $this;
    }

    private function indexPersonsByPersonId(array $persons): array
    {
        $indexedPersons = [];
        foreach ($persons as $person) {
            $personId = $person[CommonResponseFieldNameEnum::PERSON_ID->value] ?? null;
            if (!is_null($personId)) {
                $indexedPersons[$personId] = $person;
            }
        }
        return $indexedPersons;
    }

    private function groupWorkSchedulesByPersonId(array $workSchedules): array
    {
        $grouped = [];
        foreach ($workSchedules as $schedule) {
            $personId = $schedule[CommonResponseFieldNameEnum::PERSON_ID->value] ?? null;
            if ($personId !== null) {
                $grouped[$personId][] = $schedule;
            }
        }
        return $grouped;
    }

    private function getEmployeeIdFromPersonData(array $personData): string
    {
        return $personData[CommonResponseFieldNameEnum::EMPLOYEE_NUMBER->value];
    }
}
