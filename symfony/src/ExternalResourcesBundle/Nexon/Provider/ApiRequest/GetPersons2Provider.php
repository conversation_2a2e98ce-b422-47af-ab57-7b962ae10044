<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Provider\ApiRequest;

use LoginAutonom\ExternalResourcesBundle\Adapter\APIAdapter;
use LoginAutonom\ExternalResourcesBundle\Builder\RequestDataBuilder;
use LoginAutonom\ExternalResourcesBundle\DTO\ApiConfigurationDTO;
use LoginAutonom\ExternalResourcesBundle\DTO\RequestRawDataDTO;
use LoginAutonom\ExternalResourcesBundle\Factory\APIAdapterFactory;
use LoginAutonom\ExternalResourcesBundle\Factory\RequestDataBuilderFactory;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiEndpointEnum;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiHeaderEnum;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiResponseValueEnum;

final readonly class GetPersons2Provider
{
    public function __construct(
        private APIAdapterFactory $adapterFactory,
        private RequestDataBuilderFactory $requestDataBuilderFactory,
    ) {
    }

    public function provideData(ApiConfigurationDTO $config): array
    {
        $adapter = $this->adapterFactory->build($config);
        $requestDataBuilder = $this->requestDataBuilderFactory->build($config);
        $personsRequestData = $this->buildPersonsRequestData();

        return $this->fetchApiData($personsRequestData, $requestDataBuilder, $adapter);
    }

    private function buildPersonsRequestData(): RequestRawDataDTO
    {
        $endpoint = ApiEndpointEnum::GET_PERSONS_2;
        return new RequestRawDataDTO(
            $endpoint->getPath(),
            $endpoint->getMethod(),
            ApiHeaderEnum::getDefaultHeaders(),
            []
        );
    }

    private function fetchApiData(
        RequestRawDataDTO $requestRawData,
        RequestDataBuilder $requestDataBuilder,
        APIAdapter $adapter
    ): array {
        $requestData = $requestDataBuilder->reset()
            ->setMethod($requestRawData->getMethod())
            ->setPath($requestRawData->getUrlPath())
            ->setHeaders($requestRawData->getHeaderData())
            ->setBodyData($requestRawData->getBodyData())
            ->build();

        $response = $adapter->fetch($requestData);

        return $response[ApiResponseValueEnum::VALUE->value] ?? [];
    }
}
