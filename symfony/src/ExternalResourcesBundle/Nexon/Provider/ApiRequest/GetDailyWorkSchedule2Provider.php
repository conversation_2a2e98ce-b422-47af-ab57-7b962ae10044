<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Provider\ApiRequest;

use Carbon\Carbon;
use LoginAutonom\ExternalResourcesBundle\Adapter\APIAdapter;
use LoginAutonom\ExternalResourcesBundle\Builder\RequestDataBuilder;
use LoginAutonom\ExternalResourcesBundle\DTO\ApiConfigurationDTO;
use LoginAutonom\ExternalResourcesBundle\DTO\RequestRawDataDTO;
use LoginAutonom\ExternalResourcesBundle\Factory\APIAdapterFactory;
use LoginAutonom\ExternalResourcesBundle\Factory\RequestDataBuilderFactory;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiEndpointEnum;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiHeaderEnum;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiRequestFieldEnum;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiResponseValueEnum;

final readonly class GetDailyWorkSchedule2Provider
{
    public function __construct(
        private APIAdapterFactory $adapterFactory,
        private RequestDataBuilderFactory $requestDataBuilderFactory,
    ) {
    }

    public function provideData(ApiConfigurationDTO $config, array $personIds): array
    {
        $adapter = $this->adapterFactory->build($config);
        $requestDataBuilder = $this->requestDataBuilderFactory->build($config);
        $workSchedulesRequestData = $this->buildWorkSchedulesRequestData($personIds);

        return $this->fetchApiData($workSchedulesRequestData, $requestDataBuilder, $adapter);
    }

    private function buildWorkSchedulesRequestData(array $personIds): RequestRawDataDTO
    {
        $bodyData = [
            ApiRequestFieldEnum::PERSON_IDS->value => $personIds,
            ApiRequestFieldEnum::FROM->value => Carbon::today()->format('Y-m-d'),
            ApiRequestFieldEnum::TO->value => Carbon::today()->addMonth()->format('Y-m-d'),
            ApiRequestFieldEnum::INCLUDE_TERMINATED_HR_RELATIONSHIPS->value => true,
            ApiRequestFieldEnum::INCLUDE_INACTIVE_POSITION_FULFILLMENTS->value => true
        ];

        $endpoint = ApiEndpointEnum::GET_DAILY_WORK_SCHEDULE_2;
        return new RequestRawDataDTO(
            $endpoint->getPath(),
            $endpoint->getMethod(),
            ApiHeaderEnum::getDefaultHeaders(),
            $bodyData
        );
    }

    private function fetchApiData(
        RequestRawDataDTO $requestRawData,
        RequestDataBuilder $requestDataBuilder,
        APIAdapter $adapter
    ): array {
        $requestData = $requestDataBuilder->reset()
            ->setMethod($requestRawData->getMethod())
            ->setPath($requestRawData->getUrlPath())
            ->setHeaders($requestRawData->getHeaderData())
            ->setBodyData($requestRawData->getBodyData())
            ->build();

        $response = $adapter->fetch($requestData);

        return $response[ApiResponseValueEnum::VALUE->value] ?? [];
    }
}
