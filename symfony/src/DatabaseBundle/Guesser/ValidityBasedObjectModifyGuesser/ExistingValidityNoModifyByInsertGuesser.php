<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Guesser\ValidityBasedObjectModifyGuesser;

use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\CoreBundle\Util\ValidityUtil;
use LoginAutonom\DatabaseBundle\DTO\ValidityBasedObjectModifyInfo;
use LoginAutonom\DatabaseBundle\Enum\ValidityBasedObjectActionEnum;
use LoginAutonom\DatabaseBundle\Enum\ValidityBasedObjectModifyTypeEnum;
use LoginAutonom\DatabaseBundle\Guess\ValidityBasedObjectModifyGuess;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityBasedObjectModifyGuesserInterface;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityBasedObjectModifyGuessInterface;

final readonly class ExistingValidityNoModifyByInsertGuesser implements ValidityBasedObjectModifyGuesserInterface
{
    public function __construct(
        private ValidityUtil $validityUtil,
    ) {
    }

    public function guess(ValidityBasedObjectModifyInfo $info): ValidityBasedObjectModifyGuessInterface
    {
        return new ValidityBasedObjectModifyGuess(
            $info,
            ValidityBasedObjectModifyTypeEnum::ValidityNotChanged,
            []
        );
    }

    private function collectContainsIntervals(ValidityBasedObjectModifyInfo $info): array
    {
        $objects = $info->getObjects();
        $stateSet = $info->getStateSet();
        $identifiers = $stateSet->getIdentifiers();
        $validityObjects = $objects->get($identifiers);
        $changedValidFrom = $stateSet->getValidFrom();
        $changedValidTo = $stateSet->getValidTo();
        $originalValidFrom = $stateSet->getOriginalValidFrom();
        $originalValidTo = $stateSet->getOriginalValidTo();

        $contains = [];
        foreach ($validityObjects->getAll() as $validityObject) {
            $validityObjectFrom = $validityObjects->getFrom($validityObject);
            $validityObjectTo = $validityObjects->getTo($validityObject);
            if (
                ($this->validityUtil->ltNull($changedValidTo, $validityObjectFrom) ||
                    $this->validityUtil->gtNull($changedValidFrom, $validityObjectTo)) ||
                ($this->validityUtil->eqNull($originalValidFrom, $validityObjectFrom) &&
                    $this->validityUtil->eqNull($originalValidTo, $validityObjectTo))
            ) {
                $contains[] = $validityObject;
            }
        }
        return $contains;
    }

    public function isSupported(ValidityBasedObjectModifyInfo $info): bool
    {
        if ($info->getAction() !== ValidityBasedObjectActionEnum::MODIFY) {
            return false;
        }

        try {
            $contains = $this->collectContainsIntervals($info);
        } catch (NotFoundException) {
            return false;
        }
        $objects = $info->getObjects();
        $identifiers = $info->getStateSet()->getIdentifiers();
        $allObjects = $objects->get($identifiers)->getAll();

        return count($contains) === count($allObjects);
    }
}
