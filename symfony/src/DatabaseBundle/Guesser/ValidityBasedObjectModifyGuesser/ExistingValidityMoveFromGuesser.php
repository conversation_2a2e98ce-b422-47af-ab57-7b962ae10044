<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Guesser\ValidityBasedObjectModifyGuesser;

use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\CoreBundle\Util\DateTimeUtil;
use LoginAutonom\CoreBundle\Util\ValidityUtil;
use LoginAutonom\DatabaseBundle\DTO\ValidityBasedObjectModifyInfo;
use LoginAutonom\DatabaseBundle\Enum\ValidityBasedObjectActionEnum;
use LoginAutonom\DatabaseBundle\Enum\ValidityBasedObjectModifyTypeEnum;
use LoginAutonom\DatabaseBundle\Guess\ValidityBasedObjectModifyGuess;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityBasedObjectModifyGuesserInterface;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityBasedObjectModifyGuessInterface;

final readonly class ExistingValidityMoveFromGuesser implements ValidityBasedObjectModifyGuesserInterface
{
    public function __construct(
        private DateTimeUtil $dateTimeUtil,
        private ValidityUtil $validityUtil,
    ) {
    }

    public function guess(ValidityBasedObjectModifyInfo $info): ValidityBasedObjectModifyGuessInterface
    {
        $inner = $this->collectInnerIntervals($info);
        return new ValidityBasedObjectModifyGuess(
            $info,
            ValidityBasedObjectModifyTypeEnum::ExistingValidityFromMove,
            $inner
        );
    }

    private function collectInnerIntervals(ValidityBasedObjectModifyInfo $info): array
    {
        $objects = $info->getObjects();
        $stateSet = $info->getStateSet();
        $identifiers = $stateSet->getIdentifiers();
        $validityObjects = $objects->get($identifiers);
        $originalValidFrom = $stateSet->getOriginalValidFrom();
        $originalValidTo = null;
        if ($stateSet->hasOriginalValidTo()) {
            $originalValidTo = clone $stateSet->getOriginalValidTo();
        }
        $changedValidFrom = $stateSet->getValidFrom();
        $changedValidTo = $stateSet->getValidTo();

        $modifiedObjects = [];
        foreach ($validityObjects->getAll() as $validityObject) {
            $validityObjectFrom = $validityObjects->getFrom($validityObject);
            $validityObjectTo = $validityObjects->getTo($validityObject);

            $isOriginalValidity = $this->validityUtil->eqNull($validityObjectFrom, $originalValidFrom);
            $isNotChangeValidTo = $this->validityUtil->eqNull($changedValidTo, $originalValidTo);
            $isReplaced = $this->validityUtil->eqNull($changedValidFrom, $validityObjectFrom) &&
                $this->validityUtil->eqNull($changedValidTo, $validityObjectTo);
            if (
                $isOriginalValidity ||
                $isNotChangeValidTo ||
                $isReplaced
            ) {
                continue;
            }
            $isCorrectValidityObjectFromValue = $this->validityUtil->eqNull(
                $this->dateTimeUtil->beforeDayObject($validityObjectFrom),
                $changedValidTo
            );
            $isNextValidity = $this->validityUtil->ltNull($originalValidTo, $validityObjectFrom);
            if (
                !$isCorrectValidityObjectFromValue &&
                $this->validityUtil->between($changedValidTo, $validityObjectFrom, $validityObjectTo) &&
                !$this->validityUtil->eqNull($changedValidTo, $validityObjectTo) &&
                $isNextValidity
            ) {
                $modifiedObjects[] = $validityObject;
            }

            if ($originalValidTo instanceof \DateTimeInterface) {
                $isRightAfterValidity = $this->validityUtil->eqNull(
                    $this->dateTimeUtil->afterDayObject($originalValidTo),
                    $validityObjectFrom
                );
            } else {
                $isRightAfterValidity = $this->validityUtil->eqNull(
                    $this->dateTimeUtil->afterDayObject($this->dateTimeUtil->getDefaultValidTo()),
                    $validityObjectFrom
                );
            }
            if (
                !$isCorrectValidityObjectFromValue &&
                $isRightAfterValidity &&
                $this->validityUtil->ltNull($changedValidTo, $originalValidTo)
            ) {
                $modifiedObjects[] = $validityObject;
            }
        }

        return $modifiedObjects;
    }

    public function isSupported(ValidityBasedObjectModifyInfo $info): bool
    {
        if ($info->getAction() !== ValidityBasedObjectActionEnum::MODIFY) {
            return false;
        }
        try {
            $modifiedObjects = $this->collectInnerIntervals($info);
        } catch (NotFoundException) {
            return false;
        }

        return count($modifiedObjects) >= 1;
    }
}
