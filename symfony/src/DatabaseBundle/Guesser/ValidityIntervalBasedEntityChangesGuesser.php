<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Guesser;

use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\DatabaseBundle\Builder\ValidityIntervalBasedEntitiesBuilder;
use LoginAutonom\DatabaseBundle\DTO\ObjectsChanges;
use LoginAutonom\DatabaseBundle\DTO\StateSetDTO;
use LoginAutonom\DatabaseBundle\DTO\ValidityIntervalBasedEntities;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\ValidityEmbeddable;
use LoginAutonom\DatabaseBundle\Enum\ValidityBasedEntityActionEnum;
use LoginAutonom\DatabaseBundle\Handler\EntityModifyByStateSetHandler;
use LoginAutonom\DatabaseBundle\Handler\ValidityIntervalEntityHandler;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityBasedObjectsInterface;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoAutoJoinToEmployee;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoAutoJoinToEmployeeContract;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoValidityStamp;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoVisibilityPermissionStamp;
use LoginAutonom\DatabaseBundle\Provider\EntitiesByIdentifiersProvider;

final readonly class ValidityIntervalBasedEntityChangesGuesser
{
    public function __construct(
        private ValidityIntervalBasedEntitiesBuilder $validityIntervalBasedEntitiesBuilder,
        private EntitiesByIdentifiersProvider $entitiesByIdentifiersProvider,
        private ValidityBasedEntityActionGuesser $actionGuesser,
        private ValidityIntervalEntityHandler $validityIntervalEntityHandler,
        private EntityFromValidityIntervalBasedCacheByStateSetGuesser $entityGuesser,
        private EntityModifyByStateSetHandler $entityModifyByStateSetHandler,
    ) {
    }

    public function guess(StateSetDTO $stateSet, ?ValidityBasedObjectsInterface $entitiesObject = null): ObjectsChanges
    {
        $identifiers = $stateSet->getIdentifiers();
        $entities = [];
        $isFinal = false;
        if (isset($entitiesObject) && $entitiesObject->isFinalIdentifiers($identifiers)) {
            $isFinal = true;
        }
        if ($identifiers !== [] && $isFinal === false) {
            $entities = $this->entitiesByIdentifiersProvider->provide(
                $stateSet->getEntityFQCN(),
                $identifiers,
                [
                    new NoVisibilityPermissionStamp('To validity based guessing'),
                    new NoValidityStamp(),
                    new NoAutoJoinToEmployee(),
                    new NoAutoJoinToEmployeeContract()
                ]
            );
        }
        if ($entitiesObject === null) {
            $entitiesObject = $this->validityIntervalBasedEntitiesBuilder->reset()
                ->setEntityClass($stateSet->getEntityFQCN())
                ->build();
        }
        $entitiesObject->addMultiple($entities);

        $action = $stateSet->hasAction() ?
            $stateSet->getAction() :
            $this->actionGuesser->guess($stateSet, $entitiesObject);

        if ($action === ValidityBasedEntityActionEnum::ALREADY_EXISTS) {
            $originalEntity = $this->getOriginalEntity($stateSet, $entitiesObject);
            $objectsChanges = new ObjectsChanges();
            $objectsChanges->setTarget($originalEntity);

            return $objectsChanges;
        }
        if ($action === ValidityBasedEntityActionEnum::NOT_SUPPORTED) {
            return new ObjectsChanges();
        }
        if ($action === ValidityBasedEntityActionEnum::CREATE_NEW) {
            return $this->createNewEntity($stateSet, $entitiesObject);
        }
        if ($action === ValidityBasedEntityActionEnum::INSERT_NEW) {
            return $this->insertNewEntity($stateSet, $entitiesObject);
        }
        if ($action === ValidityBasedEntityActionEnum::UPDATE) {
            return $this->modify($stateSet, $entitiesObject);
        }

        throw new \Exception('@TODO exception'); #@TODO exception
    }

    private function createNewEntity(
        StateSetDTO $stateSet,
        ValidityIntervalBasedEntities $entitiesObject
    ): ObjectsChanges {
        return $this->validityIntervalEntityHandler->create($stateSet, $entitiesObject);
    }

    private function insertNewEntity(
        StateSetDTO $stateSet,
        ValidityBasedObjectsInterface $entitiesObject
    ): ObjectsChanges {
        return $this->validityIntervalEntityHandler->insert($stateSet, $entitiesObject);
    }

    private function modify(
        StateSetDTO $stateSet,
        ValidityBasedObjectsInterface $entitiesObject
    ): ObjectsChanges {
        try {
            $originalEntity = $this->getOriginalEntity($stateSet, $entitiesObject);
        } catch (NotFoundException) {
            return $this->insertNewEntity($stateSet, $entitiesObject);
        }
        $originalClone = clone $originalEntity;
        /** @var ValidityEmbeddable $originalValidity */
        $originalValidity = $originalEntity->getValidity();
        $stateSet->setOriginalValidFrom(
            $originalValidity->getValidFrom()
        );
        $stateSet->setOriginalValidTo(
            $originalValidity->getValidTo()
        );
        $objectsChanges = $this->validityIntervalEntityHandler->update($stateSet, $entitiesObject);

        $isModified = $this->entityModifyByStateSetHandler->handle($originalEntity, $stateSet);
        if ($isModified) {
            $objectsChanges->addChanged($originalEntity);
        }
        $objectsChanges->setTarget($originalEntity);
        $objectsChanges->setOriginal($originalClone);

        return $objectsChanges;
    }

    private function getOriginalEntity(StateSetDTO $stateSet, ValidityBasedObjectsInterface $entitiesObject): object
    {
        return $this->entityGuesser->guess(
            $stateSet,
            $entitiesObject
        );
    }
}
