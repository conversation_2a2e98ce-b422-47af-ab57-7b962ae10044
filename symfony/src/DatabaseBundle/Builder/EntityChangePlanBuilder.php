<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Builder;

use LoginAutonom\DatabaseBundle\DTO\EntityChangePlan;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;
use Symfony\Component\PropertyAccess\Exception\UninitializedPropertyException;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;

#[Autoconfigure(shared: false)]
final class EntityChangePlanBuilder
{
    private object $entity;
    private array $fieldMap;
    private array $fields;

    public function __construct(
        private readonly PropertyAccessorInterface $propertyAccessor,
    ) {
    }

    public function build(): EntityChangePlan
    {
        $removes = [];
        $sets = [];
        foreach ($this->fieldMap as $inputField => $entityField) {
            try {
                $entityValue = $this->propertyAccessor->getValue($this->entity, $entityField);
            } catch (UninitializedPropertyException) {
                $entityValue = null;
            }
            if ($entityValue === null && !array_key_exists($inputField, $this->fields)) {
                continue;
            } elseif ($entityValue !== null && !array_key_exists($inputField, $this->fields)) {
                $removes[] = $entityField;
            } elseif ($entityValue === $this->fields[$inputField]) {
                continue;
            } else {
                $sets[$entityField] = $this->fields[$inputField];
            }
        }

        return new EntityChangePlan(
            $this->entity,
            $this->fieldMap,
            $removes,
            $sets
        );
    }

    public function reset(): self
    {
        unset($this->entity, $this->fieldMap, $this->fields);
        $this->fieldMap = [];
        $this->fields = [];

        return $this;
    }

    public function setEntity(object $entity): self
    {
        $this->entity = $entity;
        return $this;
    }

    public function setFieldMap(array $fieldMap): self
    {
        $this->fieldMap = $fieldMap;
        return $this;
    }

    public function setFields(array $fields): self
    {
        $this->fields = $fields;
        return $this;
    }
}
