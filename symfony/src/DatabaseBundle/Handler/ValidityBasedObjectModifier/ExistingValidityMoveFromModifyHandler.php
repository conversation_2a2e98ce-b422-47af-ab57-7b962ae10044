<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Handler\ValidityBasedObjectModifier;

use LoginAutonom\CoreBundle\Util\DateTimeUtil;
use LoginAutonom\DatabaseBundle\DTO\ObjectsChanges;
use LoginAutonom\DatabaseBundle\Enum\ValidityBasedObjectModifyTypeEnum;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityBasedObjectModifierInterface;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityBasedObjectModifyGuessInterface;

final readonly class ExistingValidityMoveFromModifyHandler implements ValidityBasedObjectModifierInterface
{
    public function __construct(
        private DateTimeUtil $dateTimeUtil,
    ) {
    }

    public function modify(ValidityBasedObjectModifyGuessInterface $guess): ObjectsChanges
    {
        $toModify = $guess->getToModify();
        $identifiers = $guess->getInfo()->getStateSet()->getIdentifiers();
        $objects = $guess->getInfo()->getObjects()->get($identifiers);
        $modifiedFrom = $guess->getInfo()->getStateSet()->getValidFrom();
        $modifiedTo = $guess->getInfo()->getStateSet()->hasValidTo() ?
            $guess->getInfo()->getStateSet()->getValidTo() :
            null;

        $modifiedObjects = [];
        foreach ($toModify as $existingObject) {
            $newFrom = $this->dateTimeUtil->afterDayObject($modifiedTo);
            $objects->setFrom($existingObject, $newFrom);
            $modifiedObjects[spl_object_hash($existingObject)] = $existingObject;
        }
        $modifiedObjects = array_values($modifiedObjects);

        return new ObjectsChanges(
            [],
            $modifiedObjects
        );
    }

    public function isSupported(ValidityBasedObjectModifyGuessInterface $guess): bool
    {
        return $guess->getModifyType() === ValidityBasedObjectModifyTypeEnum::ExistingValidityFromMove;
    }
}
