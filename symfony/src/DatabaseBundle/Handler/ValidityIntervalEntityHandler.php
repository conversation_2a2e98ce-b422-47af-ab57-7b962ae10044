<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Handler;

use LoginAutonom\DatabaseBundle\Builder\EntityBuilder;
use LoginAutonom\DatabaseBundle\DTO\ObjectsChanges;
use LoginAutonom\DatabaseBundle\DTO\StateSetDTO;
use LoginAutonom\DatabaseBundle\DTO\ValidityIntervalBasedEntities;

final readonly class ValidityIntervalEntityHandler
{
    public function __construct(
        private ValidityIntervalBasedEntitiesHandler $validityIntervalBasedEntitiesHandler,
        private EntityBuilder $entityBuilder,
    ) {
    }

    public function create(StateSetDTO $stateSet, ValidityIntervalBasedEntities $entities): ObjectsChanges
    {
        $entity = $this->entityBuilder->reset()
            ->setEntityClass($stateSet->getEntityFQCN())
            ->setValidFrom($stateSet->getValidFrom())
            ->setValidTo($stateSet->hasValidTo() ? $stateSet->getValidTo() : null)
            ->setFields(
                array_merge($stateSet->getFields(), $stateSet->getIdentifiers())
            )
            ->build();

        $objectsChanges = new ObjectsChanges([$entity]);
        $objectsChanges->setTarget($entity);

        return $objectsChanges;
    }

    public function insert(StateSetDTO $stateSet, ValidityIntervalBasedEntities $entities): ObjectsChanges
    {
        $objectChanges = $this->validityIntervalBasedEntitiesHandler->attachNewValidity(
            $stateSet,
            $entities
        );
        return $objectChanges;
    }

    public function update(StateSetDTO $stateSet, ValidityIntervalBasedEntities $entities): ObjectsChanges
    {
        return $this->validityIntervalBasedEntitiesHandler->modifyExistingEntity(
            $stateSet,
            $entities
        );
    }
}
