<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Handler;

use LoginAutonom\CoreBundle\Exception\NotImplementedException;
use LoginAutonom\DatabaseBundle\DTO\ObjectsChanges;
use LoginAutonom\DatabaseBundle\DTO\StateSetDTO;
use LoginAutonom\DatabaseBundle\DTO\StateSetEntityCreationInfo;
use LoginAutonom\DatabaseBundle\DTO\ValidityBasedObjectModifyInfo;
use LoginAutonom\DatabaseBundle\Enum\ValidityBasedObjectActionEnum;
use LoginAutonom\DatabaseBundle\Enum\ValidityBasedObjectModifyTypeEnum;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityBasedObjectModifierInterface;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityBasedObjectModifyGuesserInterface;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityBasedObjectModifyGuessInterface;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityBasedObjectsInterface;
use Symfony\Component\DependencyInjection\Attribute\TaggedIterator;

final readonly class ValidityIntervalBasedEntitiesHandler
{
    public function __construct(
        #[TaggedIterator(tag: ValidityBasedObjectModifyGuesserInterface::TAG)]
        private iterable $guessers,
        #[TaggedIterator(tag: ValidityBasedObjectModifierInterface::TAG)]
        private iterable $handlers,
        private StateSetEntityCreationHandler $stateSetEntityCreationHandler,
    ) {
    }

    public function attachNewValidity(
        StateSetDTO $stateSet,
        ValidityBasedObjectsInterface $objects
    ): ObjectsChanges {
        $validityBasedObjectModifyInfo = new ValidityBasedObjectModifyInfo(
            ValidityBasedObjectActionEnum::CREATE,
            $stateSet,
            $objects
        );
        $guesses = $this->guess($validityBasedObjectModifyInfo);
        $guess = reset($guesses);
        if ($guess->getModifyType() === ValidityBasedObjectModifyTypeEnum::ValidityCheckNoInsert) {
            return new ObjectsChanges();
        }

        $objectChanges = new ObjectsChanges();
        foreach ($guesses as $guess) {
            $objectChanges->mergeFrom($this->handle($guess));
        }

        $entityCreationInfo = new StateSetEntityCreationInfo($guess, $validityBasedObjectModifyInfo, $objectChanges);
        $stateSetCreation = $this->stateSetEntityCreationHandler->handle($entityCreationInfo);
        $objectChanges->mergeFrom($stateSetCreation);

        return $objectChanges;
    }

    public function modifyExistingEntity(
        StateSetDTO $stateSet,
        ValidityBasedObjectsInterface $objects
    ): ObjectsChanges {
        $guesses = $this->guess(
            new ValidityBasedObjectModifyInfo(
                ValidityBasedObjectActionEnum::MODIFY,
                $stateSet,
                $objects
            )
        );
        $objectsChanges = new ObjectsChanges();
        foreach ($guesses as $guess) {
            $objectsChanges->mergeFrom($this->handle($guess));
        }
        return $objectsChanges;
    }

    /**
     * @param ValidityBasedObjectModifyInfo $info
     * @return ValidityBasedObjectModifyGuessInterface[]
     */
    private function guess(ValidityBasedObjectModifyInfo $info): array
    {
        $guesses = [];
        /** @var ValidityBasedObjectModifyGuesserInterface $guesser */
        foreach ($this->guessers as $guesser) {
            if ($guesser->isSupported($info)) {
                $guesses[] = $guesser->guess($info);
            }
        }
        if (count($guesses) > 0) {
            return $guesses;
        }
        throw new NotImplementedException('Validity modify guesser not implemented');
    }


    private function handle(ValidityBasedObjectModifyGuessInterface $guess): ObjectsChanges
    {
        /** @var ValidityBasedObjectModifierInterface $handler */
        foreach ($this->handlers as $handler) {
            if ($handler->isSupported($guess)) {
                return $handler->modify($guess);
            }
        }

        return new ObjectsChanges();
    }
}
