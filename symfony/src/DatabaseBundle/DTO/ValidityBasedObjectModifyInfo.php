<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\DTO;

use LoginAutonom\DatabaseBundle\Enum\ValidityBasedObjectActionEnum;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityBasedObjectsInterface;

final readonly class ValidityBasedObjectModifyInfo
{
    public function __construct(
        private ValidityBasedObjectActionEnum $action,
        private StateSetDTO $stateSet,
        private ValidityBasedObjectsInterface $objects
    ) {
    }

    public function getAction(): ValidityBasedObjectActionEnum
    {
        return $this->action;
    }

    public function hasAction(): bool
    {
        return isset($this->action);
    }

    public function getStateSet(): StateSetDTO
    {
        return $this->stateSet;
    }

    public function hasStateSet(): bool
    {
        return isset($this->stateSet);
    }

    public function getObjects(): ValidityBasedObjectsInterface
    {
        return $this->objects;
    }

    public function hasObjects(): bool
    {
        return isset($this->objects);
    }
}
