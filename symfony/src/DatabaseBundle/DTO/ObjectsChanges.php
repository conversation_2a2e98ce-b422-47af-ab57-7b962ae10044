<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\DTO;

final class ObjectsChanges
{
    private array $new;
    private array $changed;
    private array $deleted;
    private object $target;
    private object $original;
    private array $objectHashes = [];

    public function __construct(array $new = [], array $changed = [], array $deleted = [])
    {
        $this->new = $this->changed = $this->deleted = [];
        $this->addAll($new, $changed, $deleted);
    }

    public function getNew(): array
    {
        return $this->new;
    }

    public function hasNew(): bool
    {
        return !empty($this->new);
    }

    public function addNew(object $new): void
    {
        if (!$this->objectExists($new)) {
            $this->new[] = $new;
            $this->addHash($new);
        }
    }

    public function getChanged(): array
    {
        return $this->changed;
    }

    public function hasChanged(): bool
    {
        return !empty($this->changed);
    }

    public function addChanged(object $changed): void
    {
        if (!$this->objectExists($changed)) {
            $this->changed[] = $changed;
            $this->addHash($changed);
        }
    }

    public function getDeleted(): array
    {
        return $this->deleted;
    }

    public function hasDeleted(): bool
    {
        return !empty($this->deleted);
    }

    public function addDeleted(object $deleted): void
    {
        if (!$this->objectExists($deleted)) {
            $this->deleted[] = $deleted;
            $this->addHash($deleted);
        }
    }

    public function getTarget(): object
    {
        return $this->target;
    }

    public function hasTarget(): bool
    {
        return isset($this->target);
    }

    public function setTarget(object $target): void
    {
        $this->target = $target;
    }

    public function getOriginal(): object
    {
        return $this->original;
    }

    public function hasOriginal(): bool
    {
        return isset($this->original);
    }

    public function setOriginal(object $original): void
    {
        $this->original = $original;
    }

    public function mergeFrom(self $changes): void
    {
        $this->addAll($changes->getNew(), $changes->getChanged(), $changes->getDeleted());
    }

    public function hasAnyChange(): bool
    {
        return $this->hasChanged() || $this->hasNew() || $this->hasDeleted();
    }

    private function addAll(array $new = [], array $changed = [], array $deleted = []): void
    {
        foreach ($new as $newObject) {
            $this->addNew($newObject);
        }
        foreach ($changed as $changedObject) {
            $this->addChanged($changedObject);
        }
        foreach ($deleted as $deletedObject) {
            $this->addDeleted($deletedObject);
        }
    }

    private function addHash(object $object): void
    {
        $this->objectHashes[] = spl_object_hash($object);
    }

    private function objectExists(object $object): bool
    {
        return $this->hashExists(spl_object_hash($object));
    }

    private function hashExists(string $hash): bool
    {
        return in_array($hash, $this->objectHashes, true);
    }
}
