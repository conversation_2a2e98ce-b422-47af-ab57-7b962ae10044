<?php

declare(strict_types=1);

namespace LoginAutonom\Tests\PHPUnit\EtlBundle\Flow\Transformer;

use Flow\ETL\Config;
use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Row\Factory\NativeEntryFactory;
use LoginAutonom\CoreBundle\DTO\SingleValidity;
use LoginAutonom\CoreBundle\DTO\ValidityInterval;
use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\DatabaseBundle\Builder\EntityChangePlanBuilder;
use LoginAutonom\DatabaseBundle\DTO\EntityChangePlan;
use LoginAutonom\DatabaseBundle\DTO\ObjectsChanges;
use LoginAutonom\DatabaseBundle\Provider\MultiEntitiesByIdentifiersProvider;
use LoginAutonom\EtlBundle\DTO\EtlEntityModified;
use LoginAutonom\EtlBundle\DTO\EtlEntityModifyInfo;
use LoginAutonom\EtlBundle\DTO\EtlValidityBasedEntityCacheWithMapping;
use LoginAutonom\EtlBundle\Flow\Transformer\UpdateValidityBasedEntityTransformer;
use LoginAutonom\EtlBundle\Interfaces\EtlCacheInterface;
use LoginAutonom\EtlBundle\Interfaces\EtlEntityModifyHandlerInterface;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;

final class UpdateValidityBasedEntityTransformerTest extends TestCase
{
    private FlowContext $context;
    private NativeEntryFactory $entryFactory;
    private MockObject|EntityChangePlanBuilder $changePlanBuilder;
    private MockObject|EtlEntityModifyHandlerInterface $modifyHandler;
    private MockObject|MultiEntitiesByIdentifiersProvider $entitiesByIdentifiersProvider;
    private MockObject|EtlCacheInterface $cache;
    private MockObject|LoggerInterface $logger;
    private MockObject|EtlValidityBasedEntityCacheWithMapping $entityCache;

    protected function setUp(): void
    {
        $this->context = new FlowContext(Config::default());
        $this->entryFactory = new NativeEntryFactory();
        $this->changePlanBuilder = $this->createMock(EntityChangePlanBuilder::class);
        $this->modifyHandler = $this->createMock(EtlEntityModifyHandlerInterface::class);
        $this->entitiesByIdentifiersProvider = $this->createMock(MultiEntitiesByIdentifiersProvider::class);
        $this->cache = $this->createMock(EtlCacheInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);
        $this->entityCache = $this->createMock(EtlValidityBasedEntityCacheWithMapping::class);
    }

    public function testTransformWithSingleValidityUpdatesEntity(): void
    {
        $fieldMap = ['name' => 'entityName', 'value' => 'entityValue'];
        $fromField = 'validFrom';
        $identifierFields = ['id' => 'entityId'];
        $entityClass = 'TestEntity';

        $transformer = new UpdateValidityBasedEntityTransformer(
            $fieldMap,
            $fromField,
            $identifierFields,
            $entityClass,
            $this->changePlanBuilder,
            $this->modifyHandler,
            $this->entitiesByIdentifiersProvider
        );
        $transformer->setCache($this->cache);
        $transformer->setLogger($this->logger);

        $row = Row::create(
            $this->entryFactory->create('entityId', 'test-id'),
            $this->entryFactory->create('name', 'Test Name'),
            $this->entryFactory->create('value', 'Test Value'),
            $this->entryFactory->create('validFrom', new \DateTimeImmutable('2024-01-01'))
        );

        $mockEntity = new \stdClass();
        $mockEntity->id = 'test-id';

        $mockChangePlan = $this->createMock(EntityChangePlan::class);
        $mockChangePlan->expects($this->once())
            ->method('hasChanges')
            ->willReturn(true);

        $mockObjectsChanges = new ObjectsChanges();
        $mockObjectsChanges->addChanged($mockEntity);

        $mockEntityModified = new EtlEntityModified($mockEntity, $mockObjectsChanges);

        $this->setupCacheMocks();
        $this->setupEntityCacheMocks([$mockEntity]);
        $this->setupChangePlanBuilderMocks($mockChangePlan, $mockEntity);
        $this->setupModifyHandlerMocks($mockEntityModified);

        $result = $transformer->transform($row, $this->context);

        $this->assertSame($row, $result);
    }

    public function testTransformWithValidityIntervalUpdatesEntity(): void
    {
        $fieldMap = ['name' => 'entityName'];
        $fromField = 'validFrom';
        $toField = 'validTo';
        $identifierFields = ['id' => 'entityId'];
        $entityClass = 'TestEntity';

        $transformer = new UpdateValidityBasedEntityTransformer(
            $fieldMap,
            $fromField,
            $identifierFields,
            $entityClass,
            $this->changePlanBuilder,
            $this->modifyHandler,
            $this->entitiesByIdentifiersProvider,
            $toField
        );
        $transformer->setCache($this->cache);
        $transformer->setLogger($this->logger);

        $row = Row::create(
            $this->entryFactory->create('entityId', 'test-id'),
            $this->entryFactory->create('name', 'Test Name'),
            $this->entryFactory->create('validFrom', new \DateTimeImmutable('2024-01-01')),
            $this->entryFactory->create('validTo', new \DateTimeImmutable('2024-12-31'))
        );

        $mockEntity = new \stdClass();
        $mockEntity->id = 'test-id';

        $mockChangePlan = $this->createMock(EntityChangePlan::class);
        $mockChangePlan->expects($this->once())
            ->method('hasChanges')
            ->willReturn(true);

        $mockObjectsChanges = new ObjectsChanges();
        $mockEntityModified = new EtlEntityModified($mockEntity, $mockObjectsChanges);

        $this->setupCacheMocks();
        $this->setupEntityCacheMocks([$mockEntity]);
        $this->setupChangePlanBuilderMocks($mockChangePlan, $mockEntity);
        $this->setupModifyHandlerMocks($mockEntityModified);

        $result = $transformer->transform($row, $this->context);

        $this->assertSame($row, $result);
    }

    public function testTransformSkipsWhenNoEntitiesFound(): void
    {
        $fieldMap = ['name' => 'entityName'];
        $fromField = 'validFrom';
        $identifierFields = ['id' => 'entityId'];
        $entityClass = 'TestEntity';

        $transformer = new UpdateValidityBasedEntityTransformer(
            $fieldMap,
            $fromField,
            $identifierFields,
            $entityClass,
            $this->changePlanBuilder,
            $this->modifyHandler,
            $this->entitiesByIdentifiersProvider
        );
        $transformer->setCache($this->cache);
        $transformer->setLogger($this->logger);

        $row = Row::create(
            $this->entryFactory->create('entityId', 'test-id'),
            $this->entryFactory->create('name', 'Test Name'),
            $this->entryFactory->create('validFrom', new \DateTimeImmutable('2024-01-01'))
        );

        $this->setupCacheMocks();
        $this->setupEntityCacheMocks([]);

        $this->logger->expects($this->once())
            ->method('debug')
            ->with('No entities found in validity period, skipping update');

        $result = $transformer->transform($row, $this->context);

        $this->assertSame($row, $result);
    }

    public function testTransformSkipsWhenNoChangesDetected(): void
    {
        $fieldMap = ['name' => 'entityName'];
        $fromField = 'validFrom';
        $identifierFields = ['id' => 'entityId'];
        $entityClass = 'TestEntity';

        $transformer = new UpdateValidityBasedEntityTransformer(
            $fieldMap,
            $fromField,
            $identifierFields,
            $entityClass,
            $this->changePlanBuilder,
            $this->modifyHandler,
            $this->entitiesByIdentifiersProvider
        );
        $transformer->setCache($this->cache);
        $transformer->setLogger($this->logger);

        $row = Row::create(
            $this->entryFactory->create('entityId', 'test-id'),
            $this->entryFactory->create('name', 'Test Name'),
            $this->entryFactory->create('validFrom', new \DateTimeImmutable('2024-01-01'))
        );

        $mockEntity = new \stdClass();
        $mockChangePlan = $this->createMock(EntityChangePlan::class);
        $mockChangePlan->expects($this->once())
            ->method('hasChanges')
            ->willReturn(false);

        $this->setupCacheMocks();
        $this->setupEntityCacheMocks([$mockEntity]);
        $this->setupChangePlanBuilderMocks($mockChangePlan, $mockEntity);

        $this->logger->expects($this->once())
            ->method('debug')
            ->with('No changes detected for entity, skipping');

        $this->modifyHandler->expects($this->never())
            ->method('modify');

        $result = $transformer->transform($row, $this->context);

        $this->assertSame($row, $result);
    }

    public function testTransformThrowsExceptionWhenIdentifierFieldMissing(): void
    {
        $fieldMap = ['name' => 'entityName'];
        $fromField = 'validFrom';
        $identifierFields = ['id' => 'missingField'];
        $entityClass = 'TestEntity';

        $transformer = new UpdateValidityBasedEntityTransformer(
            $fieldMap,
            $fromField,
            $identifierFields,
            $entityClass,
            $this->changePlanBuilder,
            $this->modifyHandler,
            $this->entitiesByIdentifiersProvider
        );
        $transformer->setCache($this->cache);
        $transformer->setLogger($this->logger);

        $row = Row::create(
            $this->entryFactory->create('entityId', 'test-id'),
            $this->entryFactory->create('validFrom', new \DateTimeImmutable('2024-01-01'))
        );

        $this->setupCacheMocks();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Identifier field not found: id -> missingField');

        $transformer->transform($row, $this->context);
    }

    private function setupCacheMocks(): void
    {
        $flowCache = $this->createMock(EtlCacheInterface::class);
        $flowCache->expects($this->once())
            ->method('get')
            ->willReturn($this->entityCache);

        $this->cache->expects($this->once())
            ->method('flow')
            ->willReturn($flowCache);
    }

    private function setupEntityCacheMocks(array $entities): void
    {
        if (empty($entities)) {
            $this->entityCache->expects($this->once())
                ->method('get')
                ->willThrowException(new NotFoundException('Entity not found'));

            $this->entitiesByIdentifiersProvider->expects($this->once())
                ->method('provide')
                ->willReturn([]);
        } else {
            $mockValidityBasedEntity = $this->createMock(\stdClass::class);
            $mockValidityBasedEntity->expects($this->once())
                ->method('getByDay')
                ->willReturn($entities);

            $this->entityCache->expects($this->once())
                ->method('get')
                ->willReturn($mockValidityBasedEntity);
        }
    }

    private function setupChangePlanBuilderMocks(EntityChangePlan $changePlan, object $entity): void
    {
        $this->changePlanBuilder->expects($this->once())
            ->method('reset')
            ->willReturnSelf();

        $this->changePlanBuilder->expects($this->once())
            ->method('setEntity')
            ->with($entity)
            ->willReturnSelf();

        $this->changePlanBuilder->expects($this->once())
            ->method('setFieldMap')
            ->willReturnSelf();

        $this->changePlanBuilder->expects($this->once())
            ->method('setFields')
            ->willReturnSelf();

        $this->changePlanBuilder->expects($this->once())
            ->method('build')
            ->willReturn($changePlan);
    }

    private function setupModifyHandlerMocks(EtlEntityModified $entityModified): void
    {
        $this->modifyHandler->expects($this->once())
            ->method('modify')
            ->with($this->isInstanceOf(EtlEntityModifyInfo::class))
            ->willReturn($entityModified);
    }
}
