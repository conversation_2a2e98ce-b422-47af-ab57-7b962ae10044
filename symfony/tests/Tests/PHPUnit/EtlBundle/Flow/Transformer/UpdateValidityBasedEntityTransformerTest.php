<?php

declare(strict_types=1);

namespace LoginAutonom\Tests\PHPUnit\EtlBundle\Flow\Transformer;

use DG\BypassFinals;
use Flow\ETL\Config;
use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Row\Factory\NativeEntryFactory;
use LoginAutonom\DatabaseBundle\Builder\EntityChangePlanBuilder;
use LoginAutonom\DatabaseBundle\Provider\MultiEntitiesByIdentifiersProvider;
use LoginAutonom\EtlBundle\Flow\Transformer\UpdateValidityBasedEntityTransformer;
use LoginAutonom\EtlBundle\Interfaces\EtlEntityModifyHandlerInterface;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Symfony\Component\PropertyAccess\PropertyAccessor;

BypassFinals::enable();

final class UpdateValidityBasedEntityTransformerTest extends TestCase
{
    private function createTransformerWithLogger(
        array $fieldMap,
        string $fromField,
        array $identifierFields,
        string $entityClass,
        ?string $toField = null
    ): UpdateValidityBasedEntityTransformer {
        $changePlanBuilder = new EntityChangePlanBuilder(new PropertyAccessor());
        $modifyHandler = $this->createMock(EtlEntityModifyHandlerInterface::class);
        $entitiesByIdentifiersProvider = $this->createMock(MultiEntitiesByIdentifiersProvider::class);

        $transformer = new UpdateValidityBasedEntityTransformer(
            $fieldMap,
            $fromField,
            $identifierFields,
            $entityClass,
            $changePlanBuilder,
            $modifyHandler,
            $entitiesByIdentifiersProvider,
            $toField
        );

        $logger = $this->createMock(LoggerInterface::class);
        $transformer->setLogger($logger);

        return $transformer;
    }
    public function testTransformReturnsRowUnchanged(): void
    {
        $transformer = $this->createTransformerWithLogger(
            ['name' => 'entityName'],
            'validFrom',
            ['id' => 'entityId'],
            'TestEntity'
        );

        $context = new FlowContext(Config::default());
        $entryFactory = new NativeEntryFactory();

        $row = Row::create(
            $entryFactory->create('entityId', 'test-id'),
            $entryFactory->create('name', 'Test Name'),
            $entryFactory->create('validFrom', new \DateTimeImmutable('2024-01-01'))
        );

        $result = $transformer->transform($row, $context);

        $this->assertSame($row, $result);
    }

    public function testTransformWithMissingIdentifierFieldThrowsException(): void
    {
        $transformer = $this->createTransformerWithLogger(
            ['name' => 'entityName'],
            'validFrom',
            ['id' => 'missingField'],
            'TestEntity'
        );

        $context = new FlowContext(Config::default());
        $entryFactory = new NativeEntryFactory();

        $row = Row::create(
            $entryFactory->create('entityId', 'test-id'),
            $entryFactory->create('validFrom', new \DateTimeImmutable('2024-01-01'))
        );

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Identifier field not found: id -> missingField');

        $transformer->transform($row, $context);
    }

    public function testTransformWithValidityInterval(): void
    {
        $transformer = $this->createTransformerWithLogger(
            ['name' => 'entityName'],
            'validFrom',
            ['id' => 'entityId'],
            'TestEntity',
            'validTo'
        );

        $context = new FlowContext(Config::default());
        $entryFactory = new NativeEntryFactory();

        $row = Row::create(
            $entryFactory->create('entityId', 'test-id'),
            $entryFactory->create('name', 'Test Name'),
            $entryFactory->create('validFrom', new \DateTimeImmutable('2024-01-01')),
            $entryFactory->create('validTo', new \DateTimeImmutable('2024-12-31'))
        );

        $result = $transformer->transform($row, $context);

        $this->assertSame($row, $result);
    }

    public function testTransformWithEmptyFieldMap(): void
    {
        $fieldMap = [];
        $fromField = 'validFrom';
        $identifierFields = ['id' => 'entityId'];
        $entityClass = 'TestEntity';
        
        $changePlanBuilder = new EntityChangePlanBuilder(new PropertyAccessor());
        $modifyHandler = $this->createMock(EtlEntityModifyHandlerInterface::class);
        $entitiesByIdentifiersProvider = $this->createMock(MultiEntitiesByIdentifiersProvider::class);

        $transformer = new UpdateValidityBasedEntityTransformer(
            $fieldMap,
            $fromField,
            $identifierFields,
            $entityClass,
            $changePlanBuilder,
            $modifyHandler,
            $entitiesByIdentifiersProvider
        );

        $context = new FlowContext(Config::default());
        $entryFactory = new NativeEntryFactory();
        
        $row = Row::create(
            $entryFactory->create('entityId', 'test-id'),
            $entryFactory->create('validFrom', new \DateTimeImmutable('2024-01-01'))
        );

        $result = $transformer->transform($row, $context);

        $this->assertSame($row, $result);
    }

    public function testTransformWithMultipleIdentifierFields(): void
    {
        $fieldMap = ['name' => 'entityName'];
        $fromField = 'validFrom';
        $identifierFields = ['id' => 'entityId', 'type' => 'entityType'];
        $entityClass = 'TestEntity';
        
        $changePlanBuilder = new EntityChangePlanBuilder(new PropertyAccessor());
        $modifyHandler = $this->createMock(EtlEntityModifyHandlerInterface::class);
        $entitiesByIdentifiersProvider = $this->createMock(MultiEntitiesByIdentifiersProvider::class);

        $transformer = new UpdateValidityBasedEntityTransformer(
            $fieldMap,
            $fromField,
            $identifierFields,
            $entityClass,
            $changePlanBuilder,
            $modifyHandler,
            $entitiesByIdentifiersProvider
        );

        $context = new FlowContext(Config::default());
        $entryFactory = new NativeEntryFactory();
        
        $row = Row::create(
            $entryFactory->create('entityId', 'test-id'),
            $entryFactory->create('entityType', 'test-type'),
            $entryFactory->create('name', 'Test Name'),
            $entryFactory->create('validFrom', new \DateTimeImmutable('2024-01-01'))
        );

        $result = $transformer->transform($row, $context);

        $this->assertSame($row, $result);
    }

    public function testTransformWithMissingFromFieldThrowsException(): void
    {
        $fieldMap = ['name' => 'entityName'];
        $fromField = 'missingFromField';
        $identifierFields = ['id' => 'entityId'];
        $entityClass = 'TestEntity';
        
        $changePlanBuilder = new EntityChangePlanBuilder(new PropertyAccessor());
        $modifyHandler = $this->createMock(EtlEntityModifyHandlerInterface::class);
        $entitiesByIdentifiersProvider = $this->createMock(MultiEntitiesByIdentifiersProvider::class);

        $transformer = new UpdateValidityBasedEntityTransformer(
            $fieldMap,
            $fromField,
            $identifierFields,
            $entityClass,
            $changePlanBuilder,
            $modifyHandler,
            $entitiesByIdentifiersProvider
        );

        $context = new FlowContext(Config::default());
        $entryFactory = new NativeEntryFactory();
        
        $row = Row::create(
            $entryFactory->create('entityId', 'test-id'),
            $entryFactory->create('name', 'Test Name'),
            $entryFactory->create('validFrom', new \DateTimeImmutable('2024-01-01'))
        );

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('fromField not exists');

        $transformer->transform($row, $context);
    }
}
